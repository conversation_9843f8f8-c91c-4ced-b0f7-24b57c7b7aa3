{"permissions": {"allow": ["Bash(pip3 --version)", "Bash(pip3 check)", "<PERSON><PERSON>(pip3 list)", "Bash(node --version)", "Bash(npm --version)", "Bash(rm /Users/<USER>/PycharmProjects/PythonProject/backend/pytest.ini)", "Bash(rm /Users/<USER>/PycharmProjects/PythonProject/tests/pytest.ini)", "Bash(python3 -c \"import sqlite3; db = sqlite3.connect(''/Users/<USER>/PycharmProjects/PythonProject/backend/data/databases/trading_system_2.db''); cursor = db.cursor(); cursor.execute(''SELECT name FROM sqlite_master WHERE type=\"\"table\"\";''); print(''Tables:'', cursor.fetchall()); db.close()\")", "Bash(python3 -c \"import yaml; print(''YAML config is valid'')\")", "Bash(python3 -m pytest --version)", "Bash(python3 -c \"import pytest; print(''✅ pytest配置正常'')\")", "Bash(grep -n \"3000\" /Users/<USER>/PycharmProjects/PythonProject/run.py)", "Bash(grep -n \"3000\" /Users/<USER>/PycharmProjects/PythonProject/frontend/package.json)", "Bash(grep -n \"3000\" /Users/<USER>/PycharmProjects/PythonProject/frontend/vite.config.ts)", "Bash(python3 -c \"import sys; print(''Python version:'', sys.version); import importlib; modules = [''fastapi'', ''uvicorn'', ''sqlalchemy'', ''pydantic'', ''pandas'', ''numpy'']; [print(f''{m}: ✅'') if importlib.util.find_spec(m) else print(f''{m}: ❌'') for m in modules]\")", "Bash(python3 -c \"import main; print(''✅ main.py导入成功'')\")", "Bash(python3 -c \"\nimport sys\nprint(''Python version:'', sys.version)\nmodules = [''fastapi'', ''uvicorn'', ''sqlalchemy'', ''pydantic'', ''pandas'', ''numpy'']\nfor m in modules:\n    try:\n        __import__(m)\n        print(f''{m}: ✅'')\n    except ImportError as e:\n        print(f''{m}: ❌ ({e})'')\n\")", "Bash(python3 -c \"\nimport sys\nimport os\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.core.config import settings\n    print(''✅ config导入成功'')\nexcept Exception as e:\n    print(f''❌ config导入失败: {e}'')\n\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\ntry:\n    from app.core.websocket import websocket_manager\n    print(''✅ websocket导入成功'')\nexcept Exception as e:\n    print(f''❌ websocket导入失败: {e}'')\n\")", "Bash(python3 -c \"\ntry:\n    from app.api.endpoints import market_data\n    print(''✅ market_data导入成功'')\nexcept Exception as e:\n    print(f''❌ market_data导入失败: {e}'')\n\ntry:\n    from app.api.endpoints import strategies\n    print(''✅ strategies导入成功'')\nexcept Exception as e:\n    print(f''❌ strategies导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.endpoints import backtest\n    print(''✅ backtest导入成功'')\nexcept Exception as e:\n    print(f''❌ backtest导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/src -name \"*.py\" -exec grep -l \"from.*OHLCV\" {} ;)", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/src -name \"*.py\" -exec grep -l \"import.*OHLCV\" {} ;)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from src.backtest import engine\n    print(''✅ backtest engine导入成功'')\nexcept Exception as e:\n    print(f''❌ backtest engine导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nprint(''Python path before:'', sys.path[:3])\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\nprint(''Python path after:'', sys.path[:3])\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.market_data import OHLCV\n    print(''✅ OHLCV导入成功'')\nexcept Exception as e:\n    print(f''❌ OHLCV导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''/Users/<USER>/PycharmProjects/PythonProject'')\ntry:\n    from src.domain.models.trading import Trade, Order, OrderFill, Signal\n    print(''✅ trading模块导入成功'')\nexcept Exception as e:\n    print(f''❌ trading模块导入失败: {e}'')\n\ntry:\n    from src.domain.models.portfolio import Asset, Portfolio, Position\n    print(''✅ portfolio模块导入成功'')\nexcept Exception as e:\n    print(f''❌ portfolio模块导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/src -name \"*.py\" -exec grep -l \"__future__\" {} ;)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ routes导入成功'')\nexcept Exception as e:\n    print(f''❌ routes导入失败: {e}'')\n\")", "Bash(npm run type-check)", "Bash(python3 -c \"import pytest; print(''pytest available'')\")", "Bash(sed -n '578,582p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/common/SearchAnalytics.tsx)", "Bash(sed -i '' '580s/^ /return/  return/' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/common/SearchAnalytics.tsx)", "Bash(python3 -c \"\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/common/SearchAnalytics.tsx'', ''r'') as f:\n    lines = f.readlines()\n\n# 修复第580行\nfor i, line in enumerate(lines):\n    if i == 579:  # 第580行（0-based索引）\n        lines[i] = ''  return colorMap?.[market] || \\''default\\'';\\n''\n        break\n\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/common/SearchAnalytics.tsx'', ''w'') as f:\n    f.writelines(lines)\n\")", "Bash(sed -n '428,435p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(sed -n '370p;384p;393p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts)", "Bash(sed -i '' '370s/export function getInitializationTasks(): InitializationTask[] = {/export function getInitializationTasks(): InitializationTask[] = {/' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts)", "Bash(python3 -c \"\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts'', ''r'') as f:\n    lines = f.readlines()\n\n# 修复函数声明\nfor i, line in enumerate(lines):\n    if ''export function getInitializationTasks(): InitializationTask[] = {'' in line:\n        lines[i] = line  # 保持原样\n    elif ''export function getCriticalTasks(): string[] = {'' in line:\n        lines[i] = line  # 保持原样\n    elif ''export function getPreloadTasks(): string[] = {'' in line:\n        lines[i] = line  # 保持原样\n\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts'', ''w'') as f:\n    f.writelines(lines)\n\")", "Bash(sed -i '' '381,387c\\\nexport function getCriticalTasks(): string[] = {\\\n  return APPINITIALIZATIONTASKS\\\n    .filter(task => task.essential)\\\n    .map(task => task.id);\\\n}\\\n' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts)", "Bash(sed -i '' '392,397c\\\nexport function getPreloadTasks(): string[] = {\\\n  return APPINITIALIZATIONTASKS\\\n    .filter(task => task.preload)\\\n    .map(task => task.id);\\\n}\\\n' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/config/appInitialization.ts)", "Bash(sed -i '' 's/getDependentKeys(/getDependentKeys(/g' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/services/chartDataCache.ts)", "Bash(sed -i '' 's/invalidateDependents(/invalidateDependents(/g' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/services/chartDataCache.ts)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\nsys.path.insert(0, ''..'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from src.core.monitoring.audit_logger import AuditEvent\n    print(''✅ AuditEvent导入成功'')\nexcept Exception as e:\n    print(f''❌ AuditEvent导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n\")", "<PERSON>sh(find . -name \"*.py\" -exec grep -l \"from.*AuditEvent\" {} ;)", "Bash(find . -name \"*.py\" -path \"*/endpoints/*\" -exec grep -l \"AuditEvent\" {} ;)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(grep -n \"class AuditEvent:\" /Users/<USER>/PycharmProjects/PythonProject/src/core/monitoring/audit_logger.py)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ API路由导入成功'')\nexcept Exception as e:\n    print(f''❌ API路由导入失败: {e}'')\n\")", "Bash(grep -n \"AuditEvent\" /Users/<USER>/PycharmProjects/PythonProject/src/core/monitoring/audit_logger.py)", "Bash(sed -i '' \"s/AuditEvent/''AuditEvent''/g\" /Users/<USER>/PycharmProjects/PythonProject/src/core/monitoring/audit_logger.py)", "Bash(python3 -c \"\n# 修复audit_logger.py的关键前向引用问题\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/src/core/monitoring/audit_logger.py'', ''r'') as f:\n    content = f.read()\n\n# 修复所有AuditEventType为正确的类名\ncontent = content.replace(''class \\''AuditEventType\\''(Enum):'', ''class AuditEventType(Enum):'')\n\n# 修复所有''AuditEvent''的类型注释（非类定义）\ncontent = content.replace(''event: \\''AuditEvent\\''\\''\\'''', ''event: \\''AuditEvent\\''\\''\\'''''')\n\n# 修复List[''AuditEvent'']为List[''AuditEvent'']\ncontent = content.replace(''List[\\''AuditEvent\\'']\\''\\'''', ''List[\\''AuditEvent\\'']\\''\\'''')\n\n# 修复__all__\ncontent = content.replace(''\\''\\''AuditEvent\\''\\'',\\''\\'''', ''\\''\\''AuditEvent\\''\\'''')\n\nwith open(''/Users/<USER>/PycharmProjects/PythonProject/src/core/monitoring/audit_logger.py'', ''w'') as f:\n    f.write(content)\n\")", "Bash(sed -n '425,440p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(sed -n '422,450p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(sed -n '435,445p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(sed -n '435p' /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(grep -n \"import.*message\" /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(grep -n \"success\\|error\\|notification\" /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/error/ErrorNotificationProvider.tsx)", "Bash(grep -n \"useState\\|useCallback\" /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/error/ErrorNotificationProvider.tsx)", "Bash(grep -n \"export.*function\\|const.*=\" /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(grep -n \"const BacktestMonitor\\|export.*BacktestMonitor\\|return (\" /Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/backtest/BacktestMonitor_fixed.tsx)", "Bash(npm run build)", "Bash(npm install)", "Bash(rm -rf node_modules package-lock.json)", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ 后端API路由导入成功'')\n    print(''API路由对象:'', type(api_router))\nexcept Exception as e:\n    print(f''❌ 后端API路由导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ 后端API路由导入成功'')\n    print(''API路由对象:'', type(api_router))\n    print(''路由前缀:'', api_router.prefix)\nexcept Exception as e:\n    print(f''❌ 后端API路由导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(python3 -c \"\nimport sys\nsys.path.insert(0, ''.'')\ntry:\n    from app.api.routes import api_router\n    print(''✅ 后端API路由导入成功'')\n    print(''路由对象类型:'', type(api_router))\n    print(''路由前缀:'', api_router.prefix if api_router else ''No prefix'')\nexcept Exception as e:\n    print(f''❌ 后端API路由导入失败: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "<PERSON><PERSON>(python run.py:*)", "<PERSON><PERSON>(python -c:*)", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "Bash(mkdir -p /Users/<USER>/PycharmProjects/PythonProject/temp_reports)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/DUPLICATE_CODE_*.md /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/REFACTORING_*.md /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/auto_refactoring_plan.md /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/duplicate_code_*.json /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/refactoring_*.json /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/test_refactoring_plan.json /Users/<USER>/PycharmProjects/PythonProject/temp_reports/)", "Bash(mkdir -p /Users/<USER>/PycharmProjects/PythonProject/data/databases/archive)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/data/databases/risk_monitoring_1.db /Users/<USER>/PycharmProjects/PythonProject/data/databases/archive/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/data/databases/trading_system_1.db-shm /Users/<USER>/PycharmProjects/PythonProject/data/databases/archive/)", "<PERSON><PERSON>(true)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/data/databases/trading_system_1.db-wal /Users/<USER>/PycharmProjects/PythonProject/data/databases/archive/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/requirements/requirements_original.txt /Users/<USER>/PycharmProjects/PythonProject/requirements/archive/)", "Bash(mv /Users/<USER>/PycharmProjects/PythonProject/requirements/requirements-fixed.txt /Users/<USER>/PycharmProjects/PythonProject/requirements/archive/)", "Bash(mkdir -p /Users/<USER>/PycharmProjects/PythonProject/requirements/archive)", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/logs/maintenance -name \"*.json\" -mtime +7 -exec rm {} ;)", "Bash(mkdir -p /Users/<USER>/PycharmProjects/PythonProject/reports/archive)", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/reports/code_quality -name \"*.json\" -mtime +7 -exec mv {} /Users/<USER>/PycharmProjects/PythonProject/reports/archive/ ;)", "Bash(find /Users/<USER>/PycharmProjects/PythonProject/reports/code_quality -name \"*.md\" -mtime +7 -exec mv {} /Users/<USER>/PycharmProjects/PythonProject/reports/archive/ ;)", "Bash(python /Users/<USER>/PycharmProjects/PythonProject/scripts/optimize_project_structure.py)", "<PERSON><PERSON>(python3:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "mcp__ide__getDiagnostics", "Bash(npm audit:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(npx tsc:*)", "Bash(npm update:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(timeout:*)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/intelligent_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/quick_fix_init.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/fix_syntax_errors.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/python_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/comprehensive_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/fix_imports.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/fix_documentation_syntax.py)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/typescript_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/fix_typescript_syntax.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/fix_backtest_wizard.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/advanced_typescript_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/simple_ts_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/fix_import_syntax.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/fix_specific_imports.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/comprehensive_syntax_fixer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/frontend_quality_optimizer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend/fix_syntax_batch.py)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/database_optimizer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/deep_code_analyzer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/performance_optimizer.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/security_auditor.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/frontend_ux_optimizer.py)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/api_documentation_generator.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/backend/code_quality_optimizer.py)", "Bash(analyze_refactoring_impact.py )", "Bash(batch_refactor_tests.py )", "Bash(cleanup_duplicate_code.py )", "Bash(cleanup_duplicate_docs.py )", "Bash(cleanup_todo_comments.py )", "Bash(cleanup_unused_files.py )", "Bash(comprehensive_syntax_fix.py )", "Bash(create_refactoring_base.py )", "Bash(final_typescript_fix.py )", "Bash(fix_api_routes.py )", "Bash(fix_import_errors.py )", "Bash(fix_syntax_errors.py )", "Bash(fix_long_lines.py )", "<PERSON>sh(intelligent_code_refactorer.py )", "Bash(quick_fix_issues.py )", "Bash(refactor_duplicate_tests.py )", "Bash(refactor_example.py )", "Bash(refactor_tools_duplicates.py )", "Bash(refactoring_progress_tracker.py )", "Bash(smart_code_duplicate_detector.py )", "Bash(typescript_error_fixer.py)", "Bash(cleanup_redundant_files.py )", "Bash(handle_duplicate_scripts.py )", "Bash(project_reorganizer.py )", "Bash(project_structure_analyzer.py )", "Bash(project_structure_template.py )", "Bash(project_structure_validator.py)", "Bash(CLEAN_ROOT_README.md )", "Bash(PROJECT_CLEANUP_FINAL_REPORT.md )", "Bash(PROJECT_OPTIMIZATION_REPORT.md )", "Ba<PERSON>(修复完成报告.md )", "Bash(项目问题检查报告.md)", "Bash(api_fixer.py )", "<PERSON><PERSON>(backend_fix_manager.py )", "<PERSON><PERSON>(database_fixer.py )", "Bash(dependency_resolver.py )", "<PERSON><PERSON>(problem_detector.py )", "Bash(report_generator.py)", "Bash(api_fixer.py )", "<PERSON><PERSON>(backend_fix_manager.py )", "<PERSON><PERSON>(cache_manager.py )", "<PERSON><PERSON>(database_fixer.py )", "<PERSON><PERSON>(problem_detector.py )", "<PERSON><PERSON>(service_manager.py)", "Bash(service_bus_legacy.py )", "Bash(execution_algorithms_refactored.py )", "Bash(service_bus_refactored.py)", "Bash(api_template.yaml )", "Bash(database_template.yaml )", "Bash(datasources_template.yaml )", "Bash(environment_template.yaml )", "Bash(logging_template.yaml )", "Bash(security_template.yaml )", "Bash(system_template.yaml)", "Bash(cleanup_config.py )", "Bash(reorganize_config.py )", "Bash(validate_config.py)", "Bash(reorganize_docs.py )", "Bash(CONSOLIDATED_PROJECT_REPORT.md )", "Bash(README_CODE_QUALITY_TOOLS.md )", "Bash(code_quality_quick_start.md )", "Bash(code_quality_tools_complete_guide.md)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/docs/examples/demo_docs )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/docs/examples/samples)", "Bash(CLEANUP_REDUNDANT_FILES_REPORT.md )", "Bash(DUPLICATE_SCRIPTS_REPORT.md )", "Bash(PROJECT_ISSUES_ANALYSIS_REPORT.md )", "Bash(PROJECT_ISSUES_FIXED_SUMMARY.md )", "Bash(UI_CODE_CONNECTION_FIXED.md )", "Bash(final_problems_resolution_report.md )", "Bash(phase1_improvement_final_summary.md )", "Bash(phase1_improvement_progress.md )", "Bash(phase2_improvement_progress.md )", "Bash(problems_resolution_report.md)", "Bash(DataSyncOptimizer.ts )", "Bash(EnhancedConnectionManager.ts )", "<PERSON><PERSON>(FrontendFixManager.ts )", "Bash(README_FrontendFixSystem.md )", "<PERSON><PERSON>(errorLogger.ts )", "<PERSON><PERSON>(errorMonitoring.ts )", "Ba<PERSON>(errorRecoveryManager.ts )", "Bash(unifiedWebSocketManager.ts)", "<PERSON><PERSON>(ConfigAutoFixer.ts )", "<PERSON><PERSON>(ConfigurationValidator.ts )", "Bash(ConnectionDiagnosticTool.ts )", "<PERSON><PERSON>(DiagnosticHistoryManager.ts )", "Bash(DiagnosticReportGenerator.ts )", "<PERSON><PERSON>(SystemDiagnostics.ts )", "Bash(WebSocketConfigValidator.ts )", "<PERSON><PERSON>(diagnosticTest.ts )", "<PERSON><PERSON>(loadingDiagnostics.ts)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/analysis )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/application )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/diagnostic)", "Bash(configuration_standardizer.py )", "Bash(doc_generator.py )", "Bash(doc_validator.py )", "Bash(maintenance_guide.py)", "Bash(scripts/system_performance_tester.py:*)", "Bash(integration/startup/test_user_acceptance.py )", "Bash(user_acceptance/uat_framework.py )", "Bash(regression/test_regression_suite.py )", "Bash(performance/test_performance_benchmarks.py )", "Bash(performance/test_monitoring_optimization_performance.py )", "Bash(integration/startup/test_complete_startup_flow.py)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/monitoring/cache_manager.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/performance/cache_manager.py)", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/error_handling/error_classifier/models.py )", "Bash(/Users/<USER>/PycharmProjects/PythonProject/src/error_handling/recovery_strategies/models.py)", "Bash(npx vite:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run build:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/PytharmProjects/PythonProject", "/Users/<USER>/PycharmProjects/archive"]}}