"""
智能策略推荐系统
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any
import json
import logging

from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import numpy as np
import pandas as pd

from config import config

logger = logging.getLogger(__name__)


class StrategyRecommender:
    """智能策略推荐系统"""

    def __init__(self, trading_system=None):
        """
        初始化策略推荐系统

        Args:
            trading_system: 交易系统实例
        """
        self.trading_system = trading_system
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False

        # 策略性能历史记录
        self.strategy_performance_history = {}

        # 市场状态分类器
        self.market_states = {
            "trending_up": "上升趋势",
            "trending_down": "下降趋势",
            "sideways": "横盘整理",
            "volatile": "高波动",
            "low_volatility": "低波动"
        }

        logger.info("策略推荐系统初始化完成")

    def analyze_market_conditions(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析市场状况

        Args:
            data: 市场数据

            Returns:
            市场分析结果
        """
        try:
            if len(data) < 50:
                return {"error": "数据不足，无法分析市场状况"}

            # 计算技术指标
            data = data.copy()

            # 价格趋势分析
            data['sma_20'] = data['close'].rolling(20).mean()
            data['sma_50'] = data['close'].rolling(50).mean()

            # 波动率分析
            data['returns'] = data['close'].pct_change()
            volatility = data['returns'].std() * np.sqrt(252)  # 年化波动率

            # 趋势强度
            price_change = (data['close'].iloc[-1] - data['close'].iloc[-20]) / data['close'].iloc[-20]

            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1]

            # MACD
            ema_12 = data['close'].ewm(span=12).mean()
            ema_26 = data['close'].ewm(span=26).mean()
            macd = ema_12 - ema_26
            signal = macd.ewm(span=9).mean()
            macd_histogram = macd - signal

            # 布林带
            bb_period = 20
            bb_std = 2
            bb_sma = data['close'].rolling(bb_period).mean()
            bb_std_dev = data['close'].rolling(bb_period).std()
            bb_upper = bb_sma + (bb_std_dev * bb_std)
            bb_lower = bb_sma - (bb_std_dev * bb_std)
            bb_position = (data['close'].iloc[-1] - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])

            # 成交量分析
            volume_sma = data['volume'].rolling(20).mean()
            volume_ratio = data['volume'].iloc[-1] / volume_sma.iloc[-1]

            # 市场状态判断
            market_state = self._classify_market_state(
                price_change, volatility, current_rsi,
                macd_histogram.iloc[-1], bb_position
            )

            return {
                "market_state": market_state,
                "trend_strength": round(abs(price_change) * 100, 2),
                "trend_direction": "上升" if price_change > 0 else "下降",
                "volatility": round(volatility * 100, 2),
                "rsi": round(current_rsi, 2),
                "macd_signal": "看涨" if macd_histogram.iloc[-1] > 0 else "看跌",
                "bollinger_position": round(bb_position * 100, 2),
                "volume_activity": "活跃" if volume_ratio > 1.2 else "正常" if volume_ratio > 0.8 else "低迷",
                "analysis_time": datetime.now().isoformat(),
                "data_period": f"{data.index[0]} 到 {data.index[-1]}",
                "indicators": {
            "price_change_20d": round(price_change * 100, 2),
            "volatility_annual": round(volatility * 100, 2),
            "rsi_14": round(current_rsi, 2),
            "macd_histogram": round(macd_histogram.iloc[-1], 4),
            "bb_position_pct": round(bb_position * 100, 2),
            "volume_ratio": round(volume_ratio, 2)
                }
            }

        except Exception as e:
            logger.error(f"分析市场状况失败: {e}")
            return {"error": f"分析失败: {str(e)}"}

    def _classify_market_state(self, price_change: float, volatility: float,
    rsi: float, macd_hist: float, bb_pos: float) -> str:
        """分类市场状态"""

        # 高波动市场
        if volatility > 0.3:
            return "volatile"

        # 低波动市场
        if volatility < 0.1:
            return "low_volatility"

        # 趋势市场
        if abs(price_change) > 0.05:  # 5%以上的价格变化
            if price_change > 0:
                return "trending_up"
            else:
                return "trending_down"

        # 横盘市场
        return "sideways"

    def recommend_strategies(self, symbol: str, market_analysis: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        推荐适合的交易策略

        Args:
            symbol: 交易品种
            market_analysis: 市场分析结果

            Returns:
            推荐的策略列表
        """
        try:
            # 如果没有提供市场分析，先进行分析
            if not market_analysis and self.trading_system:
                # 获取最近3个月的数据进行分析
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

                data = self.trading_system.get_market_data(symbol, start_date, end_date)
                if data.empty:
                    return [{"error": "无法获取市场数据"}]

                market_analysis = self.analyze_market_conditions(data)

                if not market_analysis or "error" in market_analysis:
                    return [{"error": "市场分析失败"}]

            market_state = market_analysis.get("market_state", "sideways")
            volatility = market_analysis.get("volatility", 0)
            rsi = market_analysis.get("rsi", 50)
            trend_strength = market_analysis.get("trend_strength", 0)

            recommendations = []

            # 基于市场状态推荐策略
            if market_state == "trending_up":
                recommendations.extend(self._get_trend_following_strategies("bullish", trend_strength))
            elif market_state == "trending_down":
                recommendations.extend(self._get_trend_following_strategies("bearish", trend_strength))
            elif market_state == "sideways":
                recommendations.extend(self._get_mean_reversion_strategies(volatility))
            elif market_state == "volatile":
                recommendations.extend(self._get_volatility_strategies(volatility))
            elif market_state == "low_volatility":
                recommendations.extend(self._get_low_volatility_strategies())

            # 基于RSI推荐策略
            if rsi < 30:
                recommendations.extend(self._get_oversold_strategies(rsi))
            elif rsi > 70:
                recommendations.extend(self._get_overbought_strategies(rsi))

            # 为每个推荐添加评分和理由
            for rec in recommendations:
                rec["market_analysis"] = market_analysis
                rec["recommendation_time"] = datetime.now().isoformat()
                rec["symbol"] = symbol

            # 按评分排序并去重
            recommendations = self._rank_and_deduplicate_recommendations(recommendations)

            return recommendations[:5]  # 返回前5个推荐

        except Exception as e:
            logger.error(f"推荐策略失败: {e}")
            return [{"error": f"推荐失败: {str(e)}"}]

    def _get_trend_following_strategies(self, direction: str, strength: float) -> List[Dict[str, Any]]:
        """获取趋势跟踪策略"""
        strategies = []

        if direction == "bullish":
            # 上升趋势策略
            strategies.append({
                "name": "移动平均突破策略",
                "type": "moving_average",
                "parameters": {
            "short_window": 10,
            "long_window": 30
                },
                "score": 85 + min(strength, 15),
                "reason": f"上升趋势强度 {strength:.1f}%，适合移动平均策略",
                "risk_level": "中等",
                "expected_return": "中高",
                "holding_period": "中期"
            })

            strategies.append({
                "name": "MACD趋势策略",
                "type": "macd",
                "parameters": {
            "fast_period": 12,
            "slow_period": 26,
            "signal_period": 9
                },
                "score": 80 + min(strength, 20),
                "reason": "MACD在上升趋势中表现良好",
                "risk_level": "中等",
                "expected_return": "中等",
                "holding_period": "中期"
            })

        else:  # bearish
            # 下降趋势策略
            strategies.append({
        "name": "反向移动平均策略",
        "type": "moving_average",
        "parameters": {
        "short_window": 5,
        "long_window": 20
        },
        "score": 80 + min(strength, 15),
        "reason": f"下降趋势强度 {strength:.1f}%，适合短期移动平均",
        "risk_level": "高",
        "expected_return": "中等",
        "holding_period": "短期"
            })

        return strategies

    def _get_mean_reversion_strategies(self, volatility: float) -> List[Dict[str, Any]]:
        """获取均值回归策略"""
        strategies = []

        strategies.append({
            "name": "布林带均值回归",
            "type": "bollinger_bands",
            "parameters": {
        "period": 20,
        "std_dev": 2
            },
            "score": 85 - min(volatility * 2, 20),
            "reason": "横盘市场适合布林带策略",
            "risk_level": "中低",
            "expected_return": "中等",
            "holding_period": "短期"
        })

        strategies.append({
            "name": "RSI超买超卖策略",
            "type": "rsi",
            "parameters": {
        "period": 14,
        "oversold": 30,
        "overbought": 70
            },
            "score": 80 - min(volatility * 1.5, 15),
            "reason": "横盘市场RSI信号较为可靠",
            "risk_level": "中等",
            "expected_return": "中等",
            "holding_period": "短期"
        })

        return strategies

    def _get_volatility_strategies(self, volatility: float) -> List[Dict[str, Any]]:
        """获取高波动策略"""
        strategies = []

        strategies.append({
            "name": "突破策略",
            "type": "bollinger_bands",
            "parameters": {
        "period": 10,
        "std_dev": 1.5
            },
            "score": 70 + min(volatility * 30, 25),
            "reason": f"高波动率 {volatility:.1f}% 适合突破策略",
            "risk_level": "高",
            "expected_return": "高",
            "holding_period": "短期"
        })

        strategies.append({
            "name": "动量策略",
            "type": "rsi",
            "parameters": {
        "period": 7,
        "oversold": 20,
        "overbought": 80
            },
            "score": 75 + min(volatility * 20, 20),
            "reason": "高波动环境下动量效应明显",
            "risk_level": "高",
            "expected_return": "高",
            "holding_period": "短期"
        })

        return strategies

    def _get_low_volatility_strategies(self) -> List[Dict[str, Any]]:
        """获取低波动策略"""
        strategies = []

        strategies.append({
            "name": "长期移动平均策略",
            "type": "moving_average",
            "parameters": {
        "short_window": 20,
        "long_window": 50
            },
            "score": 75,
            "reason": "低波动环境适合长期趋势跟踪",
            "risk_level": "低",
            "expected_return": "中低",
            "holding_period": "长期"
        })

        return strategies

    def _get_oversold_strategies(self, rsi: float) -> List[Dict[str, Any]]:
        """获取超卖反弹策略"""
        strategies = []

        oversold_strength = 30 - rsi  # 超卖程度

        strategies.append({
            "name": "RSI超卖反弹策略",
            "type": "rsi",
            "parameters": {
        "period": 14,
        "oversold": 25,
        "overbought": 75
            },
            "score": 80 + min(oversold_strength * 2, 15),
            "reason": f"RSI {rsi:.1f} 严重超卖，反弹概率高",
            "risk_level": "中等",
            "expected_return": "中高",
            "holding_period": "短期"
        })

        return strategies

    def _get_overbought_strategies(self, rsi: float) -> List[Dict[str, Any]]:
        """获取超买回调策略"""
        strategies = []

        overbought_strength = rsi - 70  # 超买程度

        strategies.append({
            "name": "RSI超买回调策略",
            "type": "rsi",
            "parameters": {
        "period": 14,
        "oversold": 25,
        "overbought": 75
            },
            "score": 75 + min(overbought_strength * 1.5, 15),
            "reason": f"RSI {rsi:.1f} 超买，回调风险较高",
            "risk_level": "中高",
            "expected_return": "中等",
            "holding_period": "短期"
        })

        return strategies

    def _rank_and_deduplicate_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对推荐进行排序和去重"""
        # 去重（基于策略类型和参数）
        seen = set()
        unique_recommendations = []

        for rec in recommendations:
            key = (rec["type"], json.dumps(rec["parameters"], sort_keys=True))
            if key not in seen:
                seen.add(key)
                unique_recommendations.append(rec)

        # 按评分排序
        unique_recommendations.sort(key=lambda x: x.get("score", 0), reverse=True)

        return unique_recommendations

    def evaluate_strategy_performance(self, strategy_config: Dict[str, Any],
    backtest_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估策略性能并更新历史记录

        Args:
            strategy_config: 策略配置
            backtest_result: 回测结果

            Returns:
            性能评估结果
        """
        try:
            strategy_key = f"{strategy_config['type']}_{json.dumps(strategy_config['parameters'], sort_keys=True)}"

            # 提取关键性能指标
            metrics = backtest_result.get("metrics", {})

            performance_data = {
                "timestamp": datetime.now().isoformat(),
                "total_return": metrics.get("total_return", 0),
                "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                "max_drawdown": metrics.get("max_drawdown", 0),
                "win_rate": metrics.get("win_rate", 0),
                "profit_factor": metrics.get("profit_factor", 0),
                "trade_count": metrics.get("total_trades", 0)
            }

            # 更新历史记录
            if strategy_key not in self.strategy_performance_history:
                self.strategy_performance_history[strategy_key] = []

            self.strategy_performance_history[strategy_key].append(performance_data)

            # 计算性能评级
            performance_grade = self._calculate_performance_grade(performance_data)

            return {
                "strategy_key": strategy_key,
                "performance_data": performance_data,
                "performance_grade": performance_grade,
                "historical_count": len(self.strategy_performance_history[strategy_key])
            }

        except Exception as e:
            logger.error(f"评估策略性能失败: {e}")
            return {"error": str(e)}

    def _calculate_performance_grade(self, performance_data: Dict[str, Any]) -> str:
        """计算策略性能评级"""
        score = 0

        # 总收益率评分 (40%)
        total_return = performance_data.get("total_return", 0)
        if total_return > 20:
            score += 40
        elif total_return > 10:
            score += 30
        elif total_return > 5:
            score += 20
        elif total_return > 0:
            score += 10

        # 夏普比率评分 (30%)
        sharpe_ratio = performance_data.get("sharpe_ratio", 0)
        if sharpe_ratio > 2:
            score += 30
        elif sharpe_ratio > 1:
            score += 20
        elif sharpe_ratio > 0.5:
            score += 10
        elif sharpe_ratio > 0:
            score += 5

        # 最大回撤评分 (20%)
        max_drawdown = abs(performance_data.get("max_drawdown", 0))
        if max_drawdown < 5:
            score += 20
        elif max_drawdown < 10:
            score += 15
        elif max_drawdown < 20:
            score += 10
        elif max_drawdown < 30:
            score += 5

        # 胜率评分 (10%)
        win_rate = performance_data.get("win_rate", 0)
        if win_rate > 60:
            score += 10
        elif win_rate > 50:
            score += 7
        elif win_rate > 40:
            score += 5

        # 评级映射
        if score >= 85:
            return "A+"
        elif score >= 75:
            return "A"
        elif score >= 65:
            return "B+"
        elif score >= 55:
            return "B"
        elif score >= 45:
            return "C+"
        elif score >= 35:
            return "C"
        else:
            return "D"

    def get_strategy_insights(self, symbol: str = None) -> Dict[str, Any]:
        """
        获取策略洞察和建议

        Args:
            symbol: 可选的交易品种

            Returns:
            策略洞察结果
        """
        try:
            insights = {
                "analysis_time": datetime.now().isoformat(),
                "symbol": symbol,
                "total_strategies_tested": len(self.strategy_performance_history),
                "top_performing_strategies": [],
                "market_insights": {},
                "recommendations": []
            }

            # 分析历史性能最好的策略
            strategy_scores = {}
            for strategy_key, history in self.strategy_performance_history.items():
                if history:
                    # 计算平均性能
                    avg_return = sum(h["total_return"] for h in history) / len(history)
                    avg_sharpe = sum(h["sharpe_ratio"] for h in history) / len(history)
                    avg_drawdown = sum(h["max_drawdown"] for h in history) / len(history)

                    # 综合评分
                    composite_score = avg_return * 0.4 + avg_sharpe * 30 - abs(avg_drawdown) * 0.3
                    strategy_scores[strategy_key] = {
                        "score": composite_score,
                        "avg_return": avg_return,
                        "avg_sharpe": avg_sharpe,
                        "avg_drawdown": avg_drawdown,
                        "test_count": len(history)
                    }

            # 排序并获取前5名
            top_strategies = sorted(strategy_scores.items(),
            key=lambda x: x[1]["score"], reverse=True)[:5]

            insights["top_performing_strategies"] = [
                {
            "strategy": strategy_key,
            "score": round(data["score"], 2),
            "avg_return": round(data["avg_return"], 2),
            "avg_sharpe": round(data["avg_sharpe"], 3),
            "avg_drawdown": round(data["avg_drawdown"], 2),
            "test_count": data["test_count"]
                }
                for strategy_key, data in top_strategies
            ]

            # 如果指定了品种，提供针对性分析
            if symbol and self.trading_system:
                try:
                    # 获取最近数据进行市场分析
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')

                    data = self.trading_system.get_market_data(symbol, start_date, end_date)
                    if not data.empty:
                        market_analysis = self.analyze_market_conditions(data)
                        insights["market_insights"] = market_analysis

                        # 基于当前市场状况推荐策略
                        recommendations = self.recommend_strategies(symbol, market_analysis)
                        insights["recommendations"] = recommendations

                except Exception as e:
                    logger.warning(f"获取 {symbol} 市场洞察失败: {e}")

            return insights

        except Exception as e:
            logger.error(f"获取策略洞察失败: {e}")
            return {"error": str(e)}

    def generate_strategy_report(self, symbol: str) -> str:
        """
        生成策略分析报告

        Args:
            symbol: 交易品种

            Returns:
            格式化的报告文本
        """
        try:
            insights = self.get_strategy_insights(symbol)

            if "error" in insights:
                return f"生成报告失败: {insights['error']}"

            report = []
            report.append("=" * 60)
            report.append(f"策略分析报告 - {symbol}")
            report.append(f"生成时间: {insights['analysis_time']}")
            report.append("=" * 60)

            # 市场洞察
            if "market_insights" in insights and insights["market_insights"]:
                market = insights["market_insights"]
                report.append("\n📊 市场状况分析")
                report.append("-" * 30)
                report.append(f"市场状态: {market.get('market_state', '未知')}")
                report.append(f"趋势方向: {market.get('trend_direction', '未知')}")
                report.append(f"趋势强度: {market.get('trend_strength', 0):.1f}%")
                report.append(f"波动率: {market.get('volatility', 0):.1f}%")
                report.append(f"RSI: {market.get('rsi', 0):.1f}")
                report.append(f"MACD信号: {market.get('macd_signal', '未知')}")
                report.append(f"成交量活跃度: {market.get('volume_activity', '未知')}")

            # 策略推荐
            if "recommendations" in insights and insights["recommendations"]:
                report.append("\n🎯 策略推荐")
                report.append("-" * 30)
                for i, rec in enumerate(insights["recommendations"][:3], 1):
                    if "error" not in rec:
                        report.append(f"{i}. {rec.get('name', '未知策略')}")
                        report.append(f"   评分: {rec.get('score', 0):.1f}/100")
                        report.append(f"   风险等级: {rec.get('risk_level', '未知')}")
                        report.append(f"   预期收益: {rec.get('expected_return', '未知')}")
                        report.append(f"   推荐理由: {rec.get('reason', '无')}")
                        report.append("")

            # 历史最佳策略
            if insights["top_performing_strategies"]:
                report.append("\n🏆 历史最佳策略")
                report.append("-" * 30)
                for i, strategy in enumerate(insights["top_performing_strategies"][:3], 1):
                    report.append(f"{i}. {strategy['strategy']}")
                    report.append(f"   综合评分: {strategy['score']:.2f}")
                    report.append(f"   平均收益: {strategy['avg_return']:.2f}%")
                    report.append(f"   夏普比率: {strategy['avg_sharpe']:.3f}")
                    report.append(f"   测试次数: {strategy['test_count']}")
                    report.append("")

            report.append("=" * 60)
            report.append("注意: 本报告仅供参考，投资有风险，决策需谨慎")
            report.append("=" * 60)

            return "\n".join(report)

        except Exception as e:
            logger.error(f"生成策略报告失败: {e}")
            return f"生成报告失败: {str(e)}"
