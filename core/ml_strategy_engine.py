"""
机器学习策略引擎
基于深度学习和机器学习算法的智能交易策略
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import logging

from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
import numpy as np
import pandas as pd

import joblib
import warnings
warnings.filterwarnings('ignore')

from config import config

logger = logging.getLogger(__name__)


class MLStrategyEngine:
    """机器学习策略引擎"""

    def __init__(self, model_dir: Path = None):
        """
        初始化ML策略引擎

        Args:
            model_dir: 模型存储目录
        """
        self.model_dir = model_dir or (config.DATA_DIR / "ml_models")
        self.model_dir.mkdir(parents=True, exist_ok=True)

        # 模型存储
        self.models = {}
        self.scalers = {}
        self.feature_columns = {}

        # 支持的ML策略类型
        self.strategy_types = {
            "lstm_trend": "LSTM趋势预测",
            "rf_classification": "随机森林分类",
            "gb_regression": "梯度提升回归",
            "ensemble_voting": "集成投票策略",
            "reinforcement_learning": "强化学习策略"
        }

        logger.info("机器学习策略引擎初始化完成")

    def create_features(self, data: pd.DataFrame, lookback_window: int = 20) -> pd.DataFrame:
        """
        创建机器学习特征

        Args:
            data: 市场数据
            lookback_window: 回望窗口

            Returns:
                pass
            特征数据框
        """
        try:
            df = data.copy()

            # 基础价格特征
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['price_change'] = df['close'] - df['open']
            df['price_range'] = df['high'] - df['low']
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
            df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']

            # 技术指标特征
            for window in [5, 10, 20, 50]:
                # 移动平均
                df[f'sma_{window}'] = df['close'].rolling(window).mean()
                df[f'ema_{window}'] = df['close'].ewm(span=window).mean()

                # 价格相对位置
                df[f'price_position_{window}'] = (df['close'] - df['close'].rolling(window).min()) / (
                    df['close'].rolling(window).max() - df['close'].rolling(window).min()
                )

                # 波动率
                df[f'volatility_{window}'] = df['returns'].rolling(window).std()

                # 成交量特征
                df[f'volume_sma_{window}'] = df['volume'].rolling(window).mean()
                df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']

            # RSI
            for period in [14, 21]:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
                rs = gain / loss
                df[f'rsi_{period}'] = 100 - (100 / (1 + rs))

            # MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']

            # 布林带
            for period in [20]:
                sma = df['close'].rolling(period).mean()
                std = df['close'].rolling(period).std()
                df[f'bb_upper_{period}'] = sma + (std * 2)
                df[f'bb_lower_{period}'] = sma - (std * 2)
                df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / (
                    df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']
                )
                df[f'bb_width_{period}'] = (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']) / sma

            # 动量指标
            for period in [10, 20]:
                df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
                df[f'roc_{period}'] = (df['close'] - df['close'].shift(period)) / df['close'].shift(period) * 100

            # 成交量指标
            df['obv'] = (df['volume'] * np.sign(df['close'].diff())).cumsum()
            df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
            df['price_volume_trend'] = ((df['close'] - df['close'].shift(1)) / df['close'].shift(1) * df['volume']).cumsum()

            # 时间特征
            df['hour'] = pd.to_datetime(df.index).hour if hasattr(df.index, 'hour') else 0
            df['day_of_week'] = pd.to_datetime(df.index).dayofweek if hasattr(df.index, 'dayofweek') else 0
            df['month'] = pd.to_datetime(df.index).month if hasattr(df.index, 'month') else 1

            # 滞后特征
            for lag in [1, 2, 3, 5]:
                df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
                df[f'rsi_14_lag_{lag}'] = df['rsi_14'].shift(lag)

            # 统计特征
            for window in [5, 10, 20]:
                df[f'returns_mean_{window}'] = df['returns'].rolling(window).mean()
                df[f'returns_std_{window}'] = df['returns'].rolling(window).std()
                df[f'returns_skew_{window}'] = df['returns'].rolling(window).skew()
                df[f'returns_kurt_{window}'] = df['returns'].rolling(window).kurt()

            # 删除无穷大和NaN值
            df = df.replace([np.inf, -np.inf], np.nan)
            df = df.fillna(method='ffill').fillna(0)

            return df

        except Exception as e:
            logger.error(f"创建特征失败: {e}")
            return pd.DataFrame()

    def create_labels(self, data: pd.DataFrame, prediction_horizon: int = 1,
    threshold: float = 0.01) -> pd.DataFrame:
        """
        创建机器学习标签

        Args:
            data: 市场数据
            prediction_horizon: 预测时间范围
            threshold: 分类阈值

            Returns:
                pass
            包含标签的数据框
        """
        try:
            df = data.copy()

            # 未来收益率
            df['future_returns'] = df['close'].shift(-prediction_horizon) / df['close'] - 1

            # 分类标签 (0: 下跌, 1: 持平, 2: 上涨)
            df['direction_label'] = 1  # 默认持平
            df.loc[df['future_returns'] > threshold, 'direction_label'] = 2  # 上涨
            df.loc[df['future_returns'] < -threshold, 'direction_label'] = 0  # 下跌

            # 回归标签
            df['return_label'] = df['future_returns']

            # 波动率标签
            df['volatility_label'] = df['future_returns'].rolling(prediction_horizon).std()

            # 趋势强度标签
            df['trend_strength'] = abs(df['future_returns'])

            return df

        except Exception as e:
            logger.error(f"创建标签失败: {e}")
            return pd.DataFrame()

    def train_random_forest_classifier(self, data: pd.DataFrame, symbol: str,
    test_size: float = 0.2) -> Dict[str, Any]:
        """
        训练随机森林分类器

        Args:
            data: 训练数据
            symbol: 交易品种
            test_size: 测试集比例

            Returns:
                pass
            训练结果
        """
        try:
            # 创建特征和标签
            feature_data = self.create_features(data)
            labeled_data = self.create_labels(feature_data)

            # 选择特征列
            feature_cols = [col for col in labeled_data.columns
            if col not in ['open', 'high', 'low', 'close', 'volume',
            'future_returns', 'direction_label', 'return_label',
            'volatility_label', 'trend_strength']]

            # 准备数据
            X = labeled_data[feature_cols].dropna()
            y = labeled_data.loc[X.index, 'direction_label']

            if len(X) < 100:
                return {"error": "训练数据不足"}

            # 数据标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=test_size, random_state=42, stratify=y
            )

            # 训练模型
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )

            model.fit(X_train, y_train)

            # 预测和评估
            y_pred = model.predict(X_test)

            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')

            # 交叉验证
            cv_scores = cross_val_score(model, X_scaled, y, cv=5)

            # 特征重要性
            feature_importance = dict(zip(feature_cols, model.feature_importances_))
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]

            # 保存模型
            model_key = f"rf_classifier_{symbol}"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            self.feature_columns[model_key] = feature_cols

            # 保存到文件
            model_path = self.model_dir / f"{model_key}.joblib"
            scaler_path = self.model_dir / f"{model_key}_scaler.joblib"

            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)

            result = {
                "model_type": "RandomForestClassifier",
                "symbol": symbol,
                "training_samples": len(X_train),
                "test_samples": len(X_test),
                "accuracy": round(accuracy, 4),
                "precision": round(precision, 4),
                "recall": round(recall, 4),
                "f1_score": round(f1, 4),
                "cv_mean": round(cv_scores.mean(), 4),
                "cv_std": round(cv_scores.std(), 4),
                "feature_count": len(feature_cols),
                "top_features": top_features,
                "model_path": str(model_path),
                "training_time": datetime.now().isoformat()
            }

            logger.info(f"随机森林分类器训练完成: {symbol}, 准确率: {accuracy:.4f}")
            return result

        except Exception as e:
            logger.error(f"训练随机森林分类器失败: {e}")
            return {"error": str(e)}

    def train_gradient_boosting_regressor(self, data: pd.DataFrame, symbol: str,
    test_size: float = 0.2) -> Dict[str, Any]:
        """
        训练梯度提升回归器

        Args:
            data: 训练数据
            symbol: 交易品种
            test_size: 测试集比例

            Returns:
                pass
            训练结果
        """
        try:
            # 创建特征和标签
            feature_data = self.create_features(data)
            labeled_data = self.create_labels(feature_data)

            # 选择特征列
            feature_cols = [col for col in labeled_data.columns
            if col not in ['open', 'high', 'low', 'close', 'volume',
            'future_returns', 'direction_label', 'return_label',
            'volatility_label', 'trend_strength']]

            # 准备数据
            X = labeled_data[feature_cols].dropna()
            y = labeled_data.loc[X.index, 'return_label']

            if len(X) < 100:
                return {"error": "训练数据不足"}

            # 数据标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=test_size, random_state=42
            )

            # 训练模型
            model = GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )

            model.fit(X_train, y_train)

            # 预测和评估
            y_pred = model.predict(X_test)

            # 计算指标
            mse = np.mean((y_test - y_pred) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(y_test - y_pred))
            r2 = model.score(X_test, y_test)

            # 交叉验证
            cv_scores = cross_val_score(model, X_scaled, y, cv=5, scoring='r2')

            # 特征重要性
            feature_importance = dict(zip(feature_cols, model.feature_importances_))
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]

            # 保存模型
            model_key = f"gb_regressor_{symbol}"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            self.feature_columns[model_key] = feature_cols

            # 保存到文件
            model_path = self.model_dir / f"{model_key}.joblib"
            scaler_path = self.model_dir / f"{model_key}_scaler.joblib"

            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)

            result = {
                "model_type": "GradientBoostingRegressor",
                "symbol": symbol,
                "training_samples": len(X_train),
                "test_samples": len(X_test),
                "mse": round(mse, 6),
                "rmse": round(rmse, 6),
                "mae": round(mae, 6),
                "r2_score": round(r2, 4),
                "cv_mean": round(cv_scores.mean(), 4),
                "cv_std": round(cv_scores.std(), 4),
                "feature_count": len(feature_cols),
                "top_features": top_features,
                "model_path": str(model_path),
                "training_time": datetime.now().isoformat()
            }

            logger.info(f"梯度提升回归器训练完成: {symbol}, R2: {r2:.4f}")
            return result

        except Exception as e:
            logger.error(f"训练梯度提升回归器失败: {e}")
            return {"error": str(e)}

    def predict(self, model_key: str, data: pd.DataFrame) -> Dict[str, Any]:
        """
        使用训练好的模型进行预测

        Args:
            model_key: 模型键
            data: 预测数据

            Returns:
                pass
            预测结果
        """
        try:
            if model_key not in self.models:
                # 尝试从文件加载模型
                model_path = self.model_dir / f"{model_key}.joblib"
                scaler_path = self.model_dir / f"{model_key}_scaler.joblib"

                if not model_path.exists():
                    return {"error": f"模型不存在: {model_key}"}

                self.models[model_key] = joblib.load(model_path)
                self.scalers[model_key] = joblib.load(scaler_path)

            model = self.models[model_key]
            scaler = self.scalers[model_key]
            feature_cols = self.feature_columns[model_key]

            # 创建特征
            feature_data = self.create_features(data)

            if feature_data.empty:
                return {"error": "特征创建失败"}

            # 选择特征
            X = feature_data[feature_cols].iloc[-1:].fillna(0)

            # 标准化
            X_scaled = scaler.transform(X)

            # 预测
            prediction = model.predict(X_scaled)[0]

            # 如果是分类器，获取概率
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(X_scaled)[0]
                prob_dict = {f"class_{i}": float(prob) for i, prob in enumerate(probabilities)}
            else:
                prob_dict = {}

            result = {
                "model_key": model_key,
                "prediction": float(prediction),
                "probabilities": prob_dict,
                "prediction_time": datetime.now().isoformat(),
                "input_features": len(feature_cols)
            }

            return result

        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {"error": str(e)}

    def generate_ml_signals(self, model_key: str, data: pd.DataFrame,
    confidence_threshold: float = 0.6) -> List[Dict[str, Any]]:
        """
        基于ML模型生成交易信号

        Args:
            model_key: 模型键
            data: 市场数据
            confidence_threshold: 置信度阈值

            Returns:
                pass
            交易信号列表
        """
        try:
            signals = []

            # 获取预测结果
            prediction_result = self.predict(model_key, data)

            if "error" in prediction_result:
                return signals

            prediction = prediction_result["prediction"]
            probabilities = prediction_result.get("probabilities", {})

            current_price = data['close'].iloc[-1]
            timestamp = data.index[-1] if hasattr(data.index[-1], 'isoformat') else datetime.now()

            # 根据模型类型生成信号
            if "classifier" in model_key:
                # 分类器信号
                max_prob = max(probabilities.values()) if probabilities else 0

                if max_prob >= confidence_threshold:
                    if prediction == 2:  # 上涨
                        signals.append({
                    "timestamp": timestamp,
                    "type": "buy",
                    "price": current_price,
                    "confidence": max_prob,
                    "reason": f"ML分类预测上涨 (置信度: {max_prob:.2f})",
                    "model": model_key
                        })
                    if prediction == 0:  # 下跌
                        signals.append({
                    "timestamp": timestamp,
                    "type": "sell",
                    "price": current_price,
                    "confidence": max_prob,
                    "reason": f"ML分类预测下跌 (置信度: {max_prob:.2f})",
                    "model": model_key
                        })

                    if "regressor" in model_key:
                        pass
                # 回归器信号
                if abs(prediction) > 0.01:  # 1%阈值
                    signal_type = "buy" if prediction > 0 else "sell"
                    confidence = min(abs(prediction) * 10, 1.0)  # 转换为置信度

                if confidence >= confidence_threshold:
                        signals.append({
                    "timestamp": timestamp,
                    "type": signal_type,
                    "price": current_price,
                    "confidence": confidence,
                    "predicted_return": prediction,
                    "reason": f"ML回归预测收益率: {prediction:.4f}",
                    "model": model_key
                        })

            return signals

        except Exception as e:
            logger.error(f"生成ML信号失败: {e}")
            return []

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            model_info = {
                "loaded_models": list(self.models.keys()),
                "available_strategies": self.strategy_types,
                "model_directory": str(self.model_dir),
                "total_models": len(self.models)
            }

            # 检查文件中的模型
            saved_models = []
            for model_file in self.model_dir.glob("*.joblib"):
                if not model_file.name.endswith("_scaler.joblib"):
                    saved_models.append(model_file.stem)

            model_info["saved_models"] = saved_models

            return model_info

        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {"error": str(e)}

    def evaluate_model_performance(self, model_key: str, data: pd.DataFrame,
    lookback_days: int = 30) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            model_key: 模型键
            data: 评估数据
            lookback_days: 回望天数

            Returns:
                pass
            性能评估结果
        """
        try:
            if len(data) < lookback_days:
                return {"error": "数据不足"}

            # 使用最近的数据进行评估
            eval_data = data.tail(lookback_days)

            predictions = []
            actuals = []

            # 滚动预测
            for i in range(1, len(eval_data)):
                train_data = eval_data.iloc[:i]

                if len(train_data) < 20:  # 最少需要20个数据点
                    continue

                # 预测
                pred_result = self.predict(model_key, train_data)

                if "error" not in pred_result:
                    predictions.append(pred_result["prediction"])

                    # 实际值（下一期收益率）
                    if i < len(eval_data) - 1:
                        actual_return = (eval_data.iloc[i+1]['close'] / eval_data.iloc[i]['close']) - 1
                        actuals.append(actual_return)

                        if len(predictions) == 0:
                            pass
                return {"error": "无法生成预测"}

            # 计算性能指标
            predictions = np.array(predictions)
            actuals = np.array(actuals[:len(predictions)])

            # 回归指标
            mse = np.mean((actuals - predictions) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(actuals - predictions))

            # 方向准确性
            pred_direction = np.sign(predictions)
            actual_direction = np.sign(actuals)
            direction_accuracy = np.mean(pred_direction == actual_direction)

            # 相关性
            correlation = np.corrcoef(predictions, actuals)[0, 1] if len(predictions) > 1 else 0

            result = {
                "model_key": model_key,
                "evaluation_period": lookback_days,
                "total_predictions": len(predictions),
                "mse": round(mse, 6),
                "rmse": round(rmse, 6),
                "mae": round(mae, 6),
                "direction_accuracy": round(direction_accuracy, 4),
                "correlation": round(correlation, 4),
                "evaluation_time": datetime.now().isoformat()
            }

            return result

        except Exception as e:
            logger.error(f"评估模型性能失败: {e}")
            return {"error": str(e)}


class MLStrategyOptimizer:
    """ML策略优化器"""

    def __init__(self, ml_engine: MLStrategyEngine):
        """
        初始化ML策略优化器

        Args:
            ml_engine: ML策略引擎
        """
        self.ml_engine = ml_engine
        logger.info("ML策略优化器初始化完成")

    def optimize_hyperparameters(self, data: pd.DataFrame, symbol: str,
    model_type: str = "random_forest") -> Dict[str, Any]:
        """
        优化超参数

        Args:
            data: 训练数据
            symbol: 交易品种
            model_type: 模型类型

            Returns:
                pass
            优化结果
        """
        try:
            from sklearn.model_selection import GridSearchCV

            # 创建特征和标签
            feature_data = self.ml_engine.create_features(data)
            labeled_data = self.ml_engine.create_labels(feature_data)

            # 选择特征列
            feature_cols = [col for col in labeled_data.columns
            if col not in ['open', 'high', 'low', 'close', 'volume',
            'future_returns', 'direction_label', 'return_label',
            'volatility_label', 'trend_strength']]

            # 准备数据
            X = labeled_data[feature_cols].dropna()
            y = labeled_data.loc[X.index, 'direction_label']

            if len(X) < 100:
                return {"error": "训练数据不足"}

            # 数据标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # 定义参数网格
            if model_type == "random_forest":
                model = RandomForestClassifier(random_state=42)
                param_grid = {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [5, 10, 15],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            else:
                return {"error": f"不支持的模型类型: {model_type}"}

            # 网格搜索
            grid_search = GridSearchCV(
                model, param_grid, cv=5, scoring='accuracy', n_jobs=-1
            )

            grid_search.fit(X_scaled, y)

            # 最佳参数
            best_params = grid_search.best_params_
            best_score = grid_search.best_score_

            result = {
                "model_type": model_type,
                "symbol": symbol,
                "best_parameters": best_params,
                "best_cv_score": round(best_score, 4),
                "optimization_time": datetime.now().isoformat()
            }

            logger.info(f"超参数优化完成: {symbol}, 最佳得分: {best_score:.4f}")
            return result

        except Exception as e:
            logger.error(f"超参数优化失败: {e}")
            return {"error": str(e)}

    def feature_selection(self, data: pd.DataFrame, top_k: int = 20) -> Dict[str, Any]:
        """
        特征选择

        Args:
            data: 数据
            top_k: 选择的特征数量

            Returns:
                pass
            特征选择结果
        """
        try:
            from sklearn.feature_selection import SelectKBest, f_classif

            # 创建特征和标签
            feature_data = self.ml_engine.create_features(data)
            labeled_data = self.ml_engine.create_labels(feature_data)

            # 选择特征列
            feature_cols = [col for col in labeled_data.columns
            if col not in ['open', 'high', 'low', 'close', 'volume',
            'future_returns', 'direction_label', 'return_label',
            'volatility_label', 'trend_strength']]

            # 准备数据
            X = labeled_data[feature_cols].dropna()
            y = labeled_data.loc[X.index, 'direction_label']

            if len(X) < 50:
                return {"error": "数据不足"}

            # 特征选择
            selector = SelectKBest(score_func=f_classif, k=min(top_k, len(feature_cols)))
            X_selected = selector.fit_transform(X, y)

            # 获取选中的特征
            selected_features = [feature_cols[i] for i in selector.get_support(indices=True)]
            feature_scores = dict(zip(feature_cols, selector.scores_))

            # 排序特征
            sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)

            result = {
                "total_features": len(feature_cols),
                "selected_features": selected_features,
                "feature_scores": dict(sorted_features[:top_k]),
                "selection_time": datetime.now().isoformat()
            }

            return result

        except Exception as e:
            logger.error(f"特征选择失败: {e}")
            return {"error": str(e)}
