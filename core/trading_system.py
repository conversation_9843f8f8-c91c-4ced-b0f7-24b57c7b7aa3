"""
简化的交易系统核心类
"""
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

import pandas as pd

from config import config
from .data_manager import DataManager
from .strategy_manager import StrategyManager
from .backtest_engine import BacktestEngine
from .strategy_optimizer import StrategyOptimizer
from .risk_manager import RiskManager
from .strategy_recommender import StrategyRecommender
from .system_monitor import SystemMonitor

logger = logging.getLogger(__name__)


class TradingSystem:
    """简化的交易系统核心类"""

    def __init__(self):
        """初始化交易系统"""
        self.data_manager = DataManager()
        self.strategy_manager = StrategyManager()
        self.backtest_engine = BacktestEngine(
            data_manager=self.data_manager,
            strategy_manager=self.strategy_manager
        )
        # 设置并行引擎以避免循环引用
        self.backtest_engine.set_parallel_engine(self)
        self.strategy_optimizer = StrategyOptimizer(self)

        # 风险管理
        self.risk_manager = RiskManager()

        # 策略推荐系统
        self.strategy_recommender = StrategyRecommender(self)

        # 系统监控
        self.system_monitor = SystemMonitor()

        # 系统状态
        self.is_running = False
        self.start_time = None

        logger.info("交易系统初始化完成")

    def start(self) -> bool:
        """启动交易系统"""
        try:
            self.is_running = True
            self.start_time = datetime.now()

            # 启动系统监控
            self.system_monitor.start_monitoring()

            logger.info("交易系统启动成功")
            return True
        except Exception as e:
            logger.error(f"交易系统启动失败: {e}")
            return False

    def stop(self) -> bool:
        """停止交易系统"""
        try:
            self.is_running = False

            # 停止系统监控
            self.system_monitor.stop_monitoring()

            logger.info("交易系统已停止")
            return True
        except Exception as e:
            logger.error(f"交易系统停止失败: {e}")
            return False

    def get_market_data(self, symbol: str, start_date: str = None, 
                       end_date: str = None, period: str = "1d") -> pd.DataFrame:
        """获取市场数据"""
        return self.data_manager.get_data(symbol, start_date, end_date, period)

    def add_strategy(self, strategy_name: str, strategy_config: Dict[str, Any]) -> bool:
        """添加策略"""
        return self.strategy_manager.add_strategy(strategy_name, strategy_config)

    def remove_strategy(self, strategy_name: str) -> bool:
        """移除策略"""
        return self.strategy_manager.remove_strategy(strategy_name)

    def get_strategies(self) -> List[str]:
        """获取所有策略"""
        return self.strategy_manager.get_strategies()

    def run_backtest(self, strategy_name: str, symbol: str, 
                    start_date: str, end_date: str, 
                    initial_capital: float = None) -> Dict[str, Any]:
        """运行回测"""
        return self.backtest_engine.run_backtest(
            strategy_name, symbol, start_date, end_date, initial_capital
        )

    def optimize_strategy(self, strategy_name: str, symbol: str,
                         start_date: str, end_date: str,
                         param_ranges: Dict[str, Any]) -> Dict[str, Any]:
        """优化策略参数"""
        return self.strategy_optimizer.optimize_strategy(
            strategy_name, symbol, start_date, end_date, param_ranges
        )

    def get_strategy_recommendations(self, symbol: str = None) -> List[Dict[str, Any]]:
        """获取策略推荐"""
        return self.strategy_recommender.get_recommendations(symbol)

    def calculate_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """计算风险指标"""
        return self.risk_manager.calculate_risk_metrics(returns)

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        uptime = None
        if self.start_time:
            uptime = str(datetime.now() - self.start_time)

        return {
            "status": "运行中" if self.is_running else "已停止",
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime": uptime,
            "version": config.VERSION,
            "components": {
                "data_manager": "正常" if self.data_manager else "异常",
                "strategy_manager": "正常" if self.strategy_manager else "异常",
                "backtest_engine": "正常" if self.backtest_engine else "异常",
                "risk_manager": "正常" if self.risk_manager else "异常"
            }
        }

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "system_info": self.get_system_info(),
            "monitoring": self.system_monitor.get_current_metrics() if self.system_monitor else {},
            "capabilities": {
                "basic_trading": True,
                "strategy_management": True,
                "backtesting": True,
                "market_analysis": True,
                "strategy_recommendations": True,
                "risk_management": True,
                "system_monitoring": True
            }
        }

    def generate_report(self, symbol: str, start_date: str, end_date: str) -> str:
        """生成分析报告"""
        try:
            # 获取市场数据
            data = self.get_market_data(symbol, start_date, end_date)
            if data.empty:
                return "无法获取市场数据"

            # 获取策略推荐
            recommendations = self.get_strategy_recommendations(symbol)

            # 计算基本统计
            returns = data['close'].pct_change().dropna()
            risk_metrics = self.calculate_risk_metrics(returns)

            # 生成报告
            report = f"""
# {symbol} 分析报告

## 基本信息
- 分析期间: {start_date} 至 {end_date}
- 数据点数: {len(data)}
- 当前价格: {data['close'].iloc[-1]:.2f}

## 收益统计
- 总收益率: {((data['close'].iloc[-1] / data['close'].iloc[0]) - 1) * 100:.2f}%
- 年化波动率: {risk_metrics.get('annual_volatility', 0) * 100:.2f}%
- 最大回撤: {risk_metrics.get('max_drawdown', 0) * 100:.2f}%

## 策略推荐
"""
            for i, rec in enumerate(recommendations[:3], 1):
                report += f"- {i}. {rec.get('strategy', 'N/A')}: {rec.get('reason', 'N/A')}\n"

            return report

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return f"生成报告失败: {str(e)}"

    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            health_status = {
                "overall": "healthy",
                "components": {},
                "issues": []
            }

            # 检查各组件
            components = {
                "data_manager": self.data_manager,
                "strategy_manager": self.strategy_manager,
                "backtest_engine": self.backtest_engine,
                "risk_manager": self.risk_manager,
                "system_monitor": self.system_monitor
            }

            for name, component in components.items():
                if component:
                    health_status["components"][name] = "healthy"
                else:
                    health_status["components"][name] = "unhealthy"
                    health_status["issues"].append(f"{name} 组件异常")

            # 如果有问题，设置整体状态
            if health_status["issues"]:
                health_status["overall"] = "degraded"

            return health_status

        except Exception as e:
            return {
                "overall": "unhealthy",
                "error": str(e)
            }
