"""
系统监控和运维模块
提供全面的系统监控、日志管理、健康检查和自动化运维功能
"""

from collections import deque
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Callable
import logging
import threading
import time

import psutil

import sqlite3

from config import config

logger = logging.getLogger(__name__)


class SystemMonitor:
    """系统监控器"""

    def __init__(self, monitoring_interval: int = 30):
        """
        初始化系统监控器

        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.monitor_thread = None

        # 监控数据存储
        self.metrics_history = deque(maxlen=2880)  # 24小时数据（30秒间隔）
        self.alerts_history = deque(maxlen=1000)

        # 阈值配置
        self.thresholds = {
            "cpu_warning": 70.0,
            "cpu_critical": 90.0,
            "memory_warning": 80.0,
            "memory_critical": 95.0,
            "disk_warning": 85.0,
            "disk_critical": 95.0,
            "response_time_warning": 2.0,
            "response_time_critical": 5.0
        }

        # 告警配置
        self.alert_handlers = []
        self.alert_cooldown = {}  # 告警冷却时间

        logger.info("系统监控器初始化完成")

    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        logger.info("系统监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        logger.info("系统监控已停止")

    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()

                # 存储指标
                self.metrics_history.append(metrics)

                # 检查告警
                self._check_alerts(metrics)

                # 等待下次监控
                time.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(self.monitoring_interval)

    def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU指标
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # 内存指标
            memory = psutil.virtual_memory()

            # 磁盘指标
            disk = psutil.disk_usage('/')

            # 网络指标
            network = psutil.net_io_counters()

            # 进程指标
            process_count = len(psutil.pids())

            metrics = {
                "timestamp": datetime.now().isoformat(),
                "cpu": {
            "percent": cpu_percent,
            "count": cpu_count
                },
                "memory": {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used
                },
                "disk": {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
                },
                "network": {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv
                },
                "processes": {
            "count": process_count
                }
            }

            return metrics

        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return {}

    def _check_alerts(self, metrics: Dict[str, Any]):
        """检查告警条件"""
        try:
            current_time = datetime.now()

            # CPU告警
            cpu_percent = metrics.get("cpu", {}).get("percent", 0)
            if cpu_percent >= self.thresholds["cpu_critical"]:
                self._trigger_alert("cpu_critical", f"CPU使用率严重: {cpu_percent:.1f}%", "critical")
            elif cpu_percent >= self.thresholds["cpu_warning"]:
                self._trigger_alert("cpu_warning", f"CPU使用率警告: {cpu_percent:.1f}%", "warning")

            # 内存告警
            memory_percent = metrics.get("memory", {}).get("percent", 0)
            if memory_percent >= self.thresholds["memory_critical"]:
                self._trigger_alert("memory_critical", f"内存使用率严重: {memory_percent:.1f}%", "critical")
            elif memory_percent >= self.thresholds["memory_warning"]:
                self._trigger_alert("memory_warning", f"内存使用率警告: {memory_percent:.1f}%", "warning")

            # 磁盘告警
            disk_percent = metrics.get("disk", {}).get("percent", 0)
            if disk_percent >= self.thresholds["disk_critical"]:
                self._trigger_alert("disk_critical", f"磁盘使用率严重: {disk_percent:.1f}%", "critical")
            elif disk_percent >= self.thresholds["disk_warning"]:
                self._trigger_alert("disk_warning", f"磁盘使用率警告: {disk_percent:.1f}%", "warning")

        except Exception as e:
            logger.error(f"检查告警失败: {e}")

    def _trigger_alert(self, alert_type: str, message: str, severity: str):
        """触发告警"""
        try:
            current_time = datetime.now()

            # 检查冷却时间
            if alert_type in self.alert_cooldown:
                last_alert_time = self.alert_cooldown[alert_type]
                if current_time - last_alert_time < timedelta(minutes=5):  # 5分钟冷却
                    return

            # 创建告警
            alert = {
                "type": alert_type,
                "message": message,
                "severity": severity,
                "timestamp": current_time.isoformat()
            }

            # 存储告警
            self.alerts_history.append(alert)

            # 更新冷却时间
            self.alert_cooldown[alert_type] = current_time

            # 执行告警处理器
            for handler in self.alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"告警处理器执行失败: {e}")

            # 记录日志
            if severity == "critical":
                logger.critical(f"[ALERT] {message}")
            elif severity == "warning":
                logger.warning(f"[ALERT] {message}")
            else:
                logger.info(f"[ALERT] {message}")

        except Exception as e:
            logger.error(f"触发告警失败: {e}")

    def add_alert_handler(self, handler: Callable):
        """添加告警处理器"""
        self.alert_handlers.append(handler)

    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return self._collect_system_metrics()

    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取指标历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        filtered_metrics = []
        for metric in self.metrics_history:
            try:
                metric_time = datetime.fromisoformat(metric["timestamp"])
                if metric_time >= cutoff_time:
                    filtered_metrics.append(metric)
            except:
                continue

        return filtered_metrics

    def get_alerts_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        filtered_alerts = []
        for alert in self.alerts_history:
            try:
                alert_time = datetime.fromisoformat(alert["timestamp"])
                if alert_time >= cutoff_time:
                    filtered_alerts.append(alert)
            except:
                continue

        return filtered_alerts


class LogManager:
    """日志管理器"""

    def __init__(self, log_dir: Path = None):
        """
        初始化日志管理器

        Args:
            log_dir: 日志目录
        """
        self.log_dir = log_dir or config.LOGS_DIR
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 日志轮转配置
        self.max_log_size = 100 * 1024 * 1024  # 100MB
        self.max_log_files = 10

        logger.info("日志管理器初始化完成")

    def rotate_logs(self):
        """轮转日志文件"""
        try:
            log_files = list(self.log_dir.glob("*.log"))

            for log_file in log_files:
                if log_file.stat().st_size > self.max_log_size:
                    # 轮转日志
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    rotated_name = f"{log_file.stem}_{timestamp}.log"
                    rotated_path = self.log_dir / rotated_name

                    log_file.rename(rotated_path)

                    # 创建新的日志文件
                    log_file.touch()

                    logger.info(f"日志轮转完成: {log_file.name} -> {rotated_name}")

            # 清理旧日志
            self._cleanup_old_logs()

        except Exception as e:
            logger.error(f"日志轮转失败: {e}")

    def _cleanup_old_logs(self):
        """清理旧日志文件"""
        try:
            # 按修改时间排序
            log_files = sorted(
                self.log_dir.glob("*.log"),
                key=lambda f: f.stat().st_mtime,
                reverse=True
            )

            # 保留最新的文件，删除多余的
            if len(log_files) > self.max_log_files:
                for old_file in log_files[self.max_log_files:]:
                    old_file.unlink()
                    logger.info(f"删除旧日志文件: {old_file.name}")

        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")

    def analyze_logs(self, log_file: str = "app.log", hours: int = 24) -> Dict[str, Any]:
        """分析日志"""
        try:
            log_path = self.log_dir / log_file

            if not log_path.exists():
                return {"error": "日志文件不存在"}

            cutoff_time = datetime.now() - timedelta(hours=hours)

            stats = {
                "total_lines": 0,
                "error_count": 0,
                "warning_count": 0,
                "info_count": 0,
                "debug_count": 0,
                "recent_errors": [],
                "analysis_time": datetime.now().isoformat()
            }

            with open(log_path, 'r', encoding='utf-8') as f:
                for line in f:
                    stats["total_lines"] += 1

                    # 简单的日志级别统计
                    if "ERROR" in line:
                        stats["error_count"] += 1
                        if len(stats["recent_errors"]) < 10:
                            stats["recent_errors"].append(line.strip())
                    elif "WARNING" in line:
                        stats["warning_count"] += 1
                    elif "INFO" in line:
                        stats["info_count"] += 1
                    elif "DEBUG" in line:
                        stats["debug_count"] += 1

            return stats

        except Exception as e:
            logger.error(f"日志分析失败: {e}")
            return {"error": str(e)}


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        """初始化健康检查器"""
        self.checks = {}
        self.check_results = {}

        # 注册默认检查
        self._register_default_checks()

        logger.info("健康检查器初始化完成")

    def _register_default_checks(self):
        """注册默认健康检查"""
        self.register_check("system_resources", self._check_system_resources)
        self.register_check("disk_space", self._check_disk_space)
        self.register_check("database_connection", self._check_database_connection)
        self.register_check("log_files", self._check_log_files)

    def register_check(self, name: str, check_function: Callable):
        """注册健康检查"""
        self.checks[name] = check_function

    def run_health_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {}
        }

        failed_checks = 0

        for check_name, check_function in self.checks.items():
            try:
                check_result = check_function()
                results["checks"][check_name] = check_result

                if not check_result.get("status") == "ok":
                    failed_checks += 1

            except Exception as e:
                results["checks"][check_name] = {
                    "status": "error",
                    "message": f"检查执行失败: {str(e)}"
                }
                failed_checks += 1

        # 确定整体状态
        if failed_checks == 0:
            results["overall_status"] = "healthy"
        if failed_checks <= len(self.checks) * 0.3:  # 30%以下失败
            results["overall_status"] = "degraded"
        else:
            results["overall_status"] = "unhealthy"

        results["failed_checks"] = failed_checks
        results["total_checks"] = len(self.checks)

        self.check_results = results
        return results

    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            issues = []

            if cpu_percent > 90:
                issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")

            if memory.percent > 95:
                issues.append(f"内存使用率过高: {memory.percent:.1f}%")

            return {
                "status": "ok" if not issues else "warning",
                "message": "系统资源正常" if not issues else "; ".join(issues),
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"系统资源检查失败: {str(e)}"
            }

    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100

            if disk_percent > 95:
                status = "critical"
                message = f"磁盘空间严重不足: {disk_percent:.1f}%"
            elif disk_percent > 85:
                status = "warning"
                message = f"磁盘空间不足: {disk_percent:.1f}%"
            else:
                status = "ok"
                message = f"磁盘空间正常: {disk_percent:.1f}%"

            return {
                "status": status,
                "message": message,
                "details": {
                    "disk_percent": disk_percent,
                    "free_gb": disk.free / (1024**3)
                }
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"磁盘空间检查失败: {str(e)}"
            }

    def _check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            db_path = config.DATABASE_DIR / "trading_system.db"

            if not db_path.exists():
                return {
                    "status": "error",
                    "message": "数据库文件不存在"
                }

            # 尝试连接数据库
            with sqlite3.connect(str(db_path), timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()

            return {
                "status": "ok",
                "message": "数据库连接正常"
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"数据库连接失败: {str(e)}"
            }

    def _check_log_files(self) -> Dict[str, Any]:
        """检查日志文件"""
        try:
            log_dir = config.LOGS_DIR

            if not log_dir.exists():
                return {
                    "status": "warning",
                    "message": "日志目录不存在"
                }

            log_files = list(log_dir.glob("*.log"))

            if not log_files:
                return {
                    "status": "warning",
                    "message": "没有找到日志文件"
                }

            # 检查日志文件大小
            large_files = []
            for log_file in log_files:
                size_mb = log_file.stat().st_size / (1024 * 1024)
                if size_mb > 100:  # 超过100MB
                    large_files.append(f"{log_file.name} ({size_mb:.1f}MB)")

            if large_files:
                return {
                    "status": "warning",
                    "message": f"发现大日志文件: {', '.join(large_files)}",
                    "details": {
                        "large_files": large_files
                    }
                }

            return {
                "status": "ok",
                "message": f"日志文件正常 ({len(log_files)} 个文件)"
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"日志文件检查失败: {str(e)}"
            }


class AutomationManager:
    """自动化管理器"""

    def __init__(self, system_monitor: SystemMonitor, log_manager: LogManager,
    health_checker: HealthChecker):
        """
        初始化自动化管理器

        Args:
            system_monitor: 系统监控器
            log_manager: 日志管理器
            health_checker: 健康检查器
        """
        self.system_monitor = system_monitor
        self.log_manager = log_manager
        self.health_checker = health_checker

        # 自动化任务配置
        self.automation_tasks = {}
        self.task_schedules = {}

        # 注册默认任务
        self._register_default_tasks()

        logger.info("自动化管理器初始化完成")

    def _register_default_tasks(self):
        """注册默认自动化任务"""
        # 每小时轮转日志
        self.register_task("log_rotation", self.log_manager.rotate_logs, interval=3600)

        # 每30分钟健康检查
        self.register_task("health_check", self.health_checker.run_health_checks, interval=1800)

        # 每天清理临时文件
        self.register_task("cleanup_temp", self._cleanup_temp_files, interval=86400)

    def register_task(self, name: str, task_function: Callable, interval: int):
        """
        注册自动化任务

        Args:
            name: 任务名称
            task_function: 任务函数
            interval: 执行间隔（秒）
        """
        self.automation_tasks[name] = task_function
        self.task_schedules[name] = {
            "interval": interval,
            "last_run": None,
            "next_run": datetime.now() + timedelta(seconds=interval)
        }

    def run_automation_cycle(self):
        """运行自动化周期"""
        current_time = datetime.now()

        for task_name, task_function in self.automation_tasks.items():
            schedule = self.task_schedules[task_name]

            if current_time >= schedule["next_run"]:
                try:
                    logger.info(f"执行自动化任务: {task_name}")
                    task_function()

                    # 更新调度信息
                    schedule["last_run"] = current_time
                    schedule["next_run"] = current_time + timedelta(seconds=schedule["interval"])

                except Exception as e:
                    logger.error(f"自动化任务执行失败 {task_name}: {e}")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dirs = [
                config.DATA_DIR / "temp",
                config.DATA_DIR / "cache" / "temp",
                Path("/tmp") / "trading_system"
            ]

            cleaned_files = 0

            for temp_dir in temp_dirs:
                if temp_dir.exists():
                    for temp_file in temp_dir.glob("*"):
                        try:
                            # 删除超过24小时的临时文件
                            if temp_file.is_file():
                                file_age = datetime.now() - datetime.fromtimestamp(temp_file.stat().st_mtime)
                                if file_age > timedelta(hours=24):
                                    temp_file.unlink()
                                    cleaned_files += 1
                        except Exception as e:
                            logger.warning(f"删除临时文件失败 {temp_file}: {e}")

            if cleaned_files > 0:
                logger.info(f"清理临时文件完成: {cleaned_files} 个文件")

        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")

    def get_automation_status(self) -> Dict[str, Any]:
        """获取自动化状态"""
        current_time = datetime.now()

        status = {
            "current_time": current_time.isoformat(),
            "total_tasks": len(self.automation_tasks),
            "tasks": {}
        }

        for task_name, schedule in self.task_schedules.items():
            status["tasks"][task_name] = {
                "interval_seconds": schedule["interval"],
                "last_run": schedule["last_run"].isoformat() if schedule["last_run"] else None,
                "next_run": schedule["next_run"].isoformat(),
                "time_until_next": str(schedule["next_run"] - current_time) if schedule["next_run"] > current_time else "overdue"
            }

        return status
