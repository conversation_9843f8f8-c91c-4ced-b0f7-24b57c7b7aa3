#!/usr/bin/env python3
"""
简化的数据库初始化脚本
"""
from pathlib import Path
import os

import sqlite3

def init_database():
    """初始化数据库"""
    db_path = Path("data/databases/trading_system.db")

    # 确保目录存在
    db_path.parent.mkdir(parents=True, exist_ok=True)

    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # 创建基础表结构
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS strategies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    config TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    open REAL,
    high REAL,
    low REAL,
    close REAL,
    volume INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)

    cursor.execute("""
        CREATE TABLE IF NOT EXISTS backtests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    strategy_id INTEGER,
    config TEXT,
    results TEXT,
    status TEXT DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (strategy_id) REFERENCES strategies (id)
        )
    """)

    # 提交更改并关闭连接
    conn.commit()
    conn.close()

    print(f"✅ 数据库初始化完成: {db_path}")
    print("📊 创建的表: strategies, market_data, backtests")

    if __name__ == "__main__":
        pass
    init_database()
