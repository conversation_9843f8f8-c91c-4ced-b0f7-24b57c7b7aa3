#!/usr/bin/env python3
"""
数据库初始化和测试数据创建脚本
"""

from datetime import datetime, timedelta
import random

import sqlite3


def create_test_data():
    """创建测试数据"""
    db_path = 'data/databases/trading_system.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 清空现有数据
    cursor.execute("DELETE FROM market_data")
    cursor.execute("DELETE FROM economic_data")
    cursor.execute("DELETE FROM market_info")
    cursor.execute("DELETE FROM data_sources")

    # 插入测试的市场数据
    print("📊 创建测试市场数据...")
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']
    base_date = datetime.now() - timedelta(days=30)

    for i, symbol in enumerate(symbols):
        for day in range(30):
            date = base_date + timedelta(days=day)
            base_price = 100 + i * 50
            price = base_price + random.uniform(-10, 10)

            cursor.execute("""
                INSERT INTO market_data (symbol, timestamp, open, high, low, close, volume, market, exchange, currency)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                symbol,
                date.isoformat(),
                price,
                price + random.uniform(0, 5),
                price - random.uniform(0, 5),
                price + random.uniform(-2, 2),
                random.randint(100000, 1000000),
                'US',
                'NASDAQ',
                'USD'
            ))

    # 插入市场信息
    print("🏢 创建市场信息...")
    markets = [
        ('AAPL', 'Apple Inc.', 'US', 'NASDAQ', 'USD', 1.0, 0.01, '09:30-16:00', 'America/New_York'),
        ('GOOGL', 'Alphabet Inc.', 'US', 'NASDAQ', 'USD', 1.0, 0.01, '09:30-16:00', 'America/New_York'),
        ('MSFT', 'Microsoft Corp.', 'US', 'NASDAQ', 'USD', 1.0, 0.01, '09:30-16:00', 'America/New_York'),
        ('TSLA', 'Tesla Inc.', 'US', 'NASDAQ', 'USD', 1.0, 0.01, '09:30-16:00', 'America/New_York'),
        ('AMZN', 'Amazon.com Inc.', 'US', 'NASDAQ', 'USD', 1.0, 0.01, '09:30-16:00', 'America/New_York')
    ]

    for symbol, name, market, exchange, currency, lot_size, tick_size, trading_hours, timezone in markets:
        cursor.execute("""
            INSERT INTO market_info (symbol, name, market, exchange, currency, lot_size, tick_size, trading_hours, timezone, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        """, (symbol, name, market, exchange, currency, lot_size, tick_size, trading_hours, timezone))

    # 插入数据源信息
    print("🔗 创建数据源信息...")
    data_sources = [
        ('yahoo_finance', 'Yahoo Finance', 'YahooFinanceAdapter', 1, 1, '{}', '{}'),
        ('alpha_vantage', 'Alpha Vantage', 'AlphaVantageAdapter', 1, 2, '{}', '{}'),
        ('iex_cloud', 'IEX Cloud', 'IEXCloudAdapter', 0, 3, '{}', '{}')
    ]

    for source_id, source_name, adapter_class, enabled, priority, rate_limit, credentials in data_sources:
        cursor.execute("""
            INSERT INTO data_sources (id, name, adapter_class, enabled, priority, rate_limit, credentials)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (source_id, source_name, adapter_class, enabled, priority, rate_limit, credentials))

    # 插入经济数据
    print("📈 创建经济数据...")
    economic_indicators = ['GDP', 'CPI', 'UNEMPLOYMENT_RATE', 'INTEREST_RATE']

    for indicator in economic_indicators:
        for month in range(12):
            date = datetime(2024, month + 1, 1)
            value = random.uniform(1, 10)

            cursor.execute("""
                INSERT INTO economic_data (series_id, timestamp, value, market, source, unit, frequency)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (indicator, date.isoformat(), value, 'ECONOMIC', 'FRED', '%', 'Monthly'))

    conn.commit()

    # 验证数据
    print("\\n📋 数据统计:")
    tables = ['market_data', 'economic_data', 'market_info', 'data_sources']
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"  {table}: {count} 条记录")

    conn.close()
    print("\\n✅ 测试数据创建完成")

    def add_api_endpoints():
        pass
    """添加API端点数据"""
    db_path = 'data/databases/trading_system.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 检查是否已经有api_endpoints表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_endpoints'")
    if not cursor.fetchone():
        # 创建API端点表
        cursor.execute("""
            CREATE TABLE api_endpoints (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        endpoint_path TEXT NOT NULL,
        method TEXT NOT NULL,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 插入API端点数据
        endpoints = [
            ('/api/market/data', 'GET', '获取市场数据'),
            ('/api/market/symbols', 'GET', '获取交易标的列表'),
            ('/api/strategy/list', 'GET', '获取策略列表'),
            ('/api/backtest/run', 'POST', '运行回测'),
            ('/api/portfolio/positions', 'GET', '获取持仓信息'),
            ('/api/risk/metrics', 'GET', '获取风险指标')
        ]

        for path, method, desc in endpoints:
            cursor.execute("""
                INSERT INTO api_endpoints (endpoint_path, method, description)
                VALUES (?, ?, ?)
            """, (path, method, desc))

        conn.commit()
        print("✅ API端点表创建完成")

    conn.close()

    if __name__ == "__main__":
        pass
    print("🔧 初始化数据库系统...")
    create_test_data()
    print("🎉 数据库初始化完成！")
