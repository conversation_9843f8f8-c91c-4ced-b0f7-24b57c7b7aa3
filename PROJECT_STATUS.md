# 量化交易系统项目状态

## 项目概述

这是一个基于Python的量化交易系统，提供策略开发、回测分析、风险管理等功能。

**版本**: 4.0.0  
**状态**: ✅ 已清理并可正常运行  
**最后更新**: 2025-08-26

## 系统架构

### 核心组件

- **后端API** (FastAPI): 提供RESTful API接口
- **前端界面** (React + TypeScript): 用户交互界面
- **核心引擎**: 交易系统核心逻辑
- **数据管理**: 市场数据获取和存储
- **策略管理**: 交易策略开发和管理
- **回测引擎**: 策略回测和性能分析
- **风险管理**: 风险控制和监控

### 技术栈

**后端**:
- Python 3.7+
- FastAPI
- SQLite
- Pandas
- NumPy

**前端**:
- React 18
- TypeScript
- Ant Design
- Vite

## 启动方式

### 统一启动（推荐）
```bash
python3 run.py
```

### 分别启动
```bash
# 仅启动后端
START_MODE=backend python3 run.py

# 仅启动前端
START_MODE=frontend python3 run.py
```

## 访问地址

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 主要功能

### 1. 数据管理
- 多数据源支持（Yahoo Finance、Alpha Vantage、模拟数据）
- 数据缓存和验证
- 数据质量检查

### 2. 策略管理
- 内置策略（移动平均、RSI等）
- 自定义策略开发
- 策略参数优化

### 3. 回测分析
- 历史数据回测
- 性能指标计算
- 并行回测支持

### 4. 风险管理
- 风险指标计算
- 仓位管理
- 止损止盈

### 5. 系统监控
- 系统资源监控
- 性能指标跟踪
- 健康状态检查

## 项目结构

```
├── backend/           # 后端API服务
├── frontend/          # 前端React应用
├── core/              # 核心业务逻辑
├── data/              # 数据存储目录
├── docs/              # 项目文档
├── logs/              # 日志文件
├── scripts/           # 工具脚本
├── tests/             # 测试文件
├── config.py          # 配置文件
└── run.py             # 统一启动脚本
```

## 最近更改

### 2025-08-26 项目清理
- ✅ 删除了损坏的模块和文件
- ✅ 修复了语法错误
- ✅ 简化了系统架构
- ✅ 确保了基本功能正常运行
- ✅ 清理了重复文档

### 保留的核心模块
- `core/trading_system.py` - 交易系统核心（已简化）
- `core/data_manager.py` - 数据管理
- `core/strategy_manager.py` - 策略管理
- `core/backtest_engine.py` - 回测引擎
- `core/risk_manager.py` - 风险管理
- `core/strategy_recommender.py` - 策略推荐
- `core/system_monitor.py` - 系统监控

### 已删除的模块
由于语法错误严重且难以修复，以下模块已被删除：
- 高级算法模块
- 机器学习预测模块
- 实时数据流模块
- 投资组合优化模块
- 高级风险管理模块
- 性能监控模块
- 大部分工具脚本

## 开发建议

1. **重新开发删除的模块**: 如需要高级功能，建议重新开发相关模块
2. **测试覆盖**: 添加单元测试和集成测试
3. **文档完善**: 补充API文档和用户手册
4. **性能优化**: 优化数据处理和计算性能
5. **功能扩展**: 根据需求添加新的交易策略和分析工具

## 故障排除

### 常见问题

1. **端口占用**: 修改配置文件中的端口设置
2. **依赖缺失**: 运行 `pip install -r requirements.txt`
3. **数据库问题**: 删除 `data/databases/` 目录重新初始化
4. **前端构建失败**: 在 `frontend/` 目录运行 `npm install`

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看交易系统日志
tail -f logs/trading_system.log
```

## 联系信息

如有问题或建议，请查看项目文档或提交Issue。

---

**注意**: 本系统仅用于学习和研究目的，不构成投资建议。实际交易请谨慎操作。
