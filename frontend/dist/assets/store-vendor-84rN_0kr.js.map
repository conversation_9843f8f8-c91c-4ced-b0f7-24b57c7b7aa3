{"version": 3, "file": "store-vendor-84rN_0kr.js", "sources": ["../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/use-sync-external-store/shim/index.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../node_modules/use-sync-external-store/shim/with-selector.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../node_modules/zustand/esm/index.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\nexport { create, react as default, useStore };\n"], "names": ["createStoreImpl", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "Object", "is", "previousState", "assign", "for<PERSON>ach", "listener", "getState", "api", "getInitialState", "initialState", "subscribe", "add", "delete", "destroy", "clear", "shimModule", "exports", "checkIfSnapshotChanged", "inst", "latestGetSnapshot", "getSnapshot", "value", "nextValue", "objectIs", "error", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "React", "require$$0", "x", "y", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "didWarnOld18Alpha", "didWarnUncachedGetSnapshot", "shim", "window", "document", "createElement", "startTransition", "cachedValue", "forceUpdate", "useSyncExternalStoreShim_development", "useSyncExternalStore", "registerInternalModuleStop", "withSelectorModule", "require$$1", "useRef", "useMemo", "withSelector_development", "useSyncExternalStoreWithSelector", "getServerSnapshot", "selector", "isEqual", "instRef", "current", "hasValue", "memoizedSelector", "nextSnapshot", "hasMemo", "memoizedSnapshot", "currentSelection", "memoizedSelection", "nextSelection", "maybeGetServerSnapshot", "ReactExports", "useSyncExternalStoreExports", "didWarnAboutEqualityFn", "identity", "arg", "createImpl", "createStore", "useBoundStore", "equalityFn", "__vite_import_meta_env__", "slice", "getServerState", "useStore", "create"], "mappings": "mEAAMA,EAAmBC,IACvB,IAAIC,EACJ,MAAMC,MAAgCC,IAChCC,EAAW,CAACC,EAASC,KACzB,MAAMC,EAA+B,mBAAZF,EAAyBA,EAAQJ,GAASI,EACnE,IAAKG,OAAOC,GAAGF,EAAWN,GAAQ,CAChC,MAAMS,EAAgBT,EACtBA,GAAoB,MAAXK,EAAkBA,EAA+B,iBAAdC,GAAwC,OAAdA,GAAsBA,EAAYC,OAAOG,OAAO,CAAA,EAAIV,EAAOM,GACjIL,EAAUU,QAASC,GAAaA,EAASZ,EAAOS,GAClD,GAEII,EAAW,IAAMb,EAcjBc,EAAM,CAAEX,WAAUU,WAAUE,gBAbV,IAAMC,EAaqBC,UAZhCL,IACjBX,EAAUiB,IAAIN,GACP,IAAMX,EAAUkB,OAAOP,IAU8BQ,QAR9C,KAMdnB,EAAUoB,UAGNL,EAAehB,EAAQD,EAAYI,EAAUU,EAAUC,GAC7D,OAAOA,mFCtBPQ,EAAAC;;;;;;;;;;ACLF,WAyDI,SAASC,EAAuBC,GAC9B,IAAIC,EAAoBD,EAAKE,YAC7BF,EAAOA,EAAKG,MACZ,IACE,IAAIC,EAAYH,IAChB,OAAQI,EAASL,EAAMI,EAAS,OACzBE,GACP,OAAO,CAAA,CACT,CAKF,oBAAuBC,gCACrB,mBACSA,+BAA+BC,6BACxCD,+BAA+BC,4BAA4BC,SAC7D,IAAIC,EAAQC,IACVN,EAAW,mBAAsBvB,OAAOC,GAAKD,OAAOC,GA9DtD,SAAY6B,EAAGC,GACb,OAAQD,IAAMC,IAAM,IAAMD,GAAK,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,CAAA,EA8DtEC,EAAWJ,EAAMI,SACjBC,EAAYL,EAAMK,UAClBC,EAAkBN,EAAMM,gBACxBC,EAAgBP,EAAMO,cACtBC,GAAoB,EACpBC,GAA6B,EAC7BC,EACE,oBAAuBC,aACvB,IAAuBA,OAAOC,eAC9B,IAAuBD,OAAOC,SAASC,cAlB3C,SAAgC/B,EAAWU,GACzC,OAAOA,GAAY,EApDrB,SAAgCV,EAAWU,GACzCgB,QACE,IAAWR,EAAMc,kBACfN,GAAoB,GAIxB,IAAIf,EAAQD,IACZ,IAAKiB,EAA4B,CAC/B,IAAIM,EAAcvB,IAClBG,EAASF,EAAOsB,KAIbN,GAA6B,EAAA,CAKlC,IAAInB,GAHJyB,EAAcX,EAAS,CACrBd,KAAM,CAAEG,QAAcD,kBAED,GAAGF,KACxB0B,EAAcD,EAAY,GAmB5B,OAlBAT,EACE,WACEhB,EAAKG,MAAQA,EACbH,EAAKE,YAAcA,EACnBH,EAAuBC,IAAS0B,EAAY,CAAE1B,QAAY,EAE5D,CAACR,EAAWW,EAAOD,IAErBa,EACE,WAEE,OADAhB,EAAuBC,IAAS0B,EAAY,CAAE1B,SACvCR,EAAU,WACfO,EAAuBC,IAAS0B,EAAY,CAAE1B,QAAY,EAC3D,EAEH,CAACR,IAEHyB,EAAcd,GACPA,CAAA,EAiCTwB,EAAAC,0BACE,IAAWlB,EAAMkB,qBAAuBlB,EAAMkB,qBAAuBR,EACvE,oBAAuBb,gCACrB,mBACSA,+BAA+BsB,4BACxCtB,+BAA+BsB,2BAA2BpB,QAC9D,CA9FF,qCCKEqB,EAAAhC;;;;;;;;;;ACLF,WAgBI,oBAAuBS,gCACrB,mBACSA,+BAA+BC,6BACxCD,+BAA+BC,4BAA4BC,SAC7D,IAAIC,EAAQC,IACVS,EAAOW,IACP1B,EAAW,mBAAsBvB,OAAOC,GAAKD,OAAOC,GATtD,SAAY6B,EAAGC,GACb,OAAQD,IAAMC,IAAM,IAAMD,GAAK,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,CAAA,EAStEe,EAAuBR,EAAKQ,qBAC5BI,EAAStB,EAAMsB,OACfjB,EAAYL,EAAMK,UAClBkB,EAAUvB,EAAMuB,QAChBhB,EAAgBP,EAAMO,cACxBiB,EAAAC,iCAA2C,SACzC3C,EACAU,EACAkC,EACAC,EACAC,GAEA,IAAIC,EAAUP,EAAO,MACrB,GAAI,OAASO,EAAQC,QAAS,CAC5B,IAAIxC,EAAO,CAAEyC,UAAU,EAAItC,MAAO,MAClCoC,EAAQC,QAAUxC,CAAA,QACNuC,EAAQC,QACtBD,EAAUN,EACR,WACE,SAASS,EAAiBC,GACxB,IAAKC,EAAS,CAIZ,GAHAA,GAAU,EACVC,EAAmBF,EACnBA,EAAeN,EAASM,QACpB,IAAWL,GAAWtC,EAAKyC,SAAU,CACvC,IAAIK,EAAmB9C,EAAKG,MAC5B,GAAImC,EAAQQ,EAAkBH,GAC5B,OAAQI,EAAoBD,CAAA,CAEhC,OAAQC,EAAoBJ,CAAA,CAG9B,GADAG,EAAmBC,EACf1C,EAASwC,EAAkBF,GAC7B,OAAOG,EACT,IAAIE,EAAgBX,EAASM,GAC7B,YAAI,IAAWL,GAAWA,EAAQQ,EAAkBE,IAC1CH,EAAmBF,EAAeG,IAC5CD,EAAmBF,EACXI,EAAoBC,EAAA,CAE9B,IACEH,EACAE,EAFEH,GAAU,EAGZK,OACE,IAAWb,EAAoB,KAAOA,EAC1C,MAAO,CACL,WACE,OAAOM,EAAiBxC,IAAa,EAEvC,OAAS+C,OACL,EACA,WACE,OAAOP,EAAiBO,IAAwB,EAExD,EAEF,CAAC/C,EAAakC,EAAmBC,EAAUC,IAE7C,IAAInC,EAAQyB,EAAqBpC,EAAW+C,EAAQ,GAAIA,EAAQ,IAShE,OARAxB,EACE,WACEf,EAAKyC,UAAW,EAChBzC,EAAKG,MAAQA,CAAA,EAEf,CAACA,IAEHc,EAAcd,GACPA,CAAA,EAET,oBAAuBI,gCACrB,mBACSA,+BAA+BsB,4BACxCtB,+BAA+BsB,2BAA2BpB,QAC9D,CAhGF,0BCKMQ,cAAEA,GAAkBiC,GACpBf,iCAAEA,GAAqCgB,EAC7C,IAAIC,GAAyB,EAC7B,MAAMC,EAAYC,GAAQA,EAkB1B,MAAMC,EAAcjF,IAMlB,MAAMe,EAA6B,mBAAhBf,ELHD,CAACA,GAAgBA,EAAcD,EAAgBC,GAAeD,EKGhCmF,CAAYlF,GAAeA,EACrEmF,EAAgB,CAACpB,EAAUqB,IAxBnC,SAAkBrE,EAAKgD,EAAWgB,EAAUK,GACgB,gBAArDC,EAAkB,kBAAuB,IAA4BD,IAAeN,IAIvFA,GAAyB,GAE3B,MAAMQ,EAAQzB,EACZ9C,EAAIG,UACJH,EAAID,SACJC,EAAIwE,gBAAkBxE,EAAIC,gBAC1B+C,EACAqB,GAGF,OADAzC,EAAc2C,GACPA,CACT,CAQkDE,CAASzE,EAAKgD,EAAUqB,GAExE,OADA5E,OAAOG,OAAOwE,EAAepE,GACtBoE,GAEHM,EAAUzF,GAAwDiF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}