{"version": 3, "file": "Dashboard-CykE3o7k.js", "sources": ["../../src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd';\nimport { \n  BarChartOutlined, \n  DollarOutlined, \n  TrophyOutlined,\n  RiseOutlined,\n  PlayCircleOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAppStore } from '@/store';\nimport { useApi } from '@/hooks/useApi';\nimport { formatPercentage, formatNumber } from '@/utils/format';\n\nconst { Title, Paragraph } = Typography;\n\nconst Dashboard: React.FC = () => {\n  const navigate = useNavigate();\n  const { strategies, backtestResults } = useAppStore();\n  const { loadStrategies, loadBacktestHistory } = useApi();\n\n  useEffect(() => {\n    loadStrategies();\n    loadBacktestHistory();\n  }, [loadStrategies, loadBacktestHistory]);\n\n  // Calculate summary statistics\n  const totalStrategies = strategies.length;\n  const totalBacktests = backtestResults.length;\n  const avgReturn = backtestResults.length > 0 \n    ? backtestResults.reduce((sum, result) => sum + result.total_return, 0) / backtestResults.length\n    : 0;\n  const bestReturn = backtestResults.length > 0\n    ? Math.max(...backtestResults.map(result => result.total_return))\n    : 0;\n\n  // Recent backtest results (last 5)\n  const recentResults = backtestResults.slice(0, 5);\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>Trading System Dashboard</Title>\n        <Paragraph type=\"secondary\">\n          Monitor your trading strategies and backtest performance\n        </Paragraph>\n      </div>\n\n      {/* Summary Statistics */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"Total Strategies\"\n              value={totalStrategies}\n              prefix={<BarChartOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"Total Backtests\"\n              value={totalBacktests}\n              prefix={<DollarOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"Average Return\"\n              value={formatPercentage(avgReturn)}\n              prefix={<RiseOutlined />}\n              valueStyle={{ color: avgReturn >= 0 ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"Best Return\"\n              value={formatPercentage(bestReturn)}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        {/* Quick Actions */}\n        <Col xs={24} lg={8}>\n          <Card title=\"Quick Actions\" style={{ height: '300px' }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"middle\">\n              <Button \n                type=\"primary\" \n                icon={<BarChartOutlined />}\n                block\n                onClick={() => navigate('/strategies')}\n              >\n                Manage Strategies\n              </Button>\n              <Button \n                type=\"default\" \n                icon={<PlayCircleOutlined />}\n                block\n                onClick={() => navigate('/backtest')}\n              >\n                Run Backtest\n              </Button>\n              <Button \n                type=\"default\" \n                block\n                onClick={() => navigate('/settings')}\n              >\n                System Settings\n              </Button>\n            </Space>\n          </Card>\n        </Col>\n\n        {/* Recent Strategies */}\n        <Col xs={24} lg={8}>\n          <Card \n            title=\"Recent Strategies\" \n            style={{ height: '300px' }}\n            extra={\n              <Button type=\"link\" onClick={() => navigate('/strategies')}>\n                View All\n              </Button>\n            }\n          >\n            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>\n              {strategies.slice(0, 5).map((strategy) => (\n                <div \n                  key={strategy.id}\n                  style={{ \n                    padding: '8px 0', \n                    borderBottom: '1px solid #f0f0f0',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                  }}\n                >\n                  <div>\n                    <div style={{ fontWeight: 500 }}>{strategy.name}</div>\n                    <div style={{ fontSize: '12px', color: '#8c8c8c' }}>\n                      {strategy.type}\n                    </div>\n                  </div>\n                </div>\n              ))}\n              {strategies.length === 0 && (\n                <div style={{ textAlign: 'center', color: '#8c8c8c', padding: '20px' }}>\n                  No strategies yet\n                </div>\n              )}\n            </div>\n          </Card>\n        </Col>\n\n        {/* Recent Backtest Results */}\n        <Col xs={24} lg={8}>\n          <Card \n            title=\"Recent Backtests\" \n            style={{ height: '300px' }}\n            extra={\n              <Button type=\"link\" onClick={() => navigate('/backtest')}>\n                View All\n              </Button>\n            }\n          >\n            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>\n              {recentResults.map((result) => (\n                <div \n                  key={result.id}\n                  style={{ \n                    padding: '8px 0', \n                    borderBottom: '1px solid #f0f0f0',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                  }}\n                >\n                  <div>\n                    <div style={{ fontWeight: 500 }}>{result.symbol}</div>\n                    <div style={{ fontSize: '12px', color: '#8c8c8c' }}>\n                      {formatNumber(result.trade_count)} trades\n                    </div>\n                  </div>\n                  <div style={{ textAlign: 'right' }}>\n                    <div \n                      style={{ \n                        color: result.total_return >= 0 ? '#52c41a' : '#ff4d4f',\n                        fontWeight: 500,\n                      }}\n                    >\n                      {formatPercentage(result.total_return)}\n                    </div>\n                  </div>\n                </div>\n              ))}\n              {backtestResults.length === 0 && (\n                <div style={{ textAlign: 'center', color: '#8c8c8c', padding: '20px' }}>\n                  No backtest results yet\n                </div>\n              )}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "names": ["Title", "Paragraph", "Typography", "Dashboard", "navigate", "useNavigate", "strategies", "backtestResults", "useAppStore", "loadStrategies", "loadBacktestHistory", "useApi", "useEffect", "totalStrategies", "length", "totalBacktests", "avgReturn", "reduce", "sum", "result", "total_return", "bestReturn", "Math", "max", "map", "recentResults", "slice", "children", "jsxDEV", "style", "marginBottom", "level", "fileName", "lineNumber", "columnNumber", "this", "type", "Row", "gutter", "Col", "xs", "sm", "lg", "Card", "Statistic", "title", "value", "prefix", "BarChartOutlined", "valueStyle", "color", "DollarOutlined", "formatPercentage", "RiseOutlined", "TrophyOutlined", "height", "Space", "direction", "width", "size", "<PERSON><PERSON>", "icon", "block", "onClick", "PlayCircleOutlined", "extra", "maxHeight", "overflowY", "strategy", "padding", "borderBottom", "display", "justifyContent", "alignItems", "fontWeight", "name", "fontSize", "id", "textAlign", "symbol", "formatNumber", "trade_count"], "mappings": "mXAcA,MAAMA,MAAEA,EAAAC,UAAOA,GAAcC,EAEvBC,EAAsB,KAC1B,MAAMC,EAAWC,KACXC,WAAEA,EAAAC,gBAAYA,GAAoBC,KAClCC,eAAEA,EAAAC,oBAAgBA,GAAwBC,IAEhDC,EAAAA,UAAU,KACRH,IACAC,KACC,CAACD,EAAgBC,IAGpB,MAAMG,EAAkBP,EAAWQ,OAC7BC,EAAiBR,EAAgBO,OACjCE,EAAYT,EAAgBO,OAAS,EACvCP,EAAgBU,OAAO,CAACC,EAAKC,IAAWD,EAAMC,EAAOC,aAAc,GAAKb,EAAgBO,OACxF,EACEO,EAAad,EAAgBO,OAAS,EACxCQ,KAAKC,OAAOhB,EAAgBiB,IAAIL,GAAUA,EAAOC,eACjD,EAGEK,EAAgBlB,EAAgBmB,MAAM,EAAG,GAE/C,gBACG,MAAA,CACCC,SAAA,CAAAC,EAAAA,OAAC,MAAA,CAAIC,MAAO,CAAEC,aAAc,QAC1BH,SAAA,CAAAC,SAAC5B,EAAA,CAAM+B,MAAO,EAAGJ,SAAA,iCAAjB,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,GACAP,SAAC3B,EAAA,CAAUmC,KAAK,YAAYT,SAAA,iEAA5B,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,GAQAP,EAAAA,OAACS,EAAA,CAAIC,OAAQ,CAAC,GAAI,IAAKT,MAAO,CAAEC,aAAc,QAC5CH,SAAA,GAAAC,OAACW,EAAA,CAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,EACvBf,kBAACgB,EAAA,CACChB,SAAAC,EAAAA,OAACgB,EAAA,CACCC,MAAM,mBACNC,MAAOjC,EACPkC,gBAASC,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAhB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,GACRc,WAAY,CAAEC,MAAO,iBAJvB,GAAA,EAAA,CAAAlB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,KAUAP,OAACW,GAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,EACvBf,SAAAC,EAAAA,OAACe,EAAA,CACChB,SAAAC,EAAAA,OAACgB,EAAA,CACCC,MAAM,kBACNC,MAAO/B,EACPgC,gBAASI,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAnB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,GACRc,WAAY,CAAEC,MAAO,iBAJvB,GAAA,EAAA,CAAAlB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,KAUAP,OAACW,GAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,EACvBf,SAAAC,EAAAA,OAACe,EAAA,CACChB,SAAAC,EAAAA,OAACgB,EAAA,CACCC,MAAM,iBACNC,MAAOM,EAAiBpC,GACxB+B,gBAASM,EAAA,CAAA,OAAD,GAAA,EAAA,CAAArB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,GACRc,WAAY,CAAEC,MAAOlC,GAAa,EAAI,UAAY,iBAJpD,GAAA,EAAA,CAAAgB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,KAUAP,OAACW,GAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,EACvBf,SAAAC,EAAAA,OAACe,EAAA,CACChB,SAAAC,EAAAA,OAACgB,EAAA,CACCC,MAAM,cACNC,MAAOM,EAAiB/B,GACxB0B,gBAASO,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAtB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,GACRc,WAAY,CAAEC,MAAO,iBAJvB,GAAA,EAAA,CAAAlB,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,UA/BF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,YA2CCE,EAAA,CAAIC,OAAQ,CAAC,GAAI,IAEhBX,SAAA,CAAAC,EAAAA,OAACW,EAAA,CAAIC,GAAI,GAAIE,GAAI,EACff,kBAACgB,EAAA,CAAKE,MAAM,gBAAgBhB,MAAO,CAAE0B,OAAQ,SAC3C5B,SAAAC,EAAAA,OAAC4B,EAAA,CAAMC,UAAU,WAAW5B,MAAO,CAAE6B,MAAO,QAAUC,KAAK,SACzDhC,SAAA,CAAAC,EAAAA,OAACgC,EAAA,CACCxB,KAAK,UACLyB,cAAOb,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAhB,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN2B,OAAK,EACLC,QAAS,IAAM3D,EAAS,eACzBuB,SAAA,0BALD,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,GAQAP,EAAAA,OAACgC,EAAA,CACCxB,KAAK,UACLyB,cAAOG,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAhC,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN2B,OAAK,EACLC,QAAS,IAAM3D,EAAS,aACzBuB,SAAA,qBALD,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAQAP,EAAAA,OAACgC,EAAA,CACCxB,KAAK,UACL0B,OAAK,EACLC,QAAS,IAAM3D,EAAS,aACzBuB,SAAA,wBAJD,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAjBF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,GA+BAP,EAAAA,OAACW,EAAA,CAAIC,GAAI,GAAIE,GAAI,EACff,SAAAC,EAAAA,OAACe,EAAA,CACCE,MAAM,oBACNhB,MAAO,CAAE0B,OAAQ,SACjBU,MACErC,EAAAA,OAACgC,EAAA,CAAOxB,KAAK,OAAO2B,QAAS,IAAM3D,EAAS,eAAgBuB,SAAA,iBAA5D,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAKFR,SAAAC,EAAAA,OAAC,OAAIC,MAAO,CAAEqC,UAAW,QAASC,UAAW,QAC1CxC,SAAA,CAAArB,EAAWoB,MAAM,EAAG,GAAGF,IAAK4C,GAC3BxC,EAAAA,OAAC,MAAA,CAECC,MAAO,CACLwC,QAAS,QACTC,aAAc,oBACdC,QAAS,OACTC,eAAgB,gBAChBC,WAAY,UAGd9C,kBAAC,MAAA,CACCA,SAAA,CAAAC,EAAAA,OAAC,OAAIC,MAAO,CAAE6C,WAAY,KAAQ/C,WAASgD,WAA3C,GAAA,EAAA,CAAA3C,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,KACAP,OAAC,MAAA,CAAIC,MAAO,CAAE+C,SAAU,OAAQ1B,MAAO,WACpCvB,SAAAyC,EAAShC,WADZ,GAAA,EAAA,CAAAJ,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,IATKiC,EAASS,IADhB,EAAA,CAAA7C,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,IAkBqB,IAAtB7B,EAAWQ,QACVc,EAAAA,OAAC,OAAIC,MAAO,CAAEiD,UAAW,SAAU5B,MAAO,UAAWmB,QAAS,QAAU1C,SAAA,0BAAxE,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,UArBJ,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,SATF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAwCAP,EAAAA,OAACW,EAAA,CAAIC,GAAI,GAAIE,GAAI,EACff,SAAAC,EAAAA,OAACe,EAAA,CACCE,MAAM,mBACNhB,MAAO,CAAE0B,OAAQ,SACjBU,MACErC,EAAAA,OAACgC,EAAA,CAAOxB,KAAK,OAAO2B,QAAS,IAAM3D,EAAS,aAAcuB,SAAA,iBAA1D,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAKFR,SAAAC,EAAAA,OAAC,OAAIC,MAAO,CAAEqC,UAAW,QAASC,UAAW,QAC1CxC,SAAA,CAAAF,EAAcD,IAAKL,GAClBS,EAAAA,OAAC,MAAA,CAECC,MAAO,CACLwC,QAAS,QACTC,aAAc,oBACdC,QAAS,OACTC,eAAgB,gBAChBC,WAAY,UAGd9C,SAAA,CAAAC,SAAC,MAAA,CACCD,SAAA,CAAAC,EAAAA,OAAC,OAAIC,MAAO,CAAE6C,WAAY,KAAQ/C,WAAOoD,aAAzC,GAAA,EAAA,CAAA/C,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAP,SAAC,OAAIC,MAAO,CAAE+C,SAAU,OAAQ1B,MAAO,WACpCvB,SAAA,CAAAqD,EAAa7D,EAAO8D,aAAa,iBADpC,GAAA,EAAA,CAAAjD,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,YAMC,MAAA,CAAIN,MAAO,CAAEiD,UAAW,SACvBnD,SAAAC,EAAAA,OAAC,MAAA,CACCC,MAAO,CACLqB,MAAO/B,EAAOC,cAAgB,EAAI,UAAY,UAC9CsD,WAAY,KAGb/C,SAAAyB,EAAiBjC,EAAOC,oBAN3B,GAAA,EAAA,CAAAY,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,KAfKhB,EAAO0D,IADd,EAAA,CAAA7C,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,IA4B0B,IAA3B5B,EAAgBO,QACfc,EAAAA,OAAC,OAAIC,MAAO,CAAEiD,UAAW,SAAU5B,MAAO,UAAWmB,QAAS,QAAU1C,SAAA,gCAAxE,GAAA,EAAA,CAAAK,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,UA/BJ,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,SATF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,IAAAC,aAAA,QAAAC,UAzEF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC,UApDF,GAAA,EAAA,CAAAH,SAAA,oFAAAC,WAAA,GAAAC,aAAA,QAAAC"}