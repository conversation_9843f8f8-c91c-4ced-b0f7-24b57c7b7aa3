import{e as t,g as e,R as n}from"./react-vendor-eITpvC6N.js";const r=t=>{let e;const n=new Set,r=(t,r)=>{const o="function"==typeof t?t(e):t;if(!Object.is(o,e)){const t=e;e=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},e,o),n.forEach(n=>n(e,t))}},o=()=>e,u={setState:r,getState:o,getInitialState:()=>_,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{n.clear()}},_=e=t(r,o,u);return u};var o,u,_,i,a={exports:{}},s={},c={exports:{}},O={};function f(){return u||(u=1,c.exports=(o||(o=1,
/**
   * @license React
   * use-sync-external-store-shim.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
function(){function e(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!r(t,n)}catch(o){return!0}}"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var n=t(),r="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=n.useState,u=n.useEffect,_=n.useLayoutEffect,i=n.useDebugValue,a=!1,s=!1,c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,c){a||void 0===n.startTransition||(a=!0);var O=c();if(!s){var f=c();r(O,f)||(s=!0)}var l=(f=o({inst:{value:O,getSnapshot:c}}))[0].inst,S=f[1];return _(function(){l.value=O,l.getSnapshot=c,e(l)&&S({inst:l})},[t,O,c]),u(function(){return e(l)&&S({inst:l}),t(function(){e(l)&&S({inst:l})})},[t]),i(O),O};O.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c,"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}()),O)),c.exports}const l=e((i||(i=1,a.exports=(_||(_=1,
/**
   * @license React
   * use-sync-external-store-shim/with-selector.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
function(){"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var e=t(),n=f(),r="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=n.useSyncExternalStore,u=e.useRef,_=e.useEffect,i=e.useMemo,a=e.useDebugValue;s.useSyncExternalStoreWithSelector=function(t,e,n,s,c){var O=u(null);if(null===O.current){var f={hasValue:!1,value:null};O.current=f}else f=O.current;O=i(function(){function t(t){if(!_){if(_=!0,o=t,t=s(t),void 0!==c&&f.hasValue){var e=f.value;if(c(e,t))return u=e}return u=t}if(e=u,r(o,t))return e;var n=s(t);return void 0!==c&&c(e,n)?(o=t,e):(o=t,u=n)}var o,u,_=!1,i=void 0===n?null:n;return[function(){return t(e())},null===i?void 0:function(){return t(i())}]},[e,n,s,c]);var l=o(t,O[0],O[1]);return _(function(){f.hasValue=!0,f.value=l},[l]),a(l),l},"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}()),s)),a.exports)),S={},{useDebugValue:d}=n,{useSyncExternalStoreWithSelector:E}=l;let L=!1;const p=t=>t;const v=t=>{const e="function"==typeof t?(t=>t?r(t):r)(t):t,n=(t,n)=>function(t,e=p,n){"production"!==(S?"production":void 0)&&n&&!L&&(L=!0);const r=E(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return d(r),r}(e,t,n);return Object.assign(n,e),n},T=t=>v;export{T as c};
//# sourceMappingURL=store-vendor-84rN_0kr.js.map
