{"version": 3, "file": "Strategies-BcSnHVxN.js", "sources": ["../../src/pages/Strategies.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { \n  Card, \n  Table, \n  Button, \n  Space, \n  Modal, \n  Form, \n  Input, \n  Select, \n  InputNumber,\n  Typography,\n  Popconfirm,\n  message,\n} from 'antd';\nimport { PlusOutlined, DeleteOutlined, DownloadOutlined } from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { useAppStore } from '@/store';\nimport { useApi } from '@/hooks/useApi';\nimport { useKeyboardShortcuts, COMMON_SHORTCUTS } from '@/hooks/useKeyboardShortcuts';\nimport { formatDate, formatStrategyType } from '@/utils/format';\nimport { validateStrategyName, validateStrategyParameters } from '@/utils/validation';\nimport { exportStrategiesToCSV } from '@/utils/export';\nimport type { Strategy, StrategyRequest } from '@/types';\n\nconst { Title } = Typography;\nconst { Option } = Select;\n\nconst Strategies: React.FC = () => {\n  const { strategies, loading } = useAppStore();\n  const { loadStrategies, createStrategy, deleteStrategy } = useApi();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [selectedStrategyType, setSelectedStrategyType] = useState<string>('');\n\n  useEffect(() => {\n    loadStrategies();\n  }, [loadStrategies]);\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts([\n    {\n      ...COMMON_SHORTCUTS.CREATE_STRATEGY,\n      callback: () => setIsModalVisible(true),\n    },\n    {\n      ...COMMON_SHORTCUTS.REFRESH,\n      callback: () => loadStrategies(),\n    },\n    {\n      ...COMMON_SHORTCUTS.EXPORT,\n      callback: () => strategies.length > 0 && exportStrategiesToCSV(strategies),\n    },\n  ]);\n\n  const handleCreateStrategy = async (values: any) => {\n    const nameError = validateStrategyName(values.name);\n    if (nameError) {\n      message.error(nameError);\n      return;\n    }\n\n    const paramError = validateStrategyParameters(values.type, values.parameters || {});\n    if (paramError) {\n      message.error(paramError);\n      return;\n    }\n\n    const request: StrategyRequest = {\n      name: values.name,\n      type: values.type,\n      parameters: values.parameters || {},\n    };\n\n    const result = await createStrategy(request);\n    if (result) {\n      setIsModalVisible(false);\n      form.resetFields();\n      setSelectedStrategyType('');\n    }\n  };\n\n  const handleDeleteStrategy = async (id: string) => {\n    await deleteStrategy(id);\n  };\n\n  const renderParameterInputs = () => {\n    switch (selectedStrategyType) {\n      case 'moving_average':\n        return (\n          <>\n            <Form.Item\n              name={['parameters', 'short_window']}\n              label=\"Short Window\"\n              rules={[{ required: true, message: 'Please input short window!' }]}\n            >\n              <InputNumber min={1} max={100} placeholder=\"e.g., 10\" />\n            </Form.Item>\n            <Form.Item\n              name={['parameters', 'long_window']}\n              label=\"Long Window\"\n              rules={[{ required: true, message: 'Please input long window!' }]}\n            >\n              <InputNumber min={1} max={200} placeholder=\"e.g., 30\" />\n            </Form.Item>\n          </>\n        );\n      \n      case 'rsi':\n        return (\n          <>\n            <Form.Item\n              name={['parameters', 'period']}\n              label=\"RSI Period\"\n              rules={[{ required: true, message: 'Please input RSI period!' }]}\n            >\n              <InputNumber min={1} max={100} placeholder=\"e.g., 14\" />\n            </Form.Item>\n            <Form.Item\n              name={['parameters', 'overbought']}\n              label=\"Overbought Level\"\n              rules={[{ required: true, message: 'Please input overbought level!' }]}\n            >\n              <InputNumber min={50} max={100} placeholder=\"e.g., 70\" />\n            </Form.Item>\n            <Form.Item\n              name={['parameters', 'oversold']}\n              label=\"Oversold Level\"\n              rules={[{ required: true, message: 'Please input oversold level!' }]}\n            >\n              <InputNumber min={0} max={50} placeholder=\"e.g., 30\" />\n            </Form.Item>\n          </>\n        );\n      \n      default:\n        return null;\n    }\n  };\n\n  const columns: ColumnsType<Strategy> = [\n    {\n      title: 'Name',\n      dataIndex: 'name',\n      key: 'name',\n      sorter: (a, b) => a.name.localeCompare(b.name),\n    },\n    {\n      title: 'Type',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type: string) => formatStrategyType(type),\n      filters: [\n        { text: 'Moving Average', value: 'moving_average' },\n        { text: 'RSI', value: 'rsi' },\n      ],\n      onFilter: (value, record) => record.type === value,\n    },\n    {\n      title: 'Parameters',\n      dataIndex: 'parameters',\n      key: 'parameters',\n      render: (parameters: Record<string, any>) => (\n        <div>\n          {Object.entries(parameters).map(([key, value]) => (\n            <div key={key} style={{ fontSize: '12px' }}>\n              {key}: {value}\n            </div>\n          ))}\n        </div>\n      ),\n    },\n    {\n      title: 'Created',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (date: string) => formatDate(date),\n      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Popconfirm\n            title=\"Are you sure you want to delete this strategy?\"\n            onConfirm={() => handleDeleteStrategy(record.id)}\n            okText=\"Yes\"\n            cancelText=\"No\"\n          >\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />}\n              size=\"small\"\n            />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '24px',\n      }}>\n        <div>\n          <Title level={2}>Trading Strategies</Title>\n          <p style={{ color: '#8c8c8c', margin: 0 }}>\n            Manage your trading strategies and their parameters\n          </p>\n        </div>\n        <Space>\n          {strategies.length > 0 && (\n            <Button \n              icon={<DownloadOutlined />}\n              onClick={() => exportStrategiesToCSV(strategies)}\n            >\n              Export CSV\n            </Button>\n          )}\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={() => setIsModalVisible(true)}\n          >\n            Create Strategy\n          </Button>\n        </Space>\n      </div>\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={strategies}\n          rowKey=\"id\"\n          loading={loading.strategies}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} strategies`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title=\"Create New Strategy\"\n        open={isModalVisible}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setSelectedStrategyType('');\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleCreateStrategy}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"Strategy Name\"\n            rules={[{ required: true, message: 'Please input strategy name!' }]}\n          >\n            <Input placeholder=\"Enter strategy name\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"type\"\n            label=\"Strategy Type\"\n            rules={[{ required: true, message: 'Please select strategy type!' }]}\n          >\n            <Select \n              placeholder=\"Select strategy type\"\n              onChange={setSelectedStrategyType}\n            >\n              <Option value=\"moving_average\">Moving Average</Option>\n              <Option value=\"rsi\">RSI</Option>\n            </Select>\n          </Form.Item>\n\n          {renderParameterInputs()}\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setSelectedStrategyType('');\n              }}>\n                Cancel\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading.strategies}>\n                Create Strategy\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Strategies;"], "names": ["Title", "Typography", "Option", "Select", "Strategies", "strategies", "loading", "useAppStore", "loadStrategies", "createStrategy", "deleteStrategy", "useApi", "isModalVisible", "setIsModalVisible", "useState", "form", "Form", "useForm", "selectedStrategyType", "setSelectedStrategyType", "useEffect", "useKeyboardShortcuts", "COMMON_SHORTCUTS", "CREATE_STRATEGY", "callback", "REFRESH", "EXPORT", "length", "exportStrategiesToCSV", "columns", "title", "dataIndex", "key", "sorter", "a", "b", "name", "localeCompare", "render", "type", "formatStrategyType", "filters", "text", "value", "onFilter", "record", "parameters", "jsxDEV", "children", "Object", "entries", "map", "style", "fontSize", "fileName", "lineNumber", "columnNumber", "this", "date", "formatDate", "Date", "created_at", "getTime", "_", "Space", "Popconfirm", "onConfirm", "async", "id", "handleDeleteStrategy", "okText", "cancelText", "<PERSON><PERSON>", "danger", "icon", "DeleteOutlined", "size", "display", "justifyContent", "alignItems", "marginBottom", "level", "color", "margin", "DownloadOutlined", "onClick", "PlusOutlined", "Card", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "Modal", "open", "onCancel", "resetFields", "footer", "width", "layout", "onFinish", "values", "nameError", "validateStrategyName", "message", "error", "param<PERSON><PERSON>r", "validateStrategyParameters", "request", "<PERSON><PERSON>", "label", "rules", "required", "Input", "placeholder", "onChange", "Fragment", "InputNumber", "min", "max", "renderParameterInputs", "textAlign", "htmlType"], "mappings": "gdAyBA,MAAMA,MAAEA,GAAUC,GACZC,OAAEA,GAAWC,EAEbC,EAAuB,KAC3B,MAAMC,WAAEA,EAAAC,QAAYA,GAAYC,KAC1BC,eAAEA,EAAAC,eAAgBA,EAAAC,eAAgBA,GAAmBC,KACpDC,EAAgBC,GAAqBC,EAAAA,UAAS,IAC9CC,GAAQC,EAAKC,WACbC,EAAsBC,GAA2BL,EAAAA,SAAiB,IAEzEM,EAAAA,UAAU,KACRZ,KACC,CAACA,IAGJa,EAAqB,CACnB,IACKC,EAAiBC,gBACpBC,SAAU,IAAMX,GAAkB,IAEpC,IACKS,EAAiBG,QACpBD,SAAU,IAAMhB,KAElB,IACKc,EAAiBI,OACpBF,SAAU,IAAMnB,EAAWsB,OAAS,GAAKC,EAAsBvB,MAInE,MAqFMwB,EAAiC,CACrC,CACEC,MAAO,OACPC,UAAW,OACXC,IAAK,OACLC,OAAQ,CAACC,EAAGC,IAAMD,EAAEE,KAAKC,cAAcF,EAAEC,OAE3C,CACEN,MAAO,OACPC,UAAW,OACXC,IAAK,OACLM,OAASC,GAAiBC,EAAmBD,GAC7CE,QAAS,CACP,CAAEC,KAAM,iBAAkBC,MAAO,kBACjC,CAAED,KAAM,MAAOC,MAAO,QAExBC,SAAU,CAACD,EAAOE,IAAWA,EAAON,OAASI,GAE/C,CACEb,MAAO,aACPC,UAAW,aACXC,IAAK,aACLM,OAASQ,GACPC,EAAAA,OAAC,OACEC,SAAAC,OAAOC,QAAQJ,GAAYK,IAAI,EAAEnB,EAAKW,KACrCI,EAAAA,OAAC,OAAcK,MAAO,CAAEC,SAAU,QAC/BL,SAAA,CAAAhB,EAAI,KAAGW,IADAX,GAAV,EAAA,CAAAsB,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFJ,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,IASJ,CACE3B,MAAO,UACPC,UAAW,aACXC,IAAK,aACLM,OAASoB,GAAiBC,EAAWD,GACrCzB,OAAQ,CAACC,EAAGC,IAAM,IAAIyB,KAAK1B,EAAE2B,YAAYC,UAAY,IAAIF,KAAKzB,EAAE0B,YAAYC,WAE9E,CACEhC,MAAO,UACPE,IAAK,UACLM,OAAQ,CAACyB,EAAGlB,aACTmB,EAAA,CACChB,SAAAD,EAAAA,OAACkB,EAAA,CACCnC,MAAM,iDACNoC,UAAW,IAxGQC,OAAOC,UAC5B1D,EAAe0D,IAuGIC,CAAqBxB,EAAOuB,IAC7CE,OAAO,MACPC,WAAW,KAEXvB,SAAAD,EAAAA,OAACyB,EAAA,CACCjC,KAAK,OACLkC,QAAM,EACNC,cAAOC,EAAA,CAAA,OAAD,GAAA,EAAA,CAAArB,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACNmB,KAAK,cAJP,GAAA,EAAA,CAAAtB,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SANF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,KAmBN,gBACG,MAAA,CACCT,SAAA,CAAAD,SAAC,OAAIK,MAAO,CACVyB,QAAS,OACTC,eAAgB,gBAChBC,WAAY,SACZC,aAAc,QAEdhC,SAAA,CAAAD,SAAC,MAAA,CACCC,SAAA,CAAAD,SAAC/C,EAAA,CAAMiF,MAAO,EAAGjC,SAAA,2BAAjB,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAV,EAAAA,OAAC,KAAEK,MAAO,CAAE8B,MAAO,UAAWC,OAAQ,GAAKnC,SAAA,4DAA3C,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,YAMCO,EAAA,CACEhB,SAAA,CAAA3C,EAAWsB,OAAS,GACnBoB,EAAAA,OAACyB,EAAA,CACCE,cAAOU,EAAA,CAAA,OAAD,GAAA,EAAA,CAAA9B,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN4B,QAAS,IAAMzD,EAAsBvB,GACtC2C,SAAA,mBAHD,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOFV,EAAAA,OAACyB,EAAA,CACCjC,KAAK,UACLmC,cAAOY,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAhC,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN4B,QAAS,IAAMxE,GAAkB,GAClCmC,SAAA,wBAJD,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UATF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,UAZF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,YA+BC8B,EAAA,CACCvC,SAAAD,EAAAA,OAACyC,EAAA,CACC3D,UACA4D,WAAYpF,EACZqF,OAAO,KACPpF,QAASA,EAAQD,WACjBsF,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAYC,GAAU,SAASA,sBATnC,GAAA,EAAA,CAAA1C,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAeAV,EAAAA,OAACkD,EAAA,CACCnE,MAAM,sBACNoE,KAAMtF,EACNuF,SAAU,KACRtF,GAAkB,GAClBE,EAAKqF,cACLjF,EAAwB,KAE1BkF,OAAQ,KACRC,MAAO,IAEPtD,SAAAD,EAAAA,OAAC/B,EAAA,CACCD,OACAwF,OAAO,WACPC,SAjNqBrC,MAAOsC,IAClC,MAAMC,EAAYC,EAAqBF,EAAOrE,MAC9C,GAAIsE,EAEF,YADAE,EAAQC,MAAMH,GAIhB,MAAMI,EAAaC,EAA2BN,EAAOlE,KAAMkE,EAAO3D,YAAc,IAChF,GAAIgE,EAEF,YADAF,EAAQC,MAAMC,GAIhB,MAAME,EAA2B,CAC/B5E,KAAMqE,EAAOrE,KACbG,KAAMkE,EAAOlE,KACbO,WAAY2D,EAAO3D,YAAc,CAAA,SAGdrC,EAAeuG,KAElCnG,GAAkB,GAClBE,EAAKqF,cACLjF,EAAwB,MA4LpB6B,SAAA,CAAAD,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAK,OACL8E,MAAM,gBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,gCAEnC5D,WAAAD,OAACsE,EAAA,CAAMC,YAAY,4BAAnB,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAQAV,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAK,OACL8E,MAAM,gBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,iCAEnC5D,SAAAD,EAAAA,OAAC5C,EAAA,CACCmH,YAAY,uBACZC,SAAUpG,EAEV6B,SAAA,CAAAD,SAAC7C,EAAA,CAAOyC,MAAM,iBAAiBK,SAAA,uBAA/B,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAV,SAAC7C,EAAA,CAAOyC,MAAM,MAAMK,SAAA,YAApB,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GA5LsB,MAC5B,OAAQvC,GACN,IAAK,iBACH,OACE6B,EAAAA,OAAAyE,WAAA,CACExE,SAAA,CAAAD,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAM,CAAC,aAAc,gBACrB8E,MAAM,eACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,+BAEnC5D,kBAACyE,EAAA,CAAYC,IAAK,EAAGC,IAAK,IAAKL,YAAY,iBAA3C,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,GAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,GAAAC,aAAA,SAAAC,GAOAV,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAM,CAAC,aAAc,eACrB8E,MAAM,cACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,8BAEnC5D,kBAACyE,EAAA,CAAYC,IAAK,EAAGC,IAAK,IAAKL,YAAY,iBAA3C,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,GAAAC,aAAA,SAAAC,UARF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,GAAAC,aAAA,SAAAC,GAkBJ,IAAK,MACH,OACEV,EAAAA,OAAAyE,WAAA,CACExE,SAAA,CAAAD,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAM,CAAC,aAAc,UACrB8E,MAAM,aACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,6BAEnC5D,kBAACyE,EAAA,CAAYC,IAAK,EAAGC,IAAK,IAAKL,YAAY,iBAA3C,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOAV,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAM,CAAC,aAAc,cACrB8E,MAAM,mBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,mCAEnC5D,kBAACyE,EAAA,CAAYC,IAAK,GAAIC,IAAK,IAAKL,YAAY,iBAA5C,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOAV,EAAAA,OAAC/B,EAAKiG,KAAL,CACC7E,KAAM,CAAC,aAAc,YACrB8E,MAAM,iBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMR,QAAS,iCAEnC5D,kBAACyE,EAAA,CAAYC,IAAK,EAAGC,IAAK,GAAIL,YAAY,iBAA1C,GAAA,EAAA,CAAAhE,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAfF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAyBJ,QACE,OAAO,OAwJJmE,KAED7E,OAAC/B,EAAKiG,KAAL,CAAU7D,MAAO,CAAE4B,aAAc,EAAG6C,UAAW,SAC9C7E,WAAAD,OAACiB,EAAA,CACChB,SAAA,CAAAD,SAACyB,EAAA,CAAOa,QAAS,KACfxE,GAAkB,GAClBE,EAAKqF,cACLjF,EAAwB,KACvB6B,SAAA,eAJH,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOAV,EAAAA,OAACyB,GAAOjC,KAAK,UAAUuF,SAAS,SAASxH,QAASA,EAAQD,WAAY2C,SAAA,wBAAtE,GAAA,EAAA,CAAAM,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UARF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,SAAAC,UA7BF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,SAXF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC,UA/CF,GAAA,EAAA,CAAAH,SAAA,qFAAAC,WAAA,IAAAC,aAAA,QAAAC"}