import{j as e}from"./index-D0Fokv29.js";import{r}from"./react-vendor-eITpvC6N.js";import{u as o,D as s}from"./useApi-edF36RG5.js";import{F as t,T as n,n as i,I as a,H as l,S as c,J as m,B as d,K as u,N as P,y as h,O as N,w as g,Q as j,z as f}from"./ui-vendor-BjEg0IST.js";import"./store-vendor-84rN_0kr.js";import"./utils-vendor-C-1G2k3o.js";const{Title:y,Paragraph:v}=n,{Option:x}=g,b=()=>{const[n]=t.useForm(),[b,p]=r.useState(!1),[S,E]=r.useState(!1),{checkHealth:D}=o(),[U,V]=r.useState({api_url:"http://localhost:8000",initial_capital:s.INITIAL_CAPITAL,commission:s.COMMISSION,refresh_interval:s.REFRESH_INTERVAL,auto_refresh:!0,theme:"light",language:"zh"});r.useEffect(()=>{const e=localStorage.getItem("trading-system-settings");if(e){const r=JSON.parse(e);V(r),n.setFieldsValue(r)}I()},[]);const I=async()=>{try{const e=await D();E(e)}catch(e){E(!1)}};return e.jsxDEV("div",{children:[e.jsxDEV("div",{style:{marginBottom:"24px"},children:[e.jsxDEV(y,{level:2,children:"System Settings"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:109,columnNumber:9},void 0),e.jsxDEV(v,{type:"secondary",children:"Configure your trading system preferences and connection settings"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:110,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:108,columnNumber:7},void 0),e.jsxDEV(i,{children:e.jsxDEV(t,{form:n,layout:"vertical",initialValues:U,onFinish:async e=>{p(!0);try{localStorage.setItem("trading-system-settings",JSON.stringify(e)),V(e),f.success("Settings saved successfully"),e.api_url!==U.api_url&&await I()}catch(r){f.error("Failed to save settings")}finally{p(!1)}},children:[e.jsxDEV(y,{level:4,children:"API Configuration"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:123,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"api_url",label:"API URL",rules:[{required:!0,message:"Please input API URL!"},{type:"url",message:"Please enter a valid URL!"}],extra:"The base URL for the trading system API",children:e.jsxDEV(a,{placeholder:"http://localhost:8000"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:134,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:125,columnNumber:11},void 0),e.jsxDEV("div",{style:{marginBottom:"16px"},children:e.jsxDEV(l,{message:e.jsxDEV(c,{children:[e.jsxDEV("span",{children:"API Status:"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:141,columnNumber:19},void 0),S?e.jsxDEV("span",{style:{color:"#52c41a"},children:[e.jsxDEV(m,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:144,columnNumber:23},void 0)," Connected"]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:143,columnNumber:21},void 0):e.jsxDEV("span",{style:{color:"#ff4d4f"},children:"Disconnected"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:147,columnNumber:21},void 0),e.jsxDEV(d,{type:"link",size:"small",onClick:I,icon:e.jsxDEV(u,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:155,columnNumber:27},void 0),children:"Test Connection"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:151,columnNumber:19},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:140,columnNumber:17},void 0),type:S?"success":"warning",showIcon:!0},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:138,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:137,columnNumber:11},void 0),e.jsxDEV(P,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:166,columnNumber:11},void 0),e.jsxDEV(y,{level:4,children:"Trading Configuration"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:169,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"initial_capital",label:"Default Initial Capital",rules:[{required:!0,message:"Please input initial capital!"}],extra:"Default amount for new backtests",children:e.jsxDEV(h,{style:{width:"100%"},min:1e3,max:1e7,formatter:e=>`$ ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>{const r=Number(e.replace(/\$\s?|(,*)/g,""));return Math.max(1e3,Math.min(1e7,r))}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:177,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:171,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"commission",label:"Default Commission Rate",rules:[{required:!0,message:"Please input commission rate!"}],extra:"Commission rate as a decimal (e.g., 0.001 for 0.1%)",children:e.jsxDEV(h,{style:{width:"100%"},min:0,max:.1,step:1e-4,precision:4},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:195,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:189,columnNumber:11},void 0),e.jsxDEV(P,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:204,columnNumber:11},void 0),e.jsxDEV(y,{level:4,children:"User Interface"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:207,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"auto_refresh",label:"Auto Refresh",valuePropName:"checked",extra:"Automatically refresh data at regular intervals",children:e.jsxDEV(N,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:215,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:209,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"refresh_interval",label:"Refresh Interval (ms)",rules:[{required:!0,message:"Please input refresh interval!"}],extra:"How often to refresh data when auto refresh is enabled",children:e.jsxDEV(h,{style:{width:"100%"},min:1e3,max:6e4,step:1e3},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:224,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:218,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"theme",label:"Theme",extra:"Choose your preferred theme",children:e.jsxDEV(g,{children:[e.jsxDEV(x,{value:"light",children:"Light"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:238,columnNumber:15},void 0),e.jsxDEV(x,{value:"dark",children:"Dark"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:239,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:237,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:232,columnNumber:11},void 0),e.jsxDEV(t.Item,{name:"language",label:"Language",extra:"Choose your preferred language",children:e.jsxDEV(g,{children:[e.jsxDEV(x,{value:"en",children:"English"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:249,columnNumber:15},void 0),e.jsxDEV(x,{value:"zh",children:"中文"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:250,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:248,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:243,columnNumber:11},void 0),e.jsxDEV(P,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:254,columnNumber:11},void 0),e.jsxDEV(t.Item,{style:{marginBottom:0},children:e.jsxDEV(c,{children:[e.jsxDEV(d,{type:"primary",htmlType:"submit",icon:e.jsxDEV(j,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:262,columnNumber:23},void 0),loading:b,children:"Save Settings"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:259,columnNumber:15},void 0),e.jsxDEV(d,{onClick:()=>{const e={api_url:"http://localhost:8000",initial_capital:s.INITIAL_CAPITAL,commission:s.COMMISSION,refresh_interval:s.REFRESH_INTERVAL,auto_refresh:!0,theme:"light",language:"zh"};V(e),n.setFieldsValue(e),f.info("Settings reset to defaults")},children:"Reset to Defaults"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:267,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:258,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:257,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:116,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:115,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Settings.tsx",lineNumber:107,columnNumber:5},void 0)};export{b as default};
//# sourceMappingURL=Settings-B2j9IhFO.js.map
