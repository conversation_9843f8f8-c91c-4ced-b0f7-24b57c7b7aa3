import{u as e,j as o}from"./index-D0Fokv29.js";import{u as r,r as s}from"./react-vendor-eITpvC6N.js";import{u as n}from"./useApi-edF36RG5.js";import{f as t,a}from"./format-DdDemiMk.js";import{T as d,l as c,m as l,n as i,o as m,h,p as P,q as u,r as b,S as j,B as N,s as f}from"./ui-vendor-BjEg0IST.js";import"./store-vendor-84rN_0kr.js";import"./utils-vendor-C-1G2k3o.js";const{Title:y,Paragraph:x}=d,v=()=>{const d=r(),{strategies:v,backtestResults:g}=e(),{loadStrategies:D,loadBacktestHistory:p}=n();s.useEffect(()=>{D(),p()},[D,p]);const V=v.length,E=g.length,U=g.length>0?g.reduce((e,o)=>e+o.total_return,0)/g.length:0,k=g.length>0?Math.max(...g.map(e=>e.total_return)):0,S=g.slice(0,5);return o.jsxDEV("div",{children:[o.jsxDEV("div",{style:{marginBottom:"24px"},children:[o.jsxDEV(y,{level:2,children:"Trading System Dashboard"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:43,columnNumber:9},void 0),o.jsxDEV(x,{type:"secondary",children:"Monitor your trading strategies and backtest performance"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:44,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:42,columnNumber:7},void 0),o.jsxDEV(c,{gutter:[16,16],style:{marginBottom:"24px"},children:[o.jsxDEV(l,{xs:24,sm:12,lg:6,children:o.jsxDEV(i,{children:o.jsxDEV(m,{title:"Total Strategies",value:V,prefix:o.jsxDEV(h,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:56,columnNumber:23},void 0),valueStyle:{color:"#1890ff"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:53,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:52,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:51,columnNumber:9},void 0),o.jsxDEV(l,{xs:24,sm:12,lg:6,children:o.jsxDEV(i,{children:o.jsxDEV(m,{title:"Total Backtests",value:E,prefix:o.jsxDEV(P,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:66,columnNumber:23},void 0),valueStyle:{color:"#52c41a"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:63,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:62,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:61,columnNumber:9},void 0),o.jsxDEV(l,{xs:24,sm:12,lg:6,children:o.jsxDEV(i,{children:o.jsxDEV(m,{title:"Average Return",value:t(U),prefix:o.jsxDEV(u,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:76,columnNumber:23},void 0),valueStyle:{color:U>=0?"#52c41a":"#ff4d4f"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:73,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:72,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:71,columnNumber:9},void 0),o.jsxDEV(l,{xs:24,sm:12,lg:6,children:o.jsxDEV(i,{children:o.jsxDEV(m,{title:"Best Return",value:t(k),prefix:o.jsxDEV(b,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:86,columnNumber:23},void 0),valueStyle:{color:"#52c41a"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:83,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:82,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:81,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:50,columnNumber:7},void 0),o.jsxDEV(c,{gutter:[16,16],children:[o.jsxDEV(l,{xs:24,lg:8,children:o.jsxDEV(i,{title:"Quick Actions",style:{height:"300px"},children:o.jsxDEV(j,{direction:"vertical",style:{width:"100%"},size:"middle",children:[o.jsxDEV(N,{type:"primary",icon:o.jsxDEV(h,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:100,columnNumber:23},void 0),block:!0,onClick:()=>d("/strategies"),children:"Manage Strategies"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:98,columnNumber:15},void 0),o.jsxDEV(N,{type:"default",icon:o.jsxDEV(f,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:108,columnNumber:23},void 0),block:!0,onClick:()=>d("/backtest"),children:"Run Backtest"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:106,columnNumber:15},void 0),o.jsxDEV(N,{type:"default",block:!0,onClick:()=>d("/settings"),children:"System Settings"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:114,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:97,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:96,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:95,columnNumber:9},void 0),o.jsxDEV(l,{xs:24,lg:8,children:o.jsxDEV(i,{title:"Recent Strategies",style:{height:"300px"},extra:o.jsxDEV(N,{type:"link",onClick:()=>d("/strategies"),children:"View All"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:131,columnNumber:15},void 0),children:o.jsxDEV("div",{style:{maxHeight:"200px",overflowY:"auto"},children:[v.slice(0,5).map(e=>o.jsxDEV("div",{style:{padding:"8px 0",borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:o.jsxDEV("div",{children:[o.jsxDEV("div",{style:{fontWeight:500},children:e.name},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:149,columnNumber:21},void 0),o.jsxDEV("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:e.type},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:150,columnNumber:21},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:148,columnNumber:19},void 0)},e.id,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:138,columnNumber:17},void 0)),0===v.length&&o.jsxDEV("div",{style:{textAlign:"center",color:"#8c8c8c",padding:"20px"},children:"No strategies yet"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:157,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:136,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:127,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:126,columnNumber:9},void 0),o.jsxDEV(l,{xs:24,lg:8,children:o.jsxDEV(i,{title:"Recent Backtests",style:{height:"300px"},extra:o.jsxDEV(N,{type:"link",onClick:()=>d("/backtest"),children:"View All"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:171,columnNumber:15},void 0),children:o.jsxDEV("div",{style:{maxHeight:"200px",overflowY:"auto"},children:[S.map(e=>o.jsxDEV("div",{style:{padding:"8px 0",borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsxDEV("div",{children:[o.jsxDEV("div",{style:{fontWeight:500},children:e.symbol},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:189,columnNumber:21},void 0),o.jsxDEV("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:[a(e.trade_count)," trades"]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:190,columnNumber:21},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:188,columnNumber:19},void 0),o.jsxDEV("div",{style:{textAlign:"right"},children:o.jsxDEV("div",{style:{color:e.total_return>=0?"#52c41a":"#ff4d4f",fontWeight:500},children:t(e.total_return)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:195,columnNumber:21},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:194,columnNumber:19},void 0)]},e.id,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:178,columnNumber:17},void 0)),0===g.length&&o.jsxDEV("div",{style:{textAlign:"center",color:"#8c8c8c",padding:"20px"},children:"No backtest results yet"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:207,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:176,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:167,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:166,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:93,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Dashboard.tsx",lineNumber:41,columnNumber:5},void 0)};export{v as default};
//# sourceMappingURL=Dashboard-CykE3o7k.js.map
