{"version": 3, "mappings": ";sjCAaE,WAGF,IAAIA,EAAQC,IAMRC,EAAqBC,OAAOC,IAAI,iBAChCC,EAAoBF,OAAOC,IAAI,gBAC/BE,EAAsBH,OAAOC,IAAI,kBACjCG,EAAyBJ,OAAOC,IAAI,qBACpCI,EAAsBL,OAAOC,IAAI,kBACjCK,EAAsBN,OAAOC,IAAI,kBACjCM,EAAqBP,OAAOC,IAAI,iBAChCO,EAAyBR,OAAOC,IAAI,qBACpCQ,EAAsBT,OAAOC,IAAI,kBACjCS,EAA2BV,OAAOC,IAAI,uBACtCU,EAAkBX,OAAOC,IAAI,cAC7BW,EAAkBZ,OAAOC,IAAI,cAC7BY,EAAuBb,OAAOC,IAAI,mBAClCa,EAAwBd,OAAOe,SAgBnC,IAAIC,EAAuBnB,EAAMoB,mDAEjC,SAASC,EAAMC,GAGT,QAASC,EAAQC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,EAAQ,EAAIA,EAAQ,EAAI,GAAIK,EAAQ,EAAGA,EAAQL,EAAOK,IACxGF,EAAKE,EAAQ,GAAKJ,UAAUI,IAQpC,SAAsBC,EAAOP,EAAQI,GAIjC,IACII,EADyBX,EAAqBY,uBACfC,mBAErB,KAAVF,IACFR,GAAU,KACVI,EAAOA,EAAKO,OAAO,CAACH,KAItB,IAAII,EAAiBR,EAAKS,IAAI,SAAUC,GACtC,OAAOC,OAAOD,EAAI,GAGpBF,EAAeI,QAAQ,YAAchB,GAIrCiB,SAASC,UAAUC,MAAMC,KAAKC,QAAQd,GAAQc,QAAST,EACzD,CA3BIU,CAAa,QAAStB,EAAQI,EAElC,CA8BF,IAUImB,EAyCJ,SAASC,EAAeC,GACtB,OAAOA,EAAKC,aAAe,UAI7B,SAASC,EAAyBF,GAChC,GAAY,MAARA,EAEF,OAAO,KAST,GAL0B,iBAAbA,EAAKG,KACd7B,EAAM,qHAIU,mBAAT0B,EACT,OAAOA,EAAKC,aAAeD,EAAKI,MAAQ,KAG1C,GAAoB,iBAATJ,EACT,OAAOA,EAGT,OAAQA,GACN,KAAKzC,EACH,MAAO,WAET,KAAKD,EACH,MAAO,SAET,KAAKG,EACH,MAAO,WAET,KAAKD,EACH,MAAO,aAET,KAAKK,EACH,MAAO,WAET,KAAKC,EACH,MAAO,eAIX,GAAoB,iBAATkC,EACT,OAAQA,EAAKK,UACX,KAAK1C,EAEH,OAAOoC,EADOC,GACmB,YAEnC,KAAKtC,EAEH,OAAOqC,EADQC,EACgBM,UAAY,YAE7C,KAAK1C,EACH,OArER,SAAwB2C,EAAWC,EAAWC,GAC5C,IAAIR,EAAcM,EAAUN,YAE5B,GAAIA,EACF,OAAOA,EAGT,IAAIS,EAAeF,EAAUP,aAAeO,EAAUJ,MAAQ,GAC9D,MAAwB,KAAjBM,EAAsBD,EAAc,IAAMC,EAAe,IAAMD,CAAA,CA6DzDE,CAAeX,EAAMA,EAAKY,OAAQ,cAE3C,KAAK7C,EACH,IAAI8C,EAAYb,EAAKC,aAAe,KAEpC,OAAkB,OAAdY,EACKA,EAGFX,EAAyBF,EAAKA,OAAS,OAEhD,KAAKhC,EAED,IAAI8C,EAAgBd,EAChBe,EAAUD,EAAcE,SACxBC,EAAOH,EAAcI,MAEzB,IACE,OAAOhB,EAAyBe,EAAKF,GAAQ,OACtCI,GACP,OAAO,MAQjB,OAAO,KA3HPrB,EAAyB1C,OAAOC,IAAI,0BA8HtC,IAOI+D,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAbAC,EAASC,OAAOD,OAMhBE,EAAgB,EASpB,SAASC,IAAc,CAEvBA,EAAYC,oBAAqB,EA+EjC,IACIC,EADAC,EAAyB7D,EAAqB6D,uBAElD,SAASC,EAA8B9B,EAAM+B,EAAQC,GAEjD,QAAe,IAAXJ,EAEF,IACE,MAAMK,OAAM,OACLlB,GACP,IAAImB,EAAQnB,EAAEpC,MAAMwD,OAAOD,MAAM,gBACjCN,EAASM,GAASA,EAAM,IAAM,GAKlC,MAAO,KAAON,EAAS5B,CACzB,CAEF,IACIoC,EADAC,GAAU,EAIRC,EAAqC,mBAAZC,QAAyBA,QAAUC,IAIlE,SAASC,EAA6BC,EAAIC,GAExC,IAAMD,GAAML,EACV,MAAO,GAIP,IAOEO,EAPEC,EAAQT,EAAoBU,IAAIJ,GAEpC,QAAc,IAAVG,EACF,OAAOA,EAKXR,GAAU,EACV,IAGIU,EAHAC,EAA4Bf,MAAMgB,kBAEtChB,MAAMgB,uBAAoB,EAIxBF,EAAqBlB,EAAuBqB,QAG5CrB,EAAuBqB,QAAU,KAjIrC,WAEI,GAAsB,IAAlBzB,EAAqB,CAEvBT,EAAUxB,QAAQ2D,IAClBlC,EAAWzB,QAAQ4D,KACnBlC,EAAW1B,QAAQ6D,KACnBlC,EAAY3B,QAAQtB,MACpBkD,EAAY5B,QAAQ8D,MACpBjC,EAAqB7B,QAAQ+D,eAC7BjC,EAAe9B,QAAQgE,SAEvB,IAAIC,EAAQ,CACVC,cAAc,EACdC,YAAY,EACZC,MAAOlC,EACPmC,UAAU,GAGZrC,OAAOsC,iBAAiBtE,QAAS,CAC/B4D,KAAMK,EACNN,IAAKM,EACLJ,KAAMI,EACNvF,MAAOuF,EACPH,MAAOG,EACPF,eAAgBE,EAChBD,SAAUC,GACX,CAIHhC,GACF,CAkGEsC,GAGF,IAEE,GAAIpB,EAAW,CAEb,IAAIqB,EAAO,WACT,MAAM/B,OAAM,EAYd,GARAT,OAAOyC,eAAeD,EAAK3E,UAAW,QAAS,CAC7C6E,IAAK,WAGH,MAAMjC,OAAM,IAIO,iBAAZkC,SAAwBA,QAAQxB,UAAW,CAGpD,IACEwB,QAAQxB,UAAUqB,EAAM,GAAE,OACnBjD,GACP6B,EAAU7B,CAAA,CAGZoD,QAAQxB,UAAUD,EAAI,GAAIsB,EAAI,KACzB,CACL,IACEA,EAAKzE,MAAK,OACHwB,GACP6B,EAAU7B,CAAA,CAGZ2B,EAAGnD,KAAKyE,EAAK3E,UAAS,CACxB,KACK,CACL,IACE,MAAM4C,OAAM,OACLlB,GACP6B,EAAU7B,CAAA,CAGZ2B,GAAG,CACL,OACO0B,GAEP,GAAIA,GAAUxB,GAAmC,iBAAjBwB,EAAOzF,MAAoB,CAQzD,IALA,IAAI0F,EAAcD,EAAOzF,MAAM2F,MAAM,MACjCC,EAAe3B,EAAQjE,MAAM2F,MAAM,MACnCE,EAAIH,EAAY/F,OAAS,EACzBmG,EAAIF,EAAajG,OAAS,EAEvBkG,GAAK,GAAKC,GAAK,GAAKJ,EAAYG,KAAOD,EAAaE,IAOzDA,IAGF,KAAOD,GAAK,GAAKC,GAAK,EAAGD,IAAKC,IAG5B,GAAIJ,EAAYG,KAAOD,EAAaE,GAAI,CAMtC,GAAU,IAAND,GAAiB,IAANC,EACb,GAKE,GAJAD,MACAC,EAGQ,GAAKJ,EAAYG,KAAOD,EAAaE,GAAI,CAE/C,IAAIC,EAAS,KAAOL,EAAYG,GAAGG,QAAQ,WAAY,QAgBvD,OAXIjC,EAAG7C,aAAe6E,EAAOE,SAAS,iBACpCF,EAASA,EAAOC,QAAQ,cAAejC,EAAG7C,cAIxB,mBAAP6C,GACTN,EAAoB8B,IAAIxB,EAAIgC,GAKzBA,CAAA,QAEFF,GAAK,GAAKC,GAAK,GAG1B,MAEJ,CACF,SAEApC,GAAU,EAGRR,EAAuBqB,QAAUH,EAlNvC,WAII,GAAsB,MAFtBtB,EAEyB,CAEvB,IAAIgC,EAAQ,CACVC,cAAc,EACdC,YAAY,EACZE,UAAU,GAGZrC,OAAOsC,iBAAiBtE,QAAS,CAC/B2D,IAAK5B,EAAO,GAAIkC,EAAO,CACrBG,MAAO5C,IAEToC,KAAM7B,EAAO,GAAIkC,EAAO,CACtBG,MAAO3C,IAEToC,KAAM9B,EAAO,GAAIkC,EAAO,CACtBG,MAAO1C,IAEThD,MAAOqD,EAAO,GAAIkC,EAAO,CACvBG,MAAOzC,IAETmC,MAAO/B,EAAO,GAAIkC,EAAO,CACvBG,MAAOxC,IAETmC,eAAgBhC,EAAO,GAAIkC,EAAO,CAChCG,MAAOvC,IAETmC,SAAUjC,EAAO,GAAIkC,EAAO,CAC1BG,MAAOtC,KAEV,CAICG,EAAgB,GAClBvD,EAAM,+EAEV,CA0KI2G,GAGF5C,MAAMgB,kBAAoBD,CAAA,CAI5B,IAAIhD,EAAO0C,EAAKA,EAAG7C,aAAe6C,EAAG1C,KAAO,GACxC8E,EAAiB9E,EAAO8B,EAA8B9B,GAAQ,GAQlE,MALoB,mBAAP0C,GACTN,EAAoB8B,IAAIxB,EAAIoC,GAIzBA,CAAA,CAaT,SAASC,EAAqCnF,EAAMmC,EAAQC,GAE1D,GAAY,MAARpC,EACF,MAAO,GAGT,GAAoB,mBAATA,EAEP,OAAO6C,EAA6B7C,MAZpCP,EAY0DO,EAZpCP,aACHA,EAAU2F,mBAFnC,IACM3F,EAgBJ,GAAoB,iBAATO,EACT,OAAOkC,EAA8BlC,GAGvC,OAAQA,GACN,KAAKnC,EACH,OAAOqE,EAA8B,YAEvC,KAAKpE,EACH,OAAOoE,EAA8B,gBAGzC,GAAoB,iBAATlC,EACT,OAAQA,EAAKK,UACX,KAAKzC,EACH,OApCGiF,EAoCmC7C,EAAKY,QApCP,GAsCtC,KAAK7C,EAEH,OAAOoH,EAAqCnF,EAAKA,KAAMmC,EAAQC,GAEjE,KAAKpE,EAED,IAAI8C,EAAgBd,EAChBe,EAAUD,EAAcE,SACxBC,EAAOH,EAAcI,MAEzB,IAEE,OAAOiE,EAAqClE,EAAKF,GAAUoB,EAAQC,EAAO,OACnEjB,GAAG,EAKpB,MAAO,GA5NPqB,EAAsB,IAAIE,EA+N5B,IAAI2C,EAAiBzD,OAAOnC,UAAU4F,eAElCC,EAAqB,GACrBtG,EAAyBZ,EAAqBY,uBAElD,SAASuG,EAA8BC,GAEnC,GAAIA,EAAS,CACX,IAAIC,EAAQD,EAAQE,OAChB3G,EAAQoG,EAAqCK,EAAQxF,KAAMwF,EAAQG,QAASF,EAAQA,EAAMzF,KAAO,MACrGhB,EAAuB4G,mBAAmB7G,EAAK,MAE/CC,EAAuB4G,mBAAmB,KAE9C,CAoDF,IAAIC,EAAcjH,MAAMkH,QAExB,SAASA,EAAQC,GACf,OAAOF,EAAYE,EAAC,CAkCtB,SAASC,EAAmBhC,GAwB1B,MAAO,GAAKA,CAAA,CAEd,SAASiC,EAAuBjC,GAE5B,GAvCJ,SAA2BA,GAEvB,IAEE,OADAgC,EAAmBhC,IACZ,QACAkC,GACP,OAAO,EAEX,CA+BMC,CAAkBnC,GAGpB,OAFA1F,EAAM,kHAlDZ,SAAkB0F,GAKd,MAFuC,mBAAX5G,QAAyBA,OAAOgJ,aAC/BpC,EAAM5G,OAAOgJ,cAAgBpC,EAAMqC,YAAYjG,MAAQ,QAEtF,CA4CkIkG,CAAStC,IAEhIgC,EAAmBhC,EAE9B,CAGF,IAOIuC,EACAC,EACAC,EATAC,EAAoBtI,EAAqBsI,kBACzCC,EAAiB,CACnBC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,UAAU,GAOVN,EAAyB,GAkK3B,SAASO,EAAOhH,EAAMiH,EAAQC,EAAU/E,EAAQgF,GAE5C,IAAIC,EAEAvD,EAAQ,GACR+C,EAAM,KACNC,EAAM,KA6BV,IAAKO,UAtBY,IAAbF,IAEAjB,EAAuBiB,GAGzBN,EAAM,GAAKM,GAnKjB,SAAqBD,GAEjB,GAAI5B,EAAe1F,KAAKsH,EAAQ,OAAQ,CACtC,IAAII,EAASzF,OAAO0F,yBAAyBL,EAAQ,OAAO/D,IAE5D,GAAImE,GAAUA,EAAOE,eACnB,OAAO,CACT,CAIJ,YAAsB,IAAfN,EAAOL,GAAQ,CA2JhBY,CAAYP,KAEZhB,EAAuBgB,EAAOL,KAGhCA,EAAM,GAAKK,EAAOL,KAzLxB,SAAqBK,GAEjB,GAAI5B,EAAe1F,KAAKsH,EAAQ,OAAQ,CACtC,IAAII,EAASzF,OAAO0F,yBAAyBL,EAAQ,OAAO/D,IAE5D,GAAImE,GAAUA,EAAOE,eACnB,OAAO,CACT,CAIJ,YAAsB,IAAfN,EAAOJ,GAAQ,CAiLhBY,CAAYR,KACdJ,EAAMI,EAAOJ,IAjKnB,SAA8CI,EAAQE,GAElD,GAA0B,iBAAfF,EAAOJ,KAAoBH,EAAkBpD,SAAW6D,GAAQT,EAAkBpD,QAAQoE,YAAcP,EAAM,CACvH,IAAIQ,EAAgBzH,EAAyBwG,EAAkBpD,QAAQtD,MAElEyG,EAAuBkB,KAC1BrJ,EAAM,4VAAsX4B,EAAyBwG,EAAkBpD,QAAQtD,MAAOiH,EAAOJ,KAE7bJ,EAAuBkB,IAAiB,EAC1C,CAEJ,CAuJIC,CAAqCX,EAAQE,IAI9BF,EACX5B,EAAe1F,KAAKsH,EAAQG,KAAcT,EAAetB,eAAe+B,KAC1EvD,EAAMuD,GAAYH,EAAOG,IAK7B,GAAIpH,GAAQA,EAAK6H,aAAc,CAC7B,IAAIA,EAAe7H,EAAK6H,aAExB,IAAKT,KAAYS,OACS,IAApBhE,EAAMuD,KACRvD,EAAMuD,GAAYS,EAAaT,GAEnC,CAGF,GAAIR,GAAOC,EAAK,CACd,IAAI5G,EAA8B,mBAATD,EAAsBA,EAAKC,aAAeD,EAAKI,MAAQ,UAAYJ,EAExF4G,GA5KV,SAAoC/C,EAAO5D,GAEvC,IAAI6H,EAAwB,WACrBvB,IACHA,GAA6B,EAE7BjI,EAAM,4OAA4P2B,GACpQ,EAGF6H,EAAsBP,gBAAiB,EACvC3F,OAAOyC,eAAeR,EAAO,MAAO,CAClCX,IAAK4E,EACLhE,cAAc,GAElB,CA8JMiE,CAA2BlE,EAAO5D,GAGhC4G,GA9JV,SAAoChD,EAAO5D,GAEvC,IAAI+H,EAAwB,WACrBxB,IACHA,GAA6B,EAE7BlI,EAAM,4OAA4P2B,GACpQ,EAGF+H,EAAsBT,gBAAiB,EACvC3F,OAAOyC,eAAeR,EAAO,MAAO,CAClCX,IAAK8E,EACLlE,cAAc,GAElB,CAgJMmE,CAA2BpE,EAAO5D,EACpC,CAGF,OA5He,SAAUD,EAAM4G,EAAKC,EAAKM,EAAMhF,EAAQsD,EAAO5B,GAChE,IAAI2B,EAAU,CAEZnF,SAAUlD,EAEV6C,OACA4G,MACAC,MACAhD,QAEA6B,OAAQD,EAQRD,OAAiB,IAiCnB,OA5BE5D,OAAOyC,eAAemB,EAAQ0C,OAAQ,YAAa,CACjDpE,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,OAAO,IAGTpC,OAAOyC,eAAemB,EAAS,QAAS,CACtC1B,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,MAAOmD,IAITvF,OAAOyC,eAAemB,EAAS,UAAW,CACxC1B,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,MAAO7B,IAGLP,OAAOuG,SACTvG,OAAOuG,OAAO3C,EAAQ3B,OACtBjC,OAAOuG,OAAO3C,IAIXA,CAAA,CAyEE4C,CAAapI,EAAM4G,EAAKC,EAAKM,EAAMhF,EAAQuE,EAAkBpD,QAASO,EAC/E,CAGF,IAeIwE,GAfAC,GAAsBlK,EAAqBsI,kBAC3C6B,GAA2BnK,EAAqBY,uBAEpD,SAASwJ,GAAgChD,GAErC,GAAIA,EAAS,CACX,IAAIC,EAAQD,EAAQE,OAChB3G,EAAQoG,EAAqCK,EAAQxF,KAAMwF,EAAQG,QAASF,EAAQA,EAAMzF,KAAO,MACrGuI,GAAyB3C,mBAAmB7G,EAAK,MAEjDwJ,GAAyB3C,mBAAmB,KAEhD,CAiBF,SAAS6C,GAAeC,GAEpB,MAAyB,iBAAXA,GAAkC,OAAXA,GAAmBA,EAAOrI,WAAalD,CAC9E,CAGF,SAASwL,KAEL,GAAIL,GAAoBhF,QAAS,CAC/B,IAAIlD,EAAOF,EAAyBoI,GAAoBhF,QAAQtD,MAEhE,GAAII,EACF,MAAO,mCAAqCA,EAAO,IACrD,CAGF,MAAO,EACT,CA5BAiI,IAAgC,EAiDlC,IAAIO,GAAwB,GA8B5B,SAASC,GAAoBrD,EAASsD,GAElC,GAAKtD,EAAQ0C,SAAU1C,EAAQ0C,OAAOa,WAA4B,MAAfvD,EAAQoB,IAA3D,CAIApB,EAAQ0C,OAAOa,WAAY,EAC3B,IAAIC,EAnCR,SAAsCF,GAElC,IAAItF,EAAOmF,KAEX,IAAKnF,EAAM,CACT,IAAIyF,EAAmC,iBAAfH,EAA0BA,EAAaA,EAAW7I,aAAe6I,EAAW1I,KAEhG6I,IACFzF,EAAO,8CAAgDyF,EAAa,KACtE,CAGF,OAAOzF,CACT,CAsBkC0F,CAA6BJ,GAE7D,IAAIF,GAAsBI,GAA1B,CAIAJ,GAAsBI,IAA6B,EAInD,IAAIG,EAAa,GAEb3D,GAAWA,EAAQE,QAAUF,EAAQE,SAAW4C,GAAoBhF,UAEtE6F,EAAa,+BAAiCjJ,EAAyBsF,EAAQE,OAAO1F,MAAQ,KAGhGwI,GAAgChD,GAEhClH,EAAM,4HAAkI0K,EAA2BG,GAEnKX,GAAgC,KAlB9B,CAPA,CA0BJ,CAaF,SAASY,GAAkBC,EAAMP,GAE7B,GAAoB,iBAATO,EAIX,GAAIvD,EAAQuD,GACV,QAASC,EAAI,EAAGA,EAAID,EAAK3K,OAAQ4K,IAAK,CACpC,IAAIC,EAAQF,EAAKC,GAEbb,GAAec,IACjBV,GAAoBU,EAAOT,EAC7B,MACF,GACSL,GAAeY,GAEpBA,EAAKnB,SACPmB,EAAKnB,OAAOa,WAAY,WAEjBM,EAAM,CACf,IAAIG,EApjCV,SAAuBC,GACrB,GAAsB,OAAlBA,GAAmD,iBAAlBA,EACnC,OAAO,KAGT,IAAIC,EAAgBxL,GAAyBuL,EAAcvL,IAA0BuL,EAN5D,cAQzB,MAA6B,mBAAlBC,EACFA,EAGF,KAyiCcC,CAAcN,GAE/B,GAA0B,mBAAfG,GAGLA,IAAeH,EAAKO,QAItB,IAHA,IACIC,EADA1L,EAAWqL,EAAW7J,KAAK0J,KAGtBQ,EAAO1L,EAAS2L,QAAQC,MAC3BtB,GAAeoB,EAAK7F,QACtB6E,GAAoBgB,EAAK7F,MAAO8E,EAIxC,CAEJ,CAUF,SAASkB,GAAkBxE,GAEvB,IAMIyE,EANAjK,EAAOwF,EAAQxF,KAEnB,GAAIA,SAAuD,iBAATA,EAAlD,CAMA,GAAoB,mBAATA,EACTiK,EAAYjK,EAAKiK,cAAA,IACQ,iBAATjK,GAAsBA,EAAKK,WAAazC,GAE1DoC,EAAKK,WAAatC,EAGhB,OAFAkM,EAAYjK,EAAKiK,SAEjB,CAGF,GAAIA,EAAW,CAEb,IAAI7J,EAAOF,EAAyBF,IA5jB1C,SAAwBkK,EAAWC,EAAQC,EAAUzC,EAAenC,GAGhE,IAAI6E,EAAM7K,SAASG,KAAK2K,KAAKjF,GAE7B,QAASkF,KAAgBL,EACvB,GAAIG,EAAIH,EAAWK,GAAe,CAChC,IAAIC,OAAU,EAId,IAGE,GAAuC,mBAA5BN,EAAUK,GAA8B,CAEjD,IAAIE,EAAMpI,OAAOsF,GAAiB,eAAiB,KAAOyC,EAAW,UAAYG,EAAe,oGAA2GL,EAAUK,GAAgB,mGAErO,MADAE,EAAIrK,KAAO,sBACLqK,CAAA,CAGRD,EAAUN,EAAUK,GAAcJ,EAAQI,EAAc5C,EAAeyC,EAAU,KAAM,+CAA8C,OAC9HM,GACPF,EAAUE,CAAA,EAGRF,GAAaA,aAAmBnI,QAClCkD,EAA8BC,GAE9BlH,EAAM,2RAAqTqJ,GAAiB,cAAeyC,EAAUG,SAAqBC,GAE1XjF,EAA8B,OAG5BiF,aAAmBnI,SAAWmI,EAAQG,WAAWrF,KAGnDA,EAAmBkF,EAAQG,UAAW,EACtCpF,EAA8BC,GAE9BlH,EAAM,qBAAsB8L,EAAUI,EAAQG,SAE9CpF,EAA8B,MAChC,CAGN,CA+gBIqF,CAAeX,EAAWzE,EAAQ3B,MAAO,OAAQzD,EAAMoF,EAAO,cAClC,IAAnBxF,EAAK6K,YAA4BxC,GAA+B,CACzEA,IAAgC,EAIhC/J,EAAM,sGAFM4B,EAAyBF,IAEiF,UAAS,CAG7F,mBAAzBA,EAAK8K,iBAAmC9K,EAAK8K,gBAAgBC,sBACtEzM,EAAM,6HA5BN,CA8BJ,CAmCF,IAAI0M,GAAwB,GAmG5B,IAAIC,GAlGJ,SAA2BjL,EAAM6D,EAAO+C,EAAKsE,EAAkB/I,EAAQgF,GAEnE,IAAIgE,EAjlCR,SAA4BnL,GAC1B,MAAoB,iBAATA,GAAqC,mBAATA,GAKnCA,IAASzC,GAAuByC,IAASvC,GAA8CuC,IAASxC,GAA0BwC,IAASnC,GAAuBmC,IAASlC,GAAmDkC,IAAS/B,GAI/M,iBAAT+B,GAA8B,OAATA,IAC1BA,EAAKK,WAAarC,GAAmBgC,EAAKK,WAAatC,GAAmBiC,EAAKK,WAAa3C,GAAuBsC,EAAKK,WAAa1C,GAAsBqC,EAAKK,WAAazC,GAIjLoC,EAAKK,WAAaP,QAA+C,IAArBE,EAAKoL,YAK5C,CA6jCWC,CAAmBrL,GAGnC,IAAKmL,EAAW,CACd,IAAI3H,EAAO,SAEE,IAATxD,GAAsC,iBAATA,GAA8B,OAATA,GAA8C,IAA7B4B,OAAO0J,KAAKtL,GAAMtB,UACvF8E,GAAQ,oIAGV,IAQI+H,EARAC,EA5NV,SAAoCrJ,GAEhC,YAAe,IAAXA,EAGK,0BAFQA,EAAOsJ,SAAS1G,QAAQ,YAAa,IAEN,IAD7B5C,EAAOuJ,WACyC,IAG5D,EACT,CAmNqBC,CAA2BxJ,GAG1CqB,GADEgI,GAGM7C,KAKG,OAAT3I,EACFuL,EAAa,OACJzF,EAAQ9F,GACjBuL,EAAa,aACK,IAATvL,GAAsBA,EAAKK,WAAalD,GACjDoO,EAAa,KAAOrL,EAAyBF,EAAKA,OAAS,WAAa,MACxEwD,EAAO,sEAEP+H,SAAoBvL,EAGtB1B,EAAM,0IAAqJiN,EAAY/H,EAAI,CAG7K,IAAIgC,EAAUwB,EAAOhH,EAAM6D,EAAO+C,EAAKzE,EAAQgF,GAG/C,GAAe,MAAX3B,EACF,OAAOA,EAQT,GAAI2F,EAAW,CACb,IAAIS,EAAW/H,EAAM+H,SAErB,QAAiB,IAAbA,EACF,GAAIV,EACF,GAAIpF,EAAQ8F,GAAW,CACrB,QAAStC,EAAI,EAAGA,EAAIsC,EAASlN,OAAQ4K,IACnCF,GAAkBwC,EAAStC,GAAItJ,GAG7B4B,OAAOuG,QACTvG,OAAOuG,OAAOyD,EAChB,MAEAtN,EAAM,6JAGR8K,GAAkBwC,EAAU5L,EAEhC,CAIA,GAAIqF,EAAe1F,KAAKkE,EAAO,OAAQ,CACrC,IAAI8D,EAAgBzH,EAAyBF,GACzCsL,EAAO1J,OAAO0J,KAAKzH,GAAOgI,OAAO,SAAUC,GAC7C,MAAa,QAANA,CAAM,GAEXC,EAAgBT,EAAK5M,OAAS,EAAI,kBAAoB4M,EAAKU,KAAK,WAAa,SAAW,iBAE5F,IAAKhB,GAAsBrD,EAAgBoE,GAGzCzN,EAAM,kOAA4PyN,EAAepE,EAF9P2D,EAAK5M,OAAS,EAAI,IAAM4M,EAAKU,KAAK,WAAa,SAAW,KAEiOrE,GAE9SqD,GAAsBrD,EAAgBoE,IAAiB,CACzD,CAUJ,OANI/L,IAASzC,EApHjB,SAA+B0O,GAI3B,IAFA,IAAIX,EAAO1J,OAAO0J,KAAKW,EAASpI,OAEvByF,EAAI,EAAGA,EAAIgC,EAAK5M,OAAQ4K,IAAK,CACpC,IAAI1C,EAAM0E,EAAKhC,GAEf,GAAY,aAAR1C,GAA8B,QAARA,EAAe,CACvC4B,GAAgCyD,GAEhC3N,EAAM,2GAAiHsI,GAEvH4B,GAAgC,MAChC,MACF,CAGmB,OAAjByD,EAASpF,MACX2B,GAAgCyD,GAEhC3N,EAAM,yDAENkK,GAAgC,MAEpC,CA6FI0D,CAAsB1G,GAEtBwE,GAAkBxE,GAGbA,CACT,EAKF2G,EAAAC,SAAmB7O,EACnB4O,EAAAnF,OAAiBiE,EAAA,CAnxCf,uBCRAoB,EAAAC,QAAiBpP,0DCHnB,IAAIqP,EAAIrP,IAKFoM,EAAIiD,EAAElO,0DACVmO,aAAqB,SAAS3H,EAAG4H,GAC/BnD,EAAEoD,uBAAwB,EAC1B,IACE,OAAOH,EAAEI,WAAW9H,EAAG4H,EAAC,SAExBnD,EAAEoD,uBAAwB,EAC5B,EAEFF,EAAAI,YAAsB,SAAS/H,EAAGgI,EAAGJ,GACnCnD,EAAEoD,uBAAwB,EAC1B,IACE,OAAOH,EAAEK,YAAY/H,EAAGgI,EAAGJ,EAAC,SAE5BnD,EAAEoD,uBAAwB,EAC5B,6DCjBJJ,QALA,SAAgCpG,GAC9B,OAAOA,GAAKA,EAAE4G,WAAa5G,EAAI,CAC7B6G,QAAW7G,EAEf,EACyC8G,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,8JCL9G,SAASW,EAAQR,GAGf,OAAOO,EAAAV,QAAiBW,EAAU,mBAAqB7P,QAAU,iBAAmBA,OAAOe,SAAW,SAAUsO,GAC9G,cAAcA,CAClB,EAAM,SAAUA,GACZ,OAAOA,GAAK,mBAAqBrP,QAAUqP,EAAEpG,cAAgBjJ,QAAUqP,IAAMrP,OAAOqC,UAAY,gBAAkBgN,CACtH,EAAKO,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,QAASW,EAAQR,EAC3F,CACAO,EAAAV,QAAiBW,EAASD,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,0FCT3FW,EAAU/P,KAAgC,QAC1CgQ,mCCDAD,EAAU/P,KAAgC,QAW9C8P,EAAAV,QAVA,SAAqBa,EAAGC,GACtB,GAAI,UAAYH,EAAQE,KAAOA,EAAG,OAAOA,EACzC,IAAIjH,EAAIiH,EAAE/P,OAAO8P,aACjB,QAAI,IAAWhH,EAAG,CAChB,IAAIoD,EAAIpD,EAAEvG,KAAKwN,EAAGC,GAAK,WACvB,GAAI,UAAYH,EAAQ3D,GAAI,OAAOA,EACnC,MAAM,IAAI+D,UAAU,+CACxB,CACE,OAAQ,WAAaD,EAAI9N,OAASgO,QAAQH,EAC5C,EAC8BH,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,0BAX/FW,EDCcM,GAKlBP,EAAAV,QAJA,SAAuBa,GACrB,IAAI7D,EAAI4D,EAAYC,EAAG,UACvB,MAAO,UAAYF,EAAQ3D,GAAKA,EAAIA,EAAI,EAC1C,EACgC0D,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,yBANjGW,EACAC,6CEDJ,IAAI7I,kCCAAmJ,EAAgBtQ,KASpB8P,EAAAV,QARA,SAAyBpG,EAAGkH,EAAGD,GAC7B,OAAQC,EAAII,EAAcJ,MAAOlH,EAAItE,OAAOyC,eAAe6B,EAAGkH,EAAG,CAC/DpJ,MAAOmJ,EACPpJ,YAAY,EACZD,cAAc,EACdG,UAAU,IACPiC,EAAEkH,GAAKD,EAAGjH,CACjB,EACkC8G,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,yBATnGkB,EDAiBtQ,GACrB,SAASuQ,EAAQvH,EAAGkH,GAClB,IAAID,EAAIvL,OAAO0J,KAAKpF,GACpB,GAAItE,OAAO8L,sBAAuB,CAChC,IAAIjB,EAAI7K,OAAO8L,sBAAsBxH,GACrCkH,IAAMX,EAAIA,EAAEZ,OAAO,SAAUuB,GAC3B,OAAOxL,OAAO0F,yBAAyBpB,EAAGkH,GAAGrJ,UACnD,IAASoJ,EAAEQ,KAAKjO,MAAMyN,EAAGV,EACzB,CACE,OAAOU,CACT,CAYAH,EAAAV,QAXA,SAAwBpG,GACtB,QAASkH,EAAI,EAAGA,EAAI3O,UAAUC,OAAQ0O,IAAK,CACzC,IAAID,EAAI,MAAQ1O,UAAU2O,GAAK3O,UAAU2O,GAAK,GAC9CA,EAAI,EAAIK,EAAQ7L,OAAOuL,IAAI,GAAIS,QAAQ,SAAUR,GAC/C/I,EAAe6B,EAAGkH,EAAGD,EAAEC,GAC7B,GAASxL,OAAOiM,0BAA4BjM,OAAOsC,iBAAiBgC,EAAGtE,OAAOiM,0BAA0BV,IAAMM,EAAQ7L,OAAOuL,IAAIS,QAAQ,SAAUR,GAC7IxL,OAAOyC,eAAe6B,EAAGkH,EAAGxL,OAAO0F,yBAAyB6F,EAAGC,GACrE,EACA,CACE,OAAOlH,CACT,EACiC8G,EAAAV,QAAAQ,YAA4B,EAAME,EAAOV,QAAiB,QAAIU,EAAOV,yEEpBtG,IAAIwB,EAAyB5Q,IAAwD6P,QACrFnL,OAAOyC,eAAe0J,EAAS,aAAc,CAC3C/J,OAAO,IAET+J,EAAAhB,aAAkB,EAClB,IAAIiB,EAAiBF,EAAuBP,MACxCU,aCNJrM,OAAOyC,eAAe6J,GAAS,aAAc,CAC3ClK,OAAO,IAETkK,GAAAC,kBAAuB,EACJD,GAAAC,aAAuB,CACxCC,WAAY,OACZC,UAAW,IACXC,mBAAoB,IACpBC,iBAAiB,QDDfC,GAAA,EAAaR,EAAejB,UAAA,EAAaiB,EAAejB,SAAS,GAAIkB,EAAQE,cAAe,GAAI,CAClGK,OAAQ,QACRC,MAAO,KACPC,IAAK,KACLC,YAAa,OACbC,GAAI,KACJC,WAAY,OACZC,WAAY,OACZC,WAAY,MACZC,MAAO,KACPC,KAAM,IACNC,MAAO,IACPC,KAAM,IACNC,cAAe,aACfC,UAAW,aACXC,YAAa,OACbC,WAAY,OACZC,aAAc,OACdC,aAAc,sBACdC,SAAU,sBACVC,eAAgB,OAChBC,WAAY,OACZC,gBAAiB,OACjBC,YAAa,OACb1B,WAAY,QACZ2B,eAAgB,IAChBxB,iBAAiB,WAEJR,EAAAhB,QAAkByB,mEEnCjC5M,OAAOyC,eAAe0J,GAAS,aAAc,CAC3C/J,OAAO,IAET+J,GAAAhB,aAAkB,SAKHgB,GAAAhB,QAJA,CACbiD,YAAa,QACbC,iBAAkB,CAAC,OAAQ,8CCN7B,IAAInC,EAAyB5Q,IAAwD6P,QACrFnL,OAAOyC,eAAe0J,EAAS,aAAc,CAC3C/J,OAAO,IAET+J,EAAAhB,aAAkB,EAClB,IAAImD,EAASpC,EAAuBP,MAChC4C,EAAUrC,QAEd,MAAMU,EAAS,CACb4B,KAAMxO,OAAOD,OAAO,CAClBqO,YAAa,QACbK,gBAAiB,QACjBC,mBAAoB,QACpBC,iBAAkB,QAClBC,gBAAiB,OACjBP,iBAAkB,CAAC,OAAQ,QAC3BQ,qBAAsB,CAAC,OAAQ,QAC/BC,sBAAuB,CAAC,OAAQ,QAChCC,wBAAyB,CAAC,OAAQ,QAClCC,qBAAsB,CAAC,MAAO,QAC7BV,EAAOnD,SACV8D,iBAAkBjP,OAAOD,OAAO,GAAIwO,EAAQpD,iBAG9CyB,EAAO4B,KAAKxB,GAAK,KAGFb,EAAAhB,QAAkByB,sCC3BjC,IAAIV,EAAyB5Q,IAAwD6P,QACrFnL,OAAOyC,eAAe0J,EAAS,aAAc,CAC3C/J,OAAO,IAET+J,EAAAhB,aAAkB,EAClB,IAAImD,EAASpC,eACEC,EAAAhB,QAAkBmD,EAAOnD,4CCNxC,IAAIe,EAAyB5Q,IAAwD6P,QACrFnL,OAAOyC,eAAe0J,EAAS,aAAc,CAC3C/J,OAAO,IAET+J,EAAAhB,aAAkB,EAClB,IAAImD,EAASpC,WCLblM,OAAOyC,eAAe0J,EAAS,aAAc,CAC3C/J,OAAO,IAET+J,EAAAhB,aAAkB,EAgBHgB,EAAAhB,QAfF,CAEX+D,eAAgB,MAChBC,QAAS,KACTC,gBAAiB,KACjBC,KAAM,IAENC,UAAW,MACXC,UAAW,MACXC,OAAQ,SACRC,OAAQ,SACRC,OAAQ,SACRC,OAAQ,SACRC,UAAW,WDXTrB,EAAUrC,QACV2D,EAAU3D,QACV4D,EAAU5D,QACd,MAAM6D,EAAe,yBACfC,EAAe,CACnBpD,OAAQ,QACRqD,WAAY3B,EAAOnD,QACnB+E,WAAYL,EAAQ1E,QACpBgF,WAAYL,EAAQ3E,QACpBiF,SAAU7B,EAAQpD,QAElBkF,OAAQ,CACNjC,YAAa,MACbkC,MAAO,MAETC,MAAO,CACLC,YAAa,KACbC,cAAe,KACfC,YAAa,KACbC,gBAAiB,OACjBC,eAAgB,KAChBC,wBAAyB,UACzBC,UAAW,OACXC,UAAW,OACXC,aAAc,OACdC,WAAY,OACZC,aAAc,OACdC,UAAW,KACXC,OAAQ,MACRC,SAAU,MACVC,YAAa,OACbC,WAAY,OACZC,WAAY,QAEdC,MAAO,CACLC,OAAQ,KACRC,WAAY,KACZC,WAAY,OAEdC,KAAM,CACJC,KAAM,MACNC,SAAU,MACVC,OAAQ,QAEVC,WAAY,CACVN,WAAY,KACZD,OAAQ,MAEVQ,SAAU,CACRC,OAAQ,CAAC,GAAI,IACbC,kBAAmB,UACnBC,SAAU,IACVC,UAAW,IACXC,OAAQ,KACRC,cAAe,OACfC,cAAe,OACf1B,UAAW,OACX2B,YAAa,OACbC,UAAW,OACX3B,aAAc,QAEhB4B,OAAQ,CACNC,UAAW,QACXC,WAAY,OACZC,YAAa,OACbC,YAAa,OACbC,aAAc,QAEhBC,MAAO,CACLC,YAAa,QAEfC,KAAM,CACJC,KAAM,MAERC,KAAM,CACJC,KAAM,KACNC,KAAM,KACNC,OAAQ,OACRrC,OAAQ,KACRC,SAAU,MAEZqC,KAAM,CACJC,SAAU,OACVC,wBAAyB,CACvBzI,QAAS,iBACT0I,SAAU,cACVC,KAAM,2BACNC,WAAY,iBACZC,KAAM,CACJrX,OAAQ,iBACRsX,MAAO,kBACPC,QAAS,mBAEXC,MAAO,CACLC,OAAQrE,EACRsE,OAAQtE,EACRuE,MAAOvE,EACPjJ,OAAQiJ,EACRwE,OAAQxE,EACRiE,KAAMjE,EACNyE,QAASzE,EACT0E,QAAS1E,EACT2E,MAAO3E,EACP4E,OAAQ5E,EACR6E,MAAO7E,EACP8E,IAAK9E,EACL+E,IAAK/E,GAEPqE,OAAQ,CACNW,IAAK,sBACLC,IAAK,sBACLC,IAAK,sBACLC,MAAO,+BAETX,OAAQ,CACNQ,IAAK,qBACLC,IAAK,qBACLC,IAAK,qBACLC,MAAO,6BAETZ,MAAO,CACLS,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,MAAO,+BAETC,QAAS,CACPC,SAAU,8BAIhBC,MAAO,CACLC,QAAS,MAEXC,OAAQ,CACNC,QAAS,QACTC,QAAS,OACTC,QAAS,OAEXC,YAAa,CACXC,YAAa,KACbC,YAAa,KACbC,YAAa,KACbC,cAAe,eAGJ5J,EAAAhB,QAAkB6E,qCE1JjC7D,GAAiB7Q,uBCYV,MAAM0a,WAAsBC,YACjC,WAAAxR,CAAYxC,GACViU,MAAMjU,GACNkU,KAAKC,MAAQ,CAAEC,UAAU,EAC3B,CAEA,+BAAOC,CAAyB5Z,GAC9B,MAAO,CAAE2Z,UAAU,EAAM3Z,QAC3B,CAEA,iBAAA6Z,CAAkB7Z,EAAc8Z,GAEhC,CAEAC,aAAe,KACbC,OAAOlO,SAASmO,UAGlB,MAAA3X,GACE,OAAImX,KAAKC,MAAMC,SAEXjR,SAAC,OAAIwR,MAAO,CAAEC,QAAS,OAAQC,UAAW,UACxC9M,SAAA5E,SAAC2R,EAAA,CACCC,OAAO,QACPC,MAAM,uBACNC,SAAUf,KAAKC,MAAM1Z,OAAOqM,SAAW,+BACvCoO,eACGC,EAAA,CAAOhZ,KAAK,UAAUiZ,QAASlB,KAAKM,aAAczM,SAAA,oBAAnD,MAAAH,SAAA,6FAAAC,WAAA,GAAAwN,aAAA,IAAAnB,YALJ,MAAAtM,SAAA,6FAAAC,WAAA,GAAAwN,aAAA,IAAAnB,YADF,MAAAtM,SAAA,6FAAAC,WAAA,GAAAwN,aAAA,GAAAnB,MAeGA,KAAKlU,MAAM+H,QACpB,8LCvCIuN,OAAyCvW,IACzCwW,GAA6BhZ,IACjC,MAAMiZ,EAAMF,GAAmBjW,IAAI9C,GACnC,OAAKiZ,EACEzX,OAAO0X,YACZ1X,OAAOgI,QAAQyP,EAAIE,QAAQna,IAAI,EAAEwH,EAAK4S,KAAU,CAAC5S,EAAK4S,EAAKC,cAF5C,IAiMbC,GA1Ke,CAAC5W,EAAI6W,EAAkB,KAAO,CAACrV,EAAKpB,EAAKmW,KAC5D,MAAMO,QAAEA,EAAAC,oBAASA,EAAAC,MAAqBA,KAAUC,GAAYJ,EAC5D,IAAIK,EACJ,IACEA,GAAiC,MAAXJ,EAAkBA,EAAgE,gBAArDK,GAAkB,kBAAuB,KAA6B3B,OAAO4B,4BAClI,OAASC,GACT,CACA,IAAKH,EAMH,OAAOlX,EAAGwB,EAAKpB,EAAKmW,GAEtB,MAAMe,WAAEA,KAAeC,GAjCY,EAACP,EAAOE,EAAoBD,KAC/D,QAAc,IAAVD,EACF,MAAO,CACL9Z,KAAM,YACNoa,WAAYJ,EAAmBM,QAAQP,IAG3C,MAAMQ,EAAqBpB,GAAmBjW,IAAI6W,EAAQ3Z,MAC1D,GAAIma,EACF,MAAO,CAAEva,KAAM,UAAW8Z,WAAUS,GAEtC,MAAMC,EAAgB,CACpBJ,WAAYJ,EAAmBM,QAAQP,GACvCR,OAAQ,IAGV,OADAJ,GAAmB7U,IAAIyV,EAAQ3Z,KAAMoa,GAC9B,CAAExa,KAAM,UAAW8Z,WAAUU,IAiBaC,CAA6BX,EAAOE,EAAoBD,GACzG,IAAIW,GAAc,EAClBrB,EAAIsB,SAAW,CAAC3C,EAAOjT,EAAS6V,KAC9B,MAAMxN,EAAI9I,EAAI0T,EAAOjT,GACrB,IAAK2V,EAAa,OAAOtN,EACzB,MAAMyN,OAA0B,IAAjBD,EAA0B,CAAE5a,KAAM6Z,GAAuB,aAAwC,iBAAjBe,EAA4B,CAAE5a,KAAM4a,GAAiBA,EACpJ,YAAc,IAAVd,GACY,MAAdM,GAA8BA,EAAWU,KAAKD,EAAQ3X,KAC/CkK,IAEK,MAAdgN,GAA8BA,EAAWU,KACvC,IACKD,EACH7a,KAAM,GAAG8Z,KAASe,EAAO7a,QAE3B,IACKoZ,GAA0BW,EAAQ3Z,MACrC0Z,CAACA,GAAQT,EAAII,aAGVrM,IAET,MAAM2N,EAAuB,IAAIhV,KAC/B,MAAMiV,EAAsBN,EAC5BA,GAAc,EACdpW,KAAOyB,GACP2U,EAAcM,GAEVC,EAAenY,EAAGuW,EAAIsB,SAAUzX,EAAKmW,GAc3C,GAbmC,cAA/BgB,EAAsBra,KACV,MAAdoa,GAA8BA,EAAWnZ,KAAKga,IAE9CZ,EAAsBd,OAAOc,EAAsBP,OAAST,EAC9C,MAAde,GAA8BA,EAAWnZ,KACvCW,OAAO0X,YACL1X,OAAOgI,QAAQyQ,EAAsBd,QAAQna,IAAI,EAAEwH,EAAKsU,KAAY,CAClEtU,EACAA,IAAQyT,EAAsBP,MAAQmB,EAAeC,EAAOzB,gBAKhEJ,EAAI8B,sBAAgD,mBAAjB9B,EAAI+B,SAAyB,CAClE,IAAIC,GAAiC,EACrC,MAAMC,EAAmBjC,EAAI+B,SAC7B/B,EAAI+B,SAAW,IAAIrV,KACyC,gBAArDkU,GAAkB,kBAAuB,IAA0C,eAAdlU,EAAE,GAAG/F,MAA0Bqb,IAIvGA,GAAiC,GAEnCC,KAAoBvV,GAExB,CAmGA,OAlGAqU,EAAWmB,UAAW5Q,IACpB,IAAI6Q,EACJ,OAAQ7Q,EAAQ3K,MACd,IAAK,SACH,GAA+B,iBAApB2K,EAAQ5J,QAIjB,OAEF,OAAO0a,GACL9Q,EAAQ5J,QACP8Z,IACC,GAAoB,eAAhBA,EAAO7a,KAAuB,CAChC,QAAc,IAAV8Z,EAEF,YADAiB,EAAqBF,EAAO7C,OAG1BpW,OAAO0J,KAAKuP,EAAO7C,OAAOtZ,OAS9B,MAAMgd,EAAoBb,EAAO7C,MAAM8B,GACvC,GAAI4B,QACF,OAKF,YAHIC,KAAKC,UAAUvC,EAAII,cAAgBkC,KAAKC,UAAUF,IACpDX,EAAqBW,GAGzB,CACKrC,EAAI8B,sBACmB,mBAAjB9B,EAAI+B,UACf/B,EAAI+B,SAASP,KAGnB,IAAK,WACH,OAAQlQ,EAAQ5J,QAAQf,MACtB,IAAK,QAEH,OADA+a,EAAqBE,QACP,IAAVnB,EACmB,MAAdM,OAAqB,EAASA,EAAWnZ,KAAKoY,EAAII,YAEtC,MAAdW,OAAqB,EAASA,EAAWnZ,KAAKmY,GAA0BW,EAAQ3Z,OACzF,IAAK,SACH,YAAc,IAAV0Z,OACY,MAAdM,GAA8BA,EAAWnZ,KAAKoY,EAAII,aAG/B,MAAdW,OAAqB,EAASA,EAAWnZ,KAAKmY,GAA0BW,EAAQ3Z,OACzF,IAAK,WACH,OAAOqb,GAAc9Q,EAAQqN,MAAQA,IACnC,QAAc,IAAV8B,EAGF,OAFAiB,EAAqB/C,QACP,MAAdoC,GAA8BA,EAAWnZ,KAAKoY,EAAII,aAGpDsB,EAAqB/C,EAAM8B,IACb,MAAdM,GAA8BA,EAAWnZ,KAAKmY,GAA0BW,EAAQ3Z,SAEpF,IAAK,gBACL,IAAK,iBACH,OAAOqb,GAAc9Q,EAAQqN,MAAQA,SACrB,IAAV8B,EAIA6B,KAAKC,UAAUvC,EAAII,cAAgBkC,KAAKC,UAAU5D,EAAM8B,KAC1DiB,EAAqB/C,EAAM8B,IAJ3BiB,EAAqB/C,KAO3B,IAAK,eAAgB,CACnB,MAAM6D,gBAAEA,GAAoBlR,EAAQ5J,QAC9B+a,EAA0E,OAArDN,EAAKK,EAAgBE,eAAeC,OAAM,GAAI,SAAc,EAASR,EAAGxD,MACnG,IAAK8D,EAAmB,OAWxB,OATEf,OADY,IAAVjB,EACmBgC,EAEAA,EAAkBhC,SAE3B,MAAdM,GAA8BA,EAAWU,KACvC,KAEAe,GAGJ,CACA,IAAK,kBACH,OAAOnB,GAAeA,EAE1B,UAGCO,GAGHQ,GAAgB,CAACQ,EAAaC,KAClC,IAAIC,EACJ,IACEA,EAASR,KAAK9F,MAAMoG,EACtB,OAAS/V,GAKT,MACe,IAAXiW,GAAmBD,EAAEC,IC1LdC,GAAcC,IACzB3C,GACGpV,IAAA,CAECgY,QAAS,CACPC,YAAY,EACZC,UAAU,EACVC,MAAM,GAERF,WAAY,GACZG,gBAAiB,GACjBpe,MAAO,KAGPqe,WAAY,CAAC/V,EAAK5C,IAChBM,EAAK0T,IAAA,CACHsE,QAAS,IAAKtE,EAAMsE,QAAS1V,CAACA,GAAM5C,MAGxC4Y,cAAgBL,GAAejY,EAAI,CAAEiY,eAErCM,YAAcC,GACZxY,EAAK0T,IAAA,CACHuE,WAAY,IAAIvE,EAAMuE,WAAYO,MAGtCC,eAAiBC,GACf1Y,EAAK0T,IAAA,CACHuE,WAAYvE,EAAMuE,WAAW1Q,OAAQjH,GAAMA,EAAEoY,KAAOA,MAGxDC,mBAAqBP,GAAoBpY,EAAI,CAAEoY,oBAE/CQ,kBAAoBC,GAClB7Y,EAAK0T,IAAA,CACH0E,gBAAiB,CAACS,KAAWnF,EAAM0E,oBAGvCU,SAAW9e,GAAUgG,EAAI,CAAEhG,UAE3B+e,WAAY,IAAM/Y,EAAI,CAAEhG,MAAO,SAEjC,CACE8B,KAAM,wBCtEN8U,KAAEA,IAASoI,EAcJC,GAA8D,EAAGC,gBAC5E,MAAOC,EAASC,GAAcC,YAAS,GAEjCC,EAAkBC,IACtB,MAAMvS,EAAO,GAKb,OAJIuS,EAASC,SAASxS,EAAKqC,KAAK,QAC5BkQ,EAASE,QAAQzS,EAAKqC,KAAK,OAC3BkQ,EAASG,UAAU1S,EAAKqC,KAAK,SACjCrC,EAAKqC,KAAKkQ,EAASjX,KACZ0E,EAAKU,KAAK,QAGnB,OACEhF,SAAAoF,WAAA,CACER,SAAA,CAAA5E,SAACgS,EAAA,CACChZ,KAAK,OACLiV,cAAOgJ,EAAA,QAAD,MAAAxS,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACNkB,QAAS,IAAMyE,GAAW,GAC1B7E,MAAM,qBACNqF,KAAK,cALP,MAAAzS,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GAQA/Q,SAACqM,EAAA,CACCwF,MAAM,qBACNsF,KAAMV,EACNW,SAAU,IAAMV,GAAW,GAC3BW,OAAQ,KACRC,MAAO,IAEP1S,kBAAC,OACCA,SAAA,GAAA5E,OAACkO,GAAA,CAAKlV,KAAK,YAAYwY,MAAO,CAAE+F,aAAc,OAAQC,QAAS,SAAW5S,SAAA,yDAA1E,MAAAH,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,KAIA/Q,OAACyX,EAAA,CAAMC,UAAU,WAAWlG,MAAO,CAAE8F,MAAO,QAAUJ,KAAK,SACxDtS,SAAA4R,EAAUpe,IAAI,CAACye,EAAUc,IACxB3X,SAAC,OAECwR,MAAO,CACLgG,QAAS,OACTI,eAAgB,gBAChBC,WAAY,SACZpG,QAAS,QACTqG,aAAcH,EAAQnB,EAAU9e,OAAS,EAAI,oBAAsB,QAGrEkN,SAAA,CAAA5E,SAACkO,GAAA,CAAMtJ,WAASmJ,kBAAhB,MAAAtJ,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACA/Q,SAAC+X,GAAIvG,MAAO,CAAEwG,WAAY,aACvBpT,SAAAgS,EAAeC,SADlB,MAAApS,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,KAVK4G,GADP,GAAAlT,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,UAFJ,MAAAtM,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,SAAAnB,UALF,MAAAtM,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,QAAAnB,SAPF,MAAAtM,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,QAAAnB,UATF,MAAAtM,SAAA,qGAAAC,WAAA,GAAAwN,aAAA,QAAAnB,ICpBSkH,GAAwBzB,IACnC0B,YAAU,KACR,MAAMC,EAAiBC,IACrB,MAAMC,EAAmB7B,EAAU8B,KAAKzB,GAEpCuB,EAAMxY,IAAI2Y,gBAAkB1B,EAASjX,IAAI2Y,iBACvCH,EAAMtB,WAAcD,EAASC,WAC7BsB,EAAMrB,UAAaF,EAASE,UAC5BqB,EAAMpB,YAAeH,EAASG,UAIhCqB,IACFD,EAAMI,iBACNH,EAAiBI,aAMrB,OAFAC,SAASC,iBAAiB,UAAWR,GAE9B,KACLO,SAASE,oBAAoB,UAAWT,KAEzC,CAAC3B,IAEGA,GAIIqC,GAAmB,CAC9BC,gBAAiB,CACflZ,IAAK,IACLkX,SAAS,EACT/I,YAAa,gCAEfgL,aAAc,CACZnZ,IAAK,IACLkX,SAAS,EACT/I,YAAa,yBAEfiL,QAAS,CACPpZ,IAAK,KACLmO,YAAa,qBAEfkL,OAAQ,CACNrZ,IAAK,IACLkX,SAAS,EACT/I,YAAa,wBAEfmL,SAAU,CACRtZ,IAAK,IACLkX,SAAS,EACT/I,YAAa,4BChDXoL,OAAEA,GAAAC,MAAQA,GAAAC,QAAOA,IAAYC,GAC7BC,MAAEA,IAAUjD,EAMLkD,GAAsC,EAAG5U,eACpD,MAAO6U,EAAWC,GAAgB/C,YAAS,GACrCgD,EAAWC,IACXxW,EAAWyW,KACXvE,QAAEA,EAAAhe,MAASA,GAAU8d,KAErB0E,EAAY,CAChB,CACEla,IAAK,IACLqO,cAAO8L,EAAA,QAAD,MAAAtV,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACNiJ,MAAO,aAET,CACEpa,IAAK,cACLqO,cAAOgM,EAAA,QAAD,MAAAxV,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACNiJ,MAAO,cAET,CACEpa,IAAK,YACLqO,cAAOiM,EAAA,QAAD,MAAAzV,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACNiJ,MAAO,YAET,CACEpa,IAAK,YACLqO,cAAOkM,EAAA,QAAD,MAAA1V,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GACNiJ,MAAO,aAQLI,EAAYxf,OAAOuI,OAAOmS,GAAS+E,KAAKC,SAE9C,gBACGhB,EAAA,CAAO9H,MAAO,CAAE+I,UAAW,SAC1B3V,SAAA,CAAA5E,SAACoZ,GAAA,CACCoB,QAAS,KACTC,aAAW,EACXhB,YACAjI,MAAO,CACLkJ,WAAY,OACZC,UAAW,kCAGb/V,SAAA,CAAA5E,SAAC,OAAIwR,MAAO,CACVC,QAAS,OACTC,UAAW,SACXoG,aAAc,qBAEdlT,SAAA5E,SAACuZ,GAAA,CAAMzhB,MAAO,EAAG0Z,MAAO,CAAEoJ,OAAQ,EAAGC,MAAO,WACzCjW,SAAA6U,EAAY,KAAO,uBADtB,MAAAhV,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SALF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GAUA/Q,SAAC8a,EAAA,CACCC,KAAK,SACLC,aAAc,CAAC5X,EAAS6X,UACxBC,MAAOpB,EACP7H,QA/BgB,EAAGrS,UACzB+Z,EAAS/Z,IA+BH4R,MAAO,CAAE2J,OAAQ,cALnB,MAAA1W,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,UAnBF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,YA4BCuI,EAAA,CACC1U,SAAA,CAAA5E,SAACmZ,IAAO3H,MAAO,CACbC,QAAS,SACTiJ,WAAY,OACZC,UAAW,iCACXnD,QAAS,OACTK,WAAY,SACZD,eAAgB,iBAEhBhT,SAAA,CAAA5E,SAACgS,EAAA,CACChZ,KAAK,OACLiV,KAAMwL,EAAYzZ,SAACob,EAAA,QAAD,MAAA3W,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,YAA0BsK,EAAA,QAAD,MAAA5W,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GAC3CkB,QAAS,IAAMyH,GAAcD,GAC7BjI,MAAO,CAAE8J,SAAU,cAJrB,MAAA7W,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,YAOC0G,EAAA,CACC7S,SAAA,CAAA5E,SAACuW,GAAA,CAAsBC,UAAW5b,OAAOuI,OAAO0V,UAAhD,MAAApU,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,SAAAnB,GACCzZ,GACC0I,SAACub,EAAA,CAAM3J,OAAO,QAAQ4J,KAAK,kBAA3B,MAAA/W,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,SAAAnB,GAEDqJ,GACCpa,SAACub,EAAA,CAAM3J,OAAO,aAAa4J,KAAK,mBAAhC,MAAA/W,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,SAAAnB,GAEF/Q,SAACub,EAAA,CAAM3J,OAAO,UAAU4J,KAAK,kBAA7B,MAAA/W,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,SAAAnB,UARF,MAAAtM,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,SAAAnB,UAfF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GA2BA/Q,SAACqZ,IAAQ7H,MAAO,CACdoJ,OAAQ,OACRnJ,QAAS,OACTiJ,WAAY,OACZe,aAAc,MACdlB,UAAW,uBAEV3V,iBAPH,MAAAH,SAAA,yFAAAC,WAAA,IAAAwN,aAAA,QAAAnB,UA5BF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,UA7BF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,mkCCrDE2K,GAAYzlB,EAAM0lB,KAAK,IAAAC,GAAA,IAAMC,OAAO,2BAAmBC,mCACvDC,GAAa9lB,EAAM0lB,KAAK,IAAAC,GAAA,IAAMC,OAAO,4BAAoBC,qCACzDE,GAAW/lB,EAAM0lB,KAAK,IAAAC,GAAA,IAAMC,OAAO,0BAAkBC,qCACrDG,GAAWhmB,EAAM0lB,KAAK,IAAAC,GAAA,IAAMC,OAAO,0BAAkBC,kCAErDI,GAAiB,IACrBlc,SAAC,OAAIwR,MAAO,CACVgG,QAAS,OACTI,eAAgB,SAChBC,WAAY,SACZsE,OAAQ,SAERvX,WAAA5E,OAACoc,EAAA,CAAKlF,KAAK,cAAX,MAAAzS,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,SANF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GAUWsL,GAAsB,IAE/Brc,SAACsc,WAAA,CAASC,SAAUvc,SAACkc,GAAA,QAAD,MAAAzX,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,GAClBnM,kBAAC4X,EAAA,CACC5X,SAAA,CAAA5E,SAACyc,EAAA,CAAMC,KAAK,IAAIle,iBAAUkd,GAAA,QAAD,MAAAjX,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SAAzB,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,YACC0L,EAAA,CAAMC,KAAK,cAAcle,iBAAUud,GAAA,QAAD,MAAAtX,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SAAnC,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,YACC0L,EAAA,CAAMC,KAAK,YAAYle,iBAAUwd,GAAA,QAAD,MAAAvX,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SAAjC,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,YACC0L,EAAA,CAAMC,KAAK,YAAYle,iBAAUyd,GAAA,QAAD,MAAAxX,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SAAjC,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,KACA/Q,OAACyc,EAAA,CAAMC,KAAK,IAAIle,iBAAUme,EAAA,CAASC,GAAG,IAAI7e,SAAO,QAAxB,MAAA0G,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SAAzB,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,UALF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,SADF,MAAAtM,SAAA,yFAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GCdE8L,GAAgB,IAElB7c,SAAC8c,EAAA,CACCtV,OAAQuV,GACRC,MAAO,CACLC,MAAO,CACLC,aAAc,UACdzB,aAAc,IAIlB7W,SAAA5E,SAACmd,GACCvY,SAAA5E,SAACod,EAAA,CACCxY,kBAACgM,GAAA,CACChM,SAAA5E,SAACwZ,GAAA,CACC5U,SAAA5E,SAACqc,GAAA,QAAD,MAAA5X,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SADF,MAAAtM,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SADF,MAAAtM,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,SAAAnB,SADF,MAAAtM,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,QAAAnB,SADF,MAAAtM,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,QAAAnB,SATF,MAAAtM,SAAA,wEAAAC,WAAA,GAAAwN,aAAA,QAAAnB,GCNJpL,aAAW+S,SAAS2E,eAAe,SAAUzjB,OAC3CoG,SAACsd,aAAA,CACC1Y,SAAA5E,SAAC6c,GAAA,QAAD,MAAApY,SAAA,yEAAAC,WAAA,EAAAwN,aAAA,QAAAnB,SADF,MAAAtM,SAAA,yEAAAC,WAAA,EAAAwN,aAAA,QAAAnB", "names": ["React", "require$$0", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "MAYBE_ITERATOR_SYMBOL", "iterator", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "error", "format", "_len2", "arguments", "length", "args", "Array", "_key2", "level", "stack", "ReactDebugCurrentFrame", "getStackAddendum", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "printWarning", "REACT_MODULE_REFERENCE", "getContextName", "type", "displayName", "getComponentNameFromType", "tag", "name", "$$typeof", "_context", "outerType", "innerType", "wrapperName", "functionName", "getWrappedName", "render", "outerName", "lazyComponent", "payload", "_payload", "init", "_init", "x", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "assign", "Object", "<PERSON><PERSON><PERSON><PERSON>", "disabledLog", "__reactDisabledLog", "prefix", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "describeBuiltInComponentFrame", "source", "ownerFn", "Error", "match", "trim", "componentFrameCache", "reentry", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "control", "frame", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previousPrepareStackTrace", "prepareStackTrace", "current", "log", "info", "warn", "group", "groupCollapsed", "groupEnd", "props", "configurable", "enumerable", "value", "writable", "defineProperties", "disableLogs", "Fake", "defineProperty", "set", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "c", "_frame", "replace", "includes", "reenableLogs", "syntheticFrame", "describeUnknownElementTypeFrameInDEV", "isReactComponent", "hasOwnProperty", "loggedTypeFailures", "setCurrentlyValidatingElement", "element", "owner", "_owner", "_source", "setExtraStackFrame", "isArrayImpl", "isArray", "a", "testStringCoercion", "checkKeyStringCoercion", "e", "willCoercionThrow", "toStringTag", "constructor", "typeName", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "ReactCurrentOwner", "RESERVED_PROPS", "key", "ref", "__self", "__source", "jsxDEV", "config", "<PERSON><PERSON><PERSON>", "self", "propName", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasValidRef", "stateNode", "componentName", "warnIfStringRefCannotBeAutoConverted", "defaultProps", "warnAboutAccessingKey", "defineKeyPropWarningGetter", "warnAboutAccessingRef", "defineRefPropWarningGetter", "_store", "freeze", "ReactElement", "propTypesMisspellWarningShown", "ReactCurrentOwner$1", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement$1", "isValidElement", "object", "getDeclarationErrorAddendum", "ownerHasKeyUseWarning", "validateExplicitKey", "parentType", "validated", "currentComponentErrorInfo", "parentName", "getCurrentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "i", "child", "iteratorFn", "maybeIterable", "maybeIterator", "getIteratorFn", "entries", "step", "next", "done", "validatePropTypes", "propTypes", "typeSpecs", "values", "location", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "checkPropTypes", "PropTypes", "getDefaultProps", "isReactClassApproved", "didWarnAboutKeySpread", "jsxDEV$1", "isStaticChildren", "validType", "getModuleId", "isValidElementType", "keys", "typeString", "sourceInfo", "fileName", "lineNumber", "getSourceInfoErrorAddendum", "children", "filter", "k", "<PERSON><PERSON><PERSON><PERSON>", "join", "fragment", "validateFragmentProps", "reactJsxDevRuntime_development", "Fragment", "jsxDevRuntimeModule", "exports", "m", "client", "o", "usingClientEntryPoint", "createRoot", "hydrateRoot", "h", "__esModule", "default", "module", "_typeof", "toPrimitive", "t", "r", "TypeError", "Number", "require$$1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "push", "for<PERSON>ach", "getOwnPropertyDescriptors", "_interopRequireDefault", "zh_CN", "_objectSpread2", "_common", "common", "commonLocale", "yearFormat", "dayFormat", "cellMeridiemFormat", "monthBeforeYear", "locale", "today", "now", "backToToday", "ok", "timeSelect", "dateSelect", "weekSelect", "clear", "week", "month", "year", "previousMonth", "nextMonth", "monthSelect", "yearSelect", "decadeSelect", "previousYear", "nextYear", "previousDecade", "nextDecade", "previousCentury", "nextCentury", "cellDateFormat", "placeholder", "rangePlaceholder", "_zh_CN", "_zh_CN2", "lang", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "rangeYearPlaceholder", "rangeMonthPlaceholder", "rangeQuarterPlaceholder", "rangeWeekPlaceholder", "timePickerLocale", "items_per_page", "jump_to", "jump_to_confirm", "page", "prev_page", "next_page", "prev_5", "next_5", "prev_3", "next_3", "page_size", "_zh_CN3", "_zh_CN4", "typeTemplate", "localeValues", "Pagination", "DatePicker", "TimePicker", "Calendar", "global", "close", "Table", "filterTitle", "filterConfirm", "filterReset", "filterEmptyText", "filterCheckAll", "filterSearchPlaceholder", "emptyText", "selectAll", "selectInvert", "selectNone", "selectionAll", "sortTitle", "expand", "collapse", "triggerDesc", "triggerAsc", "cancelSort", "Modal", "okText", "cancelText", "justOkText", "Tour", "Next", "Previous", "Finish", "Popconfirm", "Transfer", "titles", "searchPlaceholder", "itemUnit", "itemsUnit", "remove", "selectCurrent", "removeCurrent", "deselectAll", "removeAll", "Upload", "uploading", "removeFile", "uploadError", "previewFile", "downloadFile", "Empty", "description", "Icon", "icon", "Text", "edit", "copy", "copied", "Form", "optional", "defaultValidateMessages", "required", "enum", "whitespace", "date", "parse", "invalid", "types", "string", "method", "array", "number", "boolean", "integer", "float", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch", "Image", "preview", "QRCode", "expired", "refresh", "scanned", "ColorPicker", "presetEmpty", "transparent", "singleColor", "gradientColor", "Error<PERSON>ou<PERSON><PERSON>", "Component", "super", "this", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "handleReload", "window", "reload", "style", "padding", "textAlign", "Result", "status", "title", "subTitle", "extra", "<PERSON><PERSON>", "onClick", "columnNumber", "trackedConnections", "getTrackedConnectionState", "api", "fromEntries", "stores", "api2", "getState", "devtools", "devtoolsOptions", "enabled", "anonymousActionType", "store", "options", "extensionConnector", "__vite_import_meta_env__", "__REDUX_DEVTOOLS_EXTENSION__", "_e", "connection", "connectionInformation", "connect", "existingConnection", "newConnection", "extractConnectionInformation", "isRecording", "setState", "nameOrAction", "action", "send", "setStateFromDevtools", "originalIsRecording", "initialState", "store2", "dispatchFromDevtools", "dispatch", "didWarnAboutReservedActionType", "originalDispatch", "subscribe", "_a", "parseJsonThen", "stateFromDevtools", "JSON", "stringify", "nextLiftedState", "lastComputedState", "computedStates", "slice", "stringified", "f", "parsed", "useAppStore", "create", "loading", "strategies", "backtest", "data", "backtestResults", "setLoading", "setStrategies", "addStrategy", "strategy", "removeStrategy", "id", "setBacktestResults", "addBacktestResult", "result", "setError", "clearError", "Typography", "KeyboardShortcutsHelp", "shortcuts", "visible", "setVisible", "useState", "formatShortcut", "shortcut", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "QuestionCircleOutlined", "size", "open", "onCancel", "footer", "width", "marginBottom", "display", "Space", "direction", "index", "justifyContent", "alignItems", "borderBottom", "Tag", "fontFamily", "useKeyboardShortcuts", "useEffect", "handleKeyDown", "event", "matchingShortcut", "find", "toLowerCase", "preventDefault", "callback", "document", "addEventListener", "removeEventListener", "COMMON_SHORTCUTS", "CREATE_STRATEGY", "RUN_BACKTEST", "REFRESH", "EXPORT", "SETTINGS", "Header", "<PERSON><PERSON>", "Content", "Layout", "Title", "AppLayout", "collapsed", "setCollapsed", "navigate", "useNavigate", "useLocation", "menuItems", "DashboardOutlined", "label", "BarChartOutlined", "ApiOutlined", "SettingOutlined", "isLoading", "some", "Boolean", "minHeight", "trigger", "collapsible", "background", "boxShadow", "margin", "color", "<PERSON><PERSON>", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "border", "MenuUnfoldOutlined", "MenuFoldOutlined", "fontSize", "Badge", "text", "borderRadius", "Dashboard", "lazy", "__vitePreload", "import", "__VITE_PRELOAD__", "Strategies", "Backtest", "Settings", "LoadingSpinner", "height", "Spin", "AppRouter", "Suspense", "fallback", "Routes", "Route", "path", "Navigate", "to", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "theme", "token", "colorPrimary", "AntApp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getElementById", "StrictMode"], "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18], "sources": ["../../node_modules/react/cjs/react-jsx-dev-runtime.development.js", "../../node_modules/react/jsx-dev-runtime.js", "../../node_modules/react-dom/client.js", "../../node_modules/@babel/runtime/helpers/interopRequireDefault.js", "../../node_modules/@babel/runtime/helpers/typeof.js", "../../node_modules/@babel/runtime/helpers/toPropertyKey.js", "../../node_modules/@babel/runtime/helpers/toPrimitive.js", "../../node_modules/@babel/runtime/helpers/objectSpread2.js", "../../node_modules/@babel/runtime/helpers/defineProperty.js", "../../node_modules/rc-picker/lib/locale/zh_CN.js", "../../node_modules/rc-picker/lib/locale/common.js", "../../node_modules/antd/lib/time-picker/locale/zh_CN.js", "../../node_modules/antd/lib/date-picker/locale/zh_CN.js", "../../node_modules/antd/lib/calendar/locale/zh_CN.js", "../../node_modules/antd/lib/locale/zh_CN.js", "../../node_modules/rc-pagination/lib/locale/zh_CN.js", "../../node_modules/antd/locale/zh_CN.js", "../../src/components/ErrorBoundary.tsx", "../../node_modules/zustand/esm/middleware.mjs", "../../src/store/index.ts", "../../src/components/KeyboardShortcutsHelp.tsx", "../../src/hooks/useKeyboardShortcuts.ts", "../../src/components/AppLayout.tsx", "../../src/components/AppRouter.tsx", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    {\n      if (hasOwnProperty.call(props, 'key')) {\n        var componentName = getComponentNameFromType(type);\n        var keys = Object.keys(props).filter(function (k) {\n          return k !== 'key';\n        });\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var defineProperty = require(\"./defineProperty.js\");\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nmodule.exports = _objectSpread2, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _common = require(\"./common\");\nvar locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {\n  locale: 'zh_CN',\n  today: '今天',\n  now: '此刻',\n  backToToday: '返回今天',\n  ok: '确定',\n  timeSelect: '选择时间',\n  dateSelect: '选择日期',\n  weekSelect: '选择周',\n  clear: '清除',\n  week: '周',\n  month: '月',\n  year: '年',\n  previousMonth: '上个月 (翻页上键)',\n  nextMonth: '下个月 (翻页下键)',\n  monthSelect: '选择月份',\n  yearSelect: '选择年份',\n  decadeSelect: '选择年代',\n  previousYear: '上一年 (Control键加左方向键)',\n  nextYear: '下一年 (Control键加右方向键)',\n  previousDecade: '上一年代',\n  nextDecade: '下一年代',\n  previousCentury: '上一世纪',\n  nextCentury: '下一世纪',\n  yearFormat: 'YYYY年',\n  cellDateFormat: 'D',\n  monthBeforeYear: false\n});\nvar _default = exports.default = locale;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.commonLocale = void 0;\nvar commonLocale = exports.commonLocale = {\n  yearFormat: 'YYYY',\n  dayFormat: 'D',\n  cellMeridiemFormat: 'A',\n  monthBeforeYear: true\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nconst locale = {\n  placeholder: '请选择时间',\n  rangePlaceholder: ['开始时间', '结束时间']\n};\nvar _default = exports.default = locale;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-picker/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../../time-picker/locale/zh_CN\"));\n// 统一合并为完整的 Locale\nconst locale = {\n  lang: Object.assign({\n    placeholder: '请选择日期',\n    yearPlaceholder: '请选择年份',\n    quarterPlaceholder: '请选择季度',\n    monthPlaceholder: '请选择月份',\n    weekPlaceholder: '请选择周',\n    rangePlaceholder: ['开始日期', '结束日期'],\n    rangeYearPlaceholder: ['开始年份', '结束年份'],\n    rangeMonthPlaceholder: ['开始月份', '结束月份'],\n    rangeQuarterPlaceholder: ['开始季度', '结束季度'],\n    rangeWeekPlaceholder: ['开始周', '结束周']\n  }, _zh_CN.default),\n  timePickerLocale: Object.assign({}, _zh_CN2.default)\n};\n// should add whitespace between char in Button\nlocale.lang.ok = '确定';\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nvar _default = exports.default = locale;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"../../date-picker/locale/zh_CN\"));\nvar _default = exports.default = _zh_CN.default;", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _zh_CN = _interopRequireDefault(require(\"rc-pagination/lib/locale/zh_CN\"));\nvar _zh_CN2 = _interopRequireDefault(require(\"../calendar/locale/zh_CN\"));\nvar _zh_CN3 = _interopRequireDefault(require(\"../date-picker/locale/zh_CN\"));\nvar _zh_CN4 = _interopRequireDefault(require(\"../time-picker/locale/zh_CN\"));\nconst typeTemplate = '${label}不是一个有效的${type}';\nconst localeValues = {\n  locale: 'zh-cn',\n  Pagination: _zh_CN.default,\n  DatePicker: _zh_CN3.default,\n  TimePicker: _zh_CN4.default,\n  Calendar: _zh_CN2.default,\n  // locales for all components\n  global: {\n    placeholder: '请选择',\n    close: '关闭'\n  },\n  Table: {\n    filterTitle: '筛选',\n    filterConfirm: '确定',\n    filterReset: '重置',\n    filterEmptyText: '无筛选项',\n    filterCheckAll: '全选',\n    filterSearchPlaceholder: '在筛选项中搜索',\n    emptyText: '暂无数据',\n    selectAll: '全选当页',\n    selectInvert: '反选当页',\n    selectNone: '清空所有',\n    selectionAll: '全选所有',\n    sortTitle: '排序',\n    expand: '展开行',\n    collapse: '关闭行',\n    triggerDesc: '点击降序',\n    triggerAsc: '点击升序',\n    cancelSort: '取消排序'\n  },\n  Modal: {\n    okText: '确定',\n    cancelText: '取消',\n    justOkText: '知道了'\n  },\n  Tour: {\n    Next: '下一步',\n    Previous: '上一步',\n    Finish: '结束导览'\n  },\n  Popconfirm: {\n    cancelText: '取消',\n    okText: '确定'\n  },\n  Transfer: {\n    titles: ['', ''],\n    searchPlaceholder: '请输入搜索内容',\n    itemUnit: '项',\n    itemsUnit: '项',\n    remove: '删除',\n    selectCurrent: '全选当页',\n    removeCurrent: '删除当页',\n    selectAll: '全选所有',\n    deselectAll: '取消全选',\n    removeAll: '删除全部',\n    selectInvert: '反选当页'\n  },\n  Upload: {\n    uploading: '文件上传中',\n    removeFile: '删除文件',\n    uploadError: '上传错误',\n    previewFile: '预览文件',\n    downloadFile: '下载文件'\n  },\n  Empty: {\n    description: '暂无数据'\n  },\n  Icon: {\n    icon: '图标'\n  },\n  Text: {\n    edit: '编辑',\n    copy: '复制',\n    copied: '复制成功',\n    expand: '展开',\n    collapse: '收起'\n  },\n  Form: {\n    optional: '（可选）',\n    defaultValidateMessages: {\n      default: '字段验证错误${label}',\n      required: '请输入${label}',\n      enum: '${label}必须是其中一个[${enum}]',\n      whitespace: '${label}不能为空字符',\n      date: {\n        format: '${label}日期格式无效',\n        parse: '${label}不能转换为日期',\n        invalid: '${label}是一个无效日期'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label}须为${len}个字符',\n        min: '${label}最少${min}个字符',\n        max: '${label}最多${max}个字符',\n        range: '${label}须在${min}-${max}字符之间'\n      },\n      number: {\n        len: '${label}必须等于${len}',\n        min: '${label}最小值为${min}',\n        max: '${label}最大值为${max}',\n        range: '${label}须在${min}-${max}之间'\n      },\n      array: {\n        len: '须为${len}个${label}',\n        min: '最少${min}个${label}',\n        max: '最多${max}个${label}',\n        range: '${label}数量须在${min}-${max}之间'\n      },\n      pattern: {\n        mismatch: '${label}与模式不匹配${pattern}'\n      }\n    }\n  },\n  Image: {\n    preview: '预览'\n  },\n  QRCode: {\n    expired: '二维码过期',\n    refresh: '点击刷新',\n    scanned: '已扫描'\n  },\n  ColorPicker: {\n    presetEmpty: '暂无',\n    transparent: '无色',\n    singleColor: '单色',\n    gradientColor: '渐变色'\n  }\n};\nvar _default = exports.default = localeValues;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar locale = {\n  // Options\n  items_per_page: '条/页',\n  jump_to: '跳至',\n  jump_to_confirm: '确定',\n  page: '页',\n  // Pagination\n  prev_page: '上一页',\n  next_page: '下一页',\n  prev_5: '向前 5 页',\n  next_5: '向后 5 页',\n  prev_3: '向前 3 页',\n  next_3: '向后 3 页',\n  page_size: '页码'\n};\nvar _default = exports.default = locale;", "module.exports = require('../lib/locale/zh_CN');", "import React, { Component, ReactNode } from 'react';\nimport { Result, Button } from 'antd';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{ padding: '50px', textAlign: 'center' }}>\n          <Result\n            status=\"error\"\n            title=\"Something went wrong\"\n            subTitle={this.state.error?.message || 'An unexpected error occurred'}\n            extra={\n              <Button type=\"primary\" onClick={this.handleReload}>\n                Reload Page\n              </Button>\n            }\n          />\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (_e) {\n  }\n  if (!extensionConnector) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && enabled) {\n      console.warn(\n        \"[zustand devtools middleware] Please install/enable Redux devtools extension\"\n      );\n    }\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (_e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst oldImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    getStorage: () => localStorage,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage;\n  try {\n    storage = options.getStorage();\n  } catch (_e) {\n  }\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const thenableSerialize = toThenable(options.serialize);\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    let errorInSync;\n    const thenable = thenableSerialize({ state, version: options.version }).then(\n      (serializedValue) => storage.setItem(options.name, serializedValue)\n    ).catch((e) => {\n      errorInSync = e;\n    });\n    if (errorInSync) {\n      throw errorInSync;\n    }\n    return thenable;\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => cb(get()));\n    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {\n      if (storageValue) {\n        return options.deserialize(storageValue);\n      }\n    }).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return deserializedStorageValue.state;\n        }\n      }\n    }).then((migratedState) => {\n      var _a2;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      return setItem();\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.getStorage) {\n        storage = newOptions.getStorage();\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  hydrate();\n  return stateFromStorage || configResult;\n};\nconst newImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persistImpl = (config, baseOptions) => {\n  if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\"\n      );\n    }\n    return oldImpl(config, baseOptions);\n  }\n  return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "import { create } from 'zustand';\nimport { devtools } from 'zustand/middleware';\nimport type { Strategy, BacktestResult, ApiError } from '@/types';\n\n// Main application state\ninterface AppState {\n  // Loading states\n  loading: {\n    strategies: boolean;\n    backtest: boolean;\n    data: boolean;\n  };\n  \n  // Data\n  strategies: Strategy[];\n  backtestResults: BacktestResult[];\n  \n  // Error handling\n  error: ApiError | null;\n  \n  // Actions\n  setLoading: (key: keyof AppState['loading'], value: boolean) => void;\n  setStrategies: (strategies: Strategy[]) => void;\n  addStrategy: (strategy: Strategy) => void;\n  removeStrategy: (id: string) => void;\n  setBacktestResults: (results: BacktestResult[]) => void;\n  addBacktestResult: (result: BacktestResult) => void;\n  setError: (error: ApiError | null) => void;\n  clearError: () => void;\n}\n\nexport const useAppStore = create<AppState>()(\n  devtools(\n    (set) => ({\n      // Initial state\n      loading: {\n        strategies: false,\n        backtest: false,\n        data: false,\n      },\n      strategies: [],\n      backtestResults: [],\n      error: null,\n\n      // Actions\n      setLoading: (key, value) =>\n        set((state) => ({\n          loading: { ...state.loading, [key]: value },\n        })),\n\n      setStrategies: (strategies) => set({ strategies }),\n\n      addStrategy: (strategy) =>\n        set((state) => ({\n          strategies: [...state.strategies, strategy],\n        })),\n\n      removeStrategy: (id) =>\n        set((state) => ({\n          strategies: state.strategies.filter((s) => s.id !== id),\n        })),\n\n      setBacktestResults: (backtestResults) => set({ backtestResults }),\n\n      addBacktestResult: (result) =>\n        set((state) => ({\n          backtestResults: [result, ...state.backtestResults],\n        })),\n\n      setError: (error) => set({ error }),\n\n      clearError: () => set({ error: null }),\n    }),\n    {\n      name: 'trading-app-store',\n    }\n  )\n);", "import React, { useState } from 'react';\nimport { Button, Modal, Typography, Space, Tag } from 'antd';\nimport { QuestionCircleOutlined } from '@ant-design/icons';\n\nconst { Text } = Typography;\n\ninterface Shortcut {\n  key: string;\n  ctrlKey?: boolean;\n  altKey?: boolean;\n  shiftKey?: boolean;\n  description: string;\n}\n\ninterface KeyboardShortcutsHelpProps {\n  shortcuts: Shortcut[];\n}\n\nexport const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({ shortcuts }) => {\n  const [visible, setVisible] = useState(false);\n\n  const formatShortcut = (shortcut: Shortcut) => {\n    const keys = [];\n    if (shortcut.ctrlKey) keys.push('Ctrl');\n    if (shortcut.altKey) keys.push('Alt');\n    if (shortcut.shiftKey) keys.push('Shift');\n    keys.push(shortcut.key);\n    return keys.join(' + ');\n  };\n\n  return (\n    <>\n      <Button\n        type=\"text\"\n        icon={<QuestionCircleOutlined />}\n        onClick={() => setVisible(true)}\n        title=\"Keyboard Shortcuts\"\n        size=\"small\"\n      />\n      \n      <Modal\n        title=\"Keyboard Shortcuts\"\n        open={visible}\n        onCancel={() => setVisible(false)}\n        footer={null}\n        width={500}\n      >\n        <div>\n          <Text type=\"secondary\" style={{ marginBottom: '16px', display: 'block' }}>\n            Use these keyboard shortcuts to navigate faster:\n          </Text>\n          \n          <Space direction=\"vertical\" style={{ width: '100%' }} size=\"middle\">\n            {shortcuts.map((shortcut, index) => (\n              <div \n                key={index}\n                style={{ \n                  display: 'flex', \n                  justifyContent: 'space-between', \n                  alignItems: 'center',\n                  padding: '8px 0',\n                  borderBottom: index < shortcuts.length - 1 ? '1px solid #f0f0f0' : 'none',\n                }}\n              >\n                <Text>{shortcut.description}</Text>\n                <Tag style={{ fontFamily: 'monospace' }}>\n                  {formatShortcut(shortcut)}\n                </Tag>\n              </div>\n            ))}\n          </Space>\n        </div>\n      </Modal>\n    </>\n  );\n};", "import { useEffect } from 'react';\n\ninterface KeyboardShortcut {\n  key: string;\n  ctrlKey?: boolean;\n  altKey?: boolean;\n  shiftKey?: boolean;\n  callback: () => void;\n  description: string;\n}\n\nexport const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      const matchingShortcut = shortcuts.find(shortcut => {\n        return (\n          event.key.toLowerCase() === shortcut.key.toLowerCase() &&\n          !!event.ctrlKey === !!shortcut.ctrlKey &&\n          !!event.altKey === !!shortcut.altKey &&\n          !!event.shiftKey === !!shortcut.shiftKey\n        );\n      });\n\n      if (matchingShortcut) {\n        event.preventDefault();\n        matchingShortcut.callback();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [shortcuts]);\n\n  return shortcuts;\n};\n\n// Common keyboard shortcuts\nexport const COMMON_SHORTCUTS = {\n  CREATE_STRATEGY: {\n    key: 'n',\n    ctrlKey: true,\n    description: 'Create new strategy (Ctrl+N)',\n  },\n  RUN_BACKTEST: {\n    key: 'r',\n    ctrlKey: true,\n    description: 'Run backtest (Ctrl+R)',\n  },\n  REFRESH: {\n    key: 'F5',\n    description: 'Refresh data (F5)',\n  },\n  EXPORT: {\n    key: 'e',\n    ctrlKey: true,\n    description: 'Export data (Ctrl+E)',\n  },\n  SETTINGS: {\n    key: ',',\n    ctrlKey: true,\n    description: 'Open settings (Ctrl+,)',\n  },\n};", "import React, { useState } from 'react';\nimport { Layout, Menu, Button, Typography, Space, Badge } from 'antd';\nimport {\n  DashboardOutlined,\n  BarChartOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  ApiOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAppStore } from '@/store';\nimport { KeyboardShortcutsHelp } from '@/components/KeyboardShortcutsHelp';\nimport { COMMON_SHORTCUTS } from '@/hooks/useKeyboardShortcuts';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nexport const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { loading, error } = useAppStore();\n\n  const menuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: 'Dashboard',\n    },\n    {\n      key: '/strategies',\n      icon: <BarChartOutlined />,\n      label: 'Strategies',\n    },\n    {\n      key: '/backtest',\n      icon: <ApiOutlined />,\n      label: 'Backtest',\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: 'Settings',\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  const isLoading = Object.values(loading).some(Boolean);\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px 0 rgba(29,35,41,.05)',\n        }}\n      >\n        <div style={{ \n          padding: '16px', \n          textAlign: 'center',\n          borderBottom: '1px solid #f0f0f0',\n        }}>\n          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\n            {collapsed ? 'TS' : 'Trading System'}\n          </Title>\n        </div>\n        \n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          style={{ border: 'none' }}\n        />\n      </Sider>\n      \n      <Layout>\n        <Header style={{ \n          padding: '0 16px', \n          background: '#fff',\n          boxShadow: '0 2px 8px 0 rgba(29,35,41,.05)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n        }}>\n          <Button\n            type=\"text\"\n            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => setCollapsed(!collapsed)}\n            style={{ fontSize: '16px' }}\n          />\n          \n          <Space>\n            <KeyboardShortcutsHelp shortcuts={Object.values(COMMON_SHORTCUTS)} />\n            {error && (\n              <Badge status=\"error\" text=\"API Error\" />\n            )}\n            {isLoading && (\n              <Badge status=\"processing\" text=\"Loading...\" />\n            )}\n            <Badge status=\"success\" text=\"Connected\" />\n          </Space>\n        </Header>\n        \n        <Content style={{ \n          margin: '16px',\n          padding: '24px',\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};", "import React, { Suspense } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Spin } from 'antd';\n\n// Lazy load pages for better performance\nconst Dashboard = React.lazy(() => import('@/pages/Dashboard'));\nconst Strategies = React.lazy(() => import('@/pages/Strategies'));\nconst Backtest = React.lazy(() => import('@/pages/Backtest'));\nconst Settings = React.lazy(() => import('@/pages/Settings'));\n\nconst LoadingSpinner = () => (\n  <div style={{ \n    display: 'flex', \n    justifyContent: 'center', \n    alignItems: 'center', \n    height: '200px' \n  }}>\n    <Spin size=\"large\" />\n  </div>\n);\n\nexport const AppRouter: React.FC = () => {\n  return (\n    <Suspense fallback={<LoadingSpinner />}>\n      <Routes>\n        <Route path=\"/\" element={<Dashboard />} />\n        <Route path=\"/strategies\" element={<Strategies />} />\n        <Route path=\"/backtest\" element={<Backtest />} />\n        <Route path=\"/settings\" element={<Settings />} />\n        <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n      </Routes>\n    </Suspense>\n  );\n};", "import React from 'react';\nimport { ConfigProvider, App as AntApp } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ErrorBoundary } from '@/components/ErrorBoundary';\nimport { AppLayout } from '@/components/AppLayout';\nimport { AppRouter } from '@/components/AppRouter';\nimport '@/styles/core.css';\n\nconst App: React.FC = () => {\n  return (\n    <ConfigProvider \n      locale={zhCN}\n      theme={{\n        token: {\n          colorPrimary: '#1890ff',\n          borderRadius: 6,\n        },\n      }}\n    >\n      <AntApp>\n        <BrowserRouter>\n          <ErrorBoundary>\n            <AppLayout>\n              <AppRouter />\n            </AppLayout>\n          </ErrorBoundary>\n        </BrowserRouter>\n      </AntApp>\n    </ConfigProvider>\n  );\n};\n\nexport default App;", "import { StrictMode } from 'react';\nimport { createRoot } from 'react-dom/client';\nimport App from './App';\nimport './index.css';\n\ncreateRoot(document.getElementById('root')!).render(\n  <StrictMode>\n    <App />\n  </StrictMode>,\n);"], "file": "assets/index-D0Fokv29.js"}