{"version": 3, "file": "export-jIOAvY8l.js", "sources": ["../../src/utils/validation.ts", "../../src/utils/export.ts"], "sourcesContent": ["import dayjs from 'dayjs';\n\n// Strategy validation\nexport const validateStrategyName = (name: string): string | null => {\n  if (!name || name.trim().length === 0) {\n    return 'Strategy name is required';\n  }\n  if (name.length > 50) {\n    return 'Strategy name must be less than 50 characters';\n  }\n  return null;\n};\n\nexport const validateStrategyParameters = (\n  type: string, \n  parameters: Record<string, any>\n): string | null => {\n  switch (type) {\n    case 'moving_average':\n      if (!parameters.short_window || parameters.short_window < 1) {\n        return 'Short window must be a positive number';\n      }\n      if (!parameters.long_window || parameters.long_window < 1) {\n        return 'Long window must be a positive number';\n      }\n      if (parameters.short_window >= parameters.long_window) {\n        return 'Short window must be less than long window';\n      }\n      break;\n    \n    case 'rsi':\n      if (!parameters.period || parameters.period < 1 || parameters.period > 100) {\n        return 'RSI period must be between 1 and 100';\n      }\n      if (!parameters.overbought || parameters.overbought < 50 || parameters.overbought > 100) {\n        return 'Overbought level must be between 50 and 100';\n      }\n      if (!parameters.oversold || parameters.oversold < 0 || parameters.oversold > 50) {\n        return 'Oversold level must be between 0 and 50';\n      }\n      break;\n  }\n  return null;\n};\n\n// Backtest validation\nexport const validateBacktestRequest = (\n  strategyId: string,\n  symbol: string,\n  startDate: string,\n  endDate: string,\n  initialCapital: number\n): string | null => {\n  if (!strategyId) {\n    return 'Strategy is required';\n  }\n  \n  if (!symbol || symbol.trim().length === 0) {\n    return 'Symbol is required';\n  }\n  \n  if (!startDate) {\n    return 'Start date is required';\n  }\n  \n  if (!endDate) {\n    return 'End date is required';\n  }\n  \n  const start = dayjs(startDate);\n  const end = dayjs(endDate);\n  \n  if (!start.isValid()) {\n    return 'Invalid start date';\n  }\n  \n  if (!end.isValid()) {\n    return 'Invalid end date';\n  }\n  \n  if (start.isAfter(end)) {\n    return 'Start date must be before end date';\n  }\n  \n  if (end.isAfter(dayjs())) {\n    return 'End date cannot be in the future';\n  }\n  \n  if (initialCapital <= 0) {\n    return 'Initial capital must be positive';\n  }\n  \n  if (initialCapital > 10000000) {\n    return 'Initial capital cannot exceed $10,000,000';\n  }\n  \n  return null;\n};\n\n// Date validation\nexport const validateDateRange = (startDate: string, endDate: string): string | null => {\n  const start = dayjs(startDate);\n  const end = dayjs(endDate);\n  \n  if (!start.isValid() || !end.isValid()) {\n    return 'Invalid date format';\n  }\n  \n  if (start.isAfter(end)) {\n    return 'Start date must be before end date';\n  }\n  \n  const diffDays = end.diff(start, 'day');\n  if (diffDays < 1) {\n    return 'Date range must be at least 1 day';\n  }\n  \n  if (diffDays > 365 * 5) {\n    return 'Date range cannot exceed 5 years';\n  }\n  \n  return null;\n};\n\n// Symbol validation\nexport const validateSymbol = (symbol: string): string | null => {\n  if (!symbol || symbol.trim().length === 0) {\n    return 'Symbol is required';\n  }\n  \n  const cleanSymbol = symbol.trim().toUpperCase();\n  \n  if (!/^[A-Z]{1,10}$/.test(cleanSymbol)) {\n    return 'Symbol must contain only letters and be 1-10 characters long';\n  }\n  \n  return null;\n};", "import type { BacktestResult, Strategy } from '@/types';\n\n// Export backtest results to CSV\nexport const exportBacktestResultsToCSV = (results: BacktestResult[]): void => {\n  if (results.length === 0) {\n    return;\n  }\n\n  const headers = [\n    'ID',\n    'Strategy ID',\n    'Symbol',\n    'Start Date',\n    'End Date',\n    'Initial Capital',\n    'Final Capital',\n    'Total Return (%)',\n    'Sharpe Ratio',\n    'Max Drawdown (%)',\n    'Trade Count',\n    'Created At',\n  ];\n\n  const csvContent = [\n    headers.join(','),\n    ...results.map(result => [\n      result.id,\n      result.strategy_id,\n      result.symbol,\n      result.start_date,\n      result.end_date,\n      result.initial_capital,\n      result.final_capital,\n      (result.total_return * 100).toFixed(2),\n      result.sharpe_ratio.toFixed(3),\n      (Math.abs(result.max_drawdown) * 100).toFixed(2),\n      result.trade_count,\n      result.created_at,\n    ].join(','))\n  ].join('\\n');\n\n  downloadCSV(csvContent, 'backtest-results.csv');\n};\n\n// Export strategies to CSV\nexport const exportStrategiesToCSV = (strategies: Strategy[]): void => {\n  if (strategies.length === 0) {\n    return;\n  }\n\n  const headers = [\n    'ID',\n    'Name',\n    'Type',\n    'Parameters',\n    'Created At',\n  ];\n\n  const csvContent = [\n    headers.join(','),\n    ...strategies.map(strategy => [\n      strategy.id,\n      `\"${strategy.name}\"`,\n      strategy.type,\n      `\"${JSON.stringify(strategy.parameters)}\"`,\n      strategy.created_at,\n    ].join(','))\n  ].join('\\n');\n\n  downloadCSV(csvContent, 'strategies.csv');\n};\n\n// Export backtest results to JSON\nexport const exportBacktestResultsToJSON = (results: BacktestResult[]): void => {\n  const jsonContent = JSON.stringify(results, null, 2);\n  downloadJSON(jsonContent, 'backtest-results.json');\n};\n\n// Export strategies to JSON\nexport const exportStrategiesToJSON = (strategies: Strategy[]): void => {\n  const jsonContent = JSON.stringify(strategies, null, 2);\n  downloadJSON(jsonContent, 'strategies.json');\n};\n\n// Helper function to download CSV\nconst downloadCSV = (content: string, filename: string): void => {\n  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n  downloadBlob(blob, filename);\n};\n\n// Helper function to download JSON\nconst downloadJSON = (content: string, filename: string): void => {\n  const blob = new Blob([content], { type: 'application/json;charset=utf-8;' });\n  downloadBlob(blob, filename);\n};\n\n// Helper function to download blob\nconst downloadBlob = (blob: Blob, filename: string): void => {\n  const link = document.createElement('a');\n  const url = URL.createObjectURL(blob);\n  \n  link.setAttribute('href', url);\n  link.setAttribute('download', filename);\n  link.style.visibility = 'hidden';\n  \n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  \n  URL.revokeObjectURL(url);\n};"], "names": ["validateStrategyName", "name", "trim", "length", "validateStrategyParameters", "type", "parameters", "short_window", "long_window", "period", "overbought", "oversold", "validateBacktestRequest", "strategyId", "symbol", "startDate", "endDate", "initialCapital", "start", "dayjs", "end", "<PERSON><PERSON><PERSON><PERSON>", "isAfter", "exportBacktestResultsToCSV", "results", "csv<PERSON><PERSON>nt", "join", "map", "result", "id", "strategy_id", "start_date", "end_date", "initial_capital", "final_capital", "total_return", "toFixed", "sharpe_ratio", "Math", "abs", "max_drawdown", "trade_count", "created_at", "downloadCSV", "exportStrategiesToCSV", "strategies", "strategy", "JSON", "stringify", "content", "filename", "blob", "Blob", "downloadBlob", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL"], "mappings": "4CAGO,MAAMA,EAAwBC,GAC9BA,GAA+B,IAAvBA,EAAKC,OAAOC,OAGrBF,EAAKE,OAAS,GACT,gDAEF,KALE,4BAQEC,EAA6B,CACxCC,EACAC,KAEA,OAAQD,GACN,IAAK,iBACH,IAAKC,EAAWC,cAAgBD,EAAWC,aAAe,EACxD,MAAO,yCAET,IAAKD,EAAWE,aAAeF,EAAWE,YAAc,EACtD,MAAO,wCAET,GAAIF,EAAWC,cAAgBD,EAAWE,YACxC,MAAO,6CAET,MAEF,IAAK,MACH,IAAKF,EAAWG,QAAUH,EAAWG,OAAS,GAAKH,EAAWG,OAAS,IACrE,MAAO,uCAET,IAAKH,EAAWI,YAAcJ,EAAWI,WAAa,IAAMJ,EAAWI,WAAa,IAClF,MAAO,8CAET,IAAKJ,EAAWK,UAAYL,EAAWK,SAAW,GAAKL,EAAWK,SAAW,GAC3E,MAAO,0CAIb,OAAO,MAIIC,EAA0B,CACrCC,EACAC,EACAC,EACAC,EACAC,KAEA,IAAKJ,EACH,MAAO,uBAGT,IAAKC,GAAmC,IAAzBA,EAAOZ,OAAOC,OAC3B,MAAO,qBAGT,IAAKY,EACH,MAAO,yBAGT,IAAKC,EACH,MAAO,uBAGT,MAAME,EAAQC,EAAMJ,GACdK,EAAMD,EAAMH,GAElB,OAAKE,EAAMG,UAIND,EAAIC,UAILH,EAAMI,QAAQF,GACT,qCAGLA,EAAIE,QAAQH,KACP,mCAGLF,GAAkB,EACb,mCAGLA,EAAiB,IACZ,4CAGF,KAnBE,mBAJA,sBCtEEM,EAA8BC,IACzC,GAAuB,IAAnBA,EAAQrB,OACV,OAGF,MAeMsB,EAAa,CAfH,CACd,KACA,cACA,SACA,aACA,WACA,kBACA,gBACA,mBACA,eACA,mBACA,cACA,cAIQC,KAAK,QACVF,EAAQG,IAAIC,GAAU,CACvBA,EAAOC,GACPD,EAAOE,YACPF,EAAOd,OACPc,EAAOG,WACPH,EAAOI,SACPJ,EAAOK,gBACPL,EAAOM,eACgB,IAAtBN,EAAOO,cAAoBC,QAAQ,GACpCR,EAAOS,aAAaD,QAAQ,IACK,IAAhCE,KAAKC,IAAIX,EAAOY,eAAqBJ,QAAQ,GAC9CR,EAAOa,YACPb,EAAOc,YACPhB,KAAK,OACPA,KAAK,MAEPiB,EAAYlB,EAAY,yBAIbmB,EAAyBC,IACpC,GAA0B,IAAtBA,EAAW1C,OACb,OAGF,MAQMsB,EAAa,CARH,CACd,KACA,OACA,OACA,aACA,cAIQC,KAAK,QACVmB,EAAWlB,IAAImB,GAAY,CAC5BA,EAASjB,GACT,IAAIiB,EAAS7C,QACb6C,EAASzC,KACT,IAAI0C,KAAKC,UAAUF,EAASxC,eAC5BwC,EAASJ,YACThB,KAAK,OACPA,KAAK,MAEPiB,EAAYlB,EAAY,mBAgBpBkB,EAAc,CAACM,EAAiBC,KACpC,MAAMC,EAAO,IAAIC,KAAK,CAACH,GAAU,CAAE5C,KAAM,4BACzCgD,EAAaF,EAAMD,IAUfG,EAAe,CAACF,EAAYD,KAChC,MAAMI,EAAOC,SAASC,cAAc,KAC9BC,EAAMC,IAAIC,gBAAgBR,GAEhCG,EAAKM,aAAa,OAAQH,GAC1BH,EAAKM,aAAa,WAAYV,GAC9BI,EAAKO,MAAMC,WAAa,SAExBP,SAASQ,KAAKC,YAAYV,GAC1BA,EAAKW,QACLV,SAASQ,KAAKG,YAAYZ,GAE1BI,IAAIS,gBAAgBV"}