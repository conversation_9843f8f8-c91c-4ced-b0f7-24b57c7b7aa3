import{G as a}from"./ui-vendor-BjEg0IST.js";const r=(a,r=2)=>a.toFixed(r),e=(a,r=2)=>`${(100*a).toFixed(r)}%`,o=(a,r="USD")=>new Intl.NumberFormat("en-US",{style:"currency",currency:r}).format(a),s=(r,e="YYYY-MM-DD")=>a(r).format(e),t=a=>({moving_average:"Moving Average",rsi:"RSI",bollinger_bands:"Bollinger Bands",macd:"MACD",custom:"Custom Strategy"}[a]||a),n=a=>a>0?"#52c41a":a<0?"#ff4d4f":"#8c8c8c";export{r as a,t as b,s as c,o as d,e as f,n as g};
//# sourceMappingURL=format-DdDemiMk.js.map
