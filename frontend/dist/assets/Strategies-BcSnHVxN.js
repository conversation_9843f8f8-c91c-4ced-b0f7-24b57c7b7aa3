import{u as e,a as r,j as o,C as t}from"./index-D0Fokv29.js";import{r as s}from"./react-vendor-eITpvC6N.js";import{u as n}from"./useApi-edF36RG5.js";import{b as a,c as i}from"./format-DdDemiMk.js";import{e as l,v as d,a as m}from"./export-jIOAvY8l.js";import{F as c,T as u,S as P,B as g,t as h,u as j,n as y,v as N,M as v,I as p,w as x,P as f,x as b,y as S,z as E}from"./ui-vendor-BjEg0IST.js";import"./store-vendor-84rN_0kr.js";import"./utils-vendor-C-1G2k3o.js";const{Title:D}=u,{Option:V}=x,U=()=>{const{strategies:u,loading:U}=e(),{loadStrategies:w,createStrategy:I,deleteStrategy:C}=n(),[T,k]=s.useState(!1),[F]=c.useForm(),[_,R]=s.useState("");s.useEffect(()=>{w()},[w]),r([{...t.CREATE_STRATEGY,callback:()=>k(!0)},{...t.REFRESH,callback:()=>w()},{...t.EXPORT,callback:()=>u.length>0&&l(u)}]);const A=[{title:"Name",dataIndex:"name",key:"name",sorter:(e,r)=>e.name.localeCompare(r.name)},{title:"Type",dataIndex:"type",key:"type",render:e=>a(e),filters:[{text:"Moving Average",value:"moving_average"},{text:"RSI",value:"rsi"}],onFilter:(e,r)=>r.type===e},{title:"Parameters",dataIndex:"parameters",key:"parameters",render:e=>o.jsxDEV("div",{children:Object.entries(e).map(([e,r])=>o.jsxDEV("div",{style:{fontSize:"12px"},children:[e,": ",r]},e,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:166,columnNumber:13},void 0))},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:164,columnNumber:9},void 0)},{title:"Created",dataIndex:"created_at",key:"created_at",render:e=>i(e),sorter:(e,r)=>new Date(e.created_at).getTime()-new Date(r.created_at).getTime()},{title:"Actions",key:"actions",render:(e,r)=>o.jsxDEV(P,{children:o.jsxDEV(f,{title:"Are you sure you want to delete this strategy?",onConfirm:()=>(async e=>{await C(e)})(r.id),okText:"Yes",cancelText:"No",children:o.jsxDEV(g,{type:"text",danger:!0,icon:o.jsxDEV(b,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:194,columnNumber:21},void 0),size:"small"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:191,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:185,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:184,columnNumber:9},void 0)}];return o.jsxDEV("div",{children:[o.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[o.jsxDEV("div",{children:[o.jsxDEV(D,{level:2,children:"Trading Strategies"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:212,columnNumber:11},void 0),o.jsxDEV("p",{style:{color:"#8c8c8c",margin:0},children:"Manage your trading strategies and their parameters"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:213,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:211,columnNumber:9},void 0),o.jsxDEV(P,{children:[u.length>0&&o.jsxDEV(g,{icon:o.jsxDEV(h,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:220,columnNumber:21},void 0),onClick:()=>l(u),children:"Export CSV"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:219,columnNumber:13},void 0),o.jsxDEV(g,{type:"primary",icon:o.jsxDEV(j,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:228,columnNumber:19},void 0),onClick:()=>k(!0),children:"Create Strategy"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:226,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:217,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:205,columnNumber:7},void 0),o.jsxDEV(y,{children:o.jsxDEV(N,{columns:A,dataSource:u,rowKey:"id",loading:U.strategies,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`Total ${e} strategies`}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:237,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:236,columnNumber:7},void 0),o.jsxDEV(v,{title:"Create New Strategy",open:T,onCancel:()=>{k(!1),F.resetFields(),R("")},footer:null,width:600,children:o.jsxDEV(c,{form:F,layout:"vertical",onFinish:async e=>{const r=d(e.name);if(r)return void E.error(r);const o=m(e.type,e.parameters||{});if(o)return void E.error(o);const t={name:e.name,type:e.type,parameters:e.parameters||{}};await I(t)&&(k(!1),F.resetFields(),R(""))},children:[o.jsxDEV(c.Item,{name:"name",label:"Strategy Name",rules:[{required:!0,message:"Please input strategy name!"}],children:o.jsxDEV(p,{placeholder:"Enter strategy name"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:272,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:267,columnNumber:11},void 0),o.jsxDEV(c.Item,{name:"type",label:"Strategy Type",rules:[{required:!0,message:"Please select strategy type!"}],children:o.jsxDEV(x,{placeholder:"Select strategy type",onChange:R,children:[o.jsxDEV(V,{value:"moving_average",children:"Moving Average"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:284,columnNumber:15},void 0),o.jsxDEV(V,{value:"rsi",children:"RSI"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:285,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:280,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:275,columnNumber:11},void 0),(()=>{switch(_){case"moving_average":return o.jsxDEV(o.Fragment,{children:[o.jsxDEV(c.Item,{name:["parameters","short_window"],label:"Short Window",rules:[{required:!0,message:"Please input short window!"}],children:o.jsxDEV(S,{min:1,max:100,placeholder:"e.g., 10"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:97,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:92,columnNumber:13},void 0),o.jsxDEV(c.Item,{name:["parameters","long_window"],label:"Long Window",rules:[{required:!0,message:"Please input long window!"}],children:o.jsxDEV(S,{min:1,max:200,placeholder:"e.g., 30"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:104,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:99,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:91,columnNumber:11},void 0);case"rsi":return o.jsxDEV(o.Fragment,{children:[o.jsxDEV(c.Item,{name:["parameters","period"],label:"RSI Period",rules:[{required:!0,message:"Please input RSI period!"}],children:o.jsxDEV(S,{min:1,max:100,placeholder:"e.g., 14"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:117,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:112,columnNumber:13},void 0),o.jsxDEV(c.Item,{name:["parameters","overbought"],label:"Overbought Level",rules:[{required:!0,message:"Please input overbought level!"}],children:o.jsxDEV(S,{min:50,max:100,placeholder:"e.g., 70"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:124,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:119,columnNumber:13},void 0),o.jsxDEV(c.Item,{name:["parameters","oversold"],label:"Oversold Level",rules:[{required:!0,message:"Please input oversold level!"}],children:o.jsxDEV(S,{min:0,max:50,placeholder:"e.g., 30"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:131,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:126,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:111,columnNumber:11},void 0);default:return null}})(),o.jsxDEV(c.Item,{style:{marginBottom:0,textAlign:"right"},children:o.jsxDEV(P,{children:[o.jsxDEV(g,{onClick:()=>{k(!1),F.resetFields(),R("")},children:"Cancel"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:293,columnNumber:15},void 0),o.jsxDEV(g,{type:"primary",htmlType:"submit",loading:U.strategies,children:"Create Strategy"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:300,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:292,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:291,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:262,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:251,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Strategies.tsx",lineNumber:204,columnNumber:5},void 0)};export{U as default};
//# sourceMappingURL=Strategies-BcSnHVxN.js.map
