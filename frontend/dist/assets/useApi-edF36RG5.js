import{r as t}from"./react-vendor-eITpvC6N.js";import{a as e}from"./utils-vendor-C-1G2k3o.js";import{u as a}from"./index-D0Fokv29.js";import{z as s}from"./ui-vendor-BjEg0IST.js";const r="http://localhost:8000",c={STRATEGIES:"/api/strategies",BACKTEST:"/api/backtest",DATA:"/api/data",HEALTH:"/api/health"},n=3e4,i={INITIAL_CAPITAL:1e5,COMMISSION:.001,REFRESH_INTERVAL:5e3};const l=new class{client;constructor(){this.client=e.create({baseURL:r,timeout:n,headers:{"Content-Type":"application/json"}}),this.client.interceptors.request.use(t=>t,t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,t=>{const e={message:t.response?.data?.message||t.message||"Unknown error",status:t.response?.status||500,details:t.response?.data};return Promise.reject(e)})}async healthCheck(){try{return await this.client.get(c.HEALTH),!0}catch{return!1}}async getStrategies(){return(await this.client.get(c.STRATEGIES)).data.data}async createStrategy(t){return(await this.client.post(c.STRATEGIES,t)).data.data}async deleteStrategy(t){await this.client.delete(`${c.STRATEGIES}/${t}`)}async runBacktest(t){return(await this.client.post(c.BACKTEST,t)).data.data}async getBacktestHistory(){return(await this.client.get(`${c.BACKTEST}/history`)).data.data}async getMarketData(t,e,a){return(await this.client.get(`${c.DATA}/${t}`,{params:{start_date:e,end_date:a}})).data.data}},u=()=>{const{setLoading:e,setStrategies:r,addStrategy:c,removeStrategy:n,setBacktestResults:i,addBacktestResult:u,setError:o,clearError:y}=a(),d=t.useCallback((t,e)=>{o(t),s.error(t.message||`Failed to ${e}`)},[o]);return{loadStrategies:t.useCallback(async()=>{try{e("strategies",!0),y();const t=await l.getStrategies();return r(t),t}catch(t){return d(t,"load strategies"),[]}finally{e("strategies",!1)}},[e,r,y,d]),createStrategy:t.useCallback(async t=>{try{e("strategies",!0),y();const a=await l.createStrategy(t);return c(a),s.success("Strategy created successfully"),a}catch(a){return d(a,"create strategy"),null}finally{e("strategies",!1)}},[e,c,y,d]),deleteStrategy:t.useCallback(async t=>{try{return e("strategies",!0),y(),await l.deleteStrategy(t),n(t),s.success("Strategy deleted successfully"),!0}catch(a){return d(a,"delete strategy"),!1}finally{e("strategies",!1)}},[e,n,y,d]),runBacktest:t.useCallback(async t=>{try{e("backtest",!0),y();const a=await l.runBacktest(t);return u(a),s.success("Backtest completed successfully"),a}catch(a){return d(a,"run backtest"),null}finally{e("backtest",!1)}},[e,u,y,d]),loadBacktestHistory:t.useCallback(async()=>{try{e("backtest",!0),y();const t=await l.getBacktestHistory();return i(t),t}catch(t){return d(t,"load backtest history"),[]}finally{e("backtest",!1)}},[e,i,y,d]),loadMarketData:t.useCallback(async(t,a,s)=>{try{e("data",!0),y();return await l.getMarketData(t,a,s)}catch(r){return d(r,"load market data"),[]}finally{e("data",!1)}},[e,y,d]),checkHealth:t.useCallback(async()=>{try{return await l.healthCheck()}catch(t){return!1}},[])}};export{i as D,u};
//# sourceMappingURL=useApi-edF36RG5.js.map
