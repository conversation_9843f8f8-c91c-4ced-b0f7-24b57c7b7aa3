{"version": 3, "file": "Settings-B2j9IhFO.js", "sources": ["../../src/pages/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Form, \n  Input, \n  InputNumber, \n  Button, \n  Typography, \n  Space,\n  Divider,\n  Switch,\n  Select,\n  message,\n  Alert,\n} from 'antd';\nimport { SaveOutlined, ReloadOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { useApi } from '@/hooks/useApi';\nimport { DEFAULT_CONFIG } from '@/config/api';\n\nconst { Title, Paragraph } = Typography;\nconst { Option } = Select;\n\ninterface SystemSettings {\n  api_url: string;\n  initial_capital: number;\n  commission: number;\n  refresh_interval: number;\n  auto_refresh: boolean;\n  theme: 'light' | 'dark';\n  language: 'en' | 'zh';\n}\n\nconst Settings: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [isConnected, setIsConnected] = useState(false);\n  const { checkHealth } = useApi();\n\n  const [settings, setSettings] = useState<SystemSettings>({\n    api_url: 'http://localhost:8000',\n    initial_capital: DEFAULT_CONFIG.INITIAL_CAPITAL,\n    commission: DEFAULT_CONFIG.COMMISSION,\n    refresh_interval: DEFAULT_CONFIG.REFRESH_INTERVAL,\n    auto_refresh: true,\n    theme: 'light',\n    language: 'zh',\n  });\n\n  useEffect(() => {\n    // Load settings from localStorage\n    const savedSettings = localStorage.getItem('trading-system-settings');\n    if (savedSettings) {\n      const parsed = JSON.parse(savedSettings);\n      setSettings(parsed);\n      form.setFieldsValue(parsed);\n    }\n    \n    // Check API connection\n    checkApiConnection();\n  }, []);\n\n  const checkApiConnection = async () => {\n    try {\n      const connected = await checkHealth();\n      setIsConnected(connected);\n    } catch (error) {\n      setIsConnected(false);\n    }\n  };\n\n  const handleSaveSettings = async (values: SystemSettings) => {\n    setLoading(true);\n    try {\n      // Save to localStorage\n      localStorage.setItem('trading-system-settings', JSON.stringify(values));\n      setSettings(values);\n      message.success('Settings saved successfully');\n      \n      // Check connection with new API URL if changed\n      if (values.api_url !== settings.api_url) {\n        await checkApiConnection();\n      }\n    } catch (error) {\n      message.error('Failed to save settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResetSettings = () => {\n    const defaultSettings: SystemSettings = {\n      api_url: 'http://localhost:8000',\n      initial_capital: DEFAULT_CONFIG.INITIAL_CAPITAL,\n      commission: DEFAULT_CONFIG.COMMISSION,\n      refresh_interval: DEFAULT_CONFIG.REFRESH_INTERVAL,\n      auto_refresh: true,\n      theme: 'light',\n      language: 'zh',\n    };\n    \n    setSettings(defaultSettings);\n    form.setFieldsValue(defaultSettings);\n    message.info('Settings reset to defaults');\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>System Settings</Title>\n        <Paragraph type=\"secondary\">\n          Configure your trading system preferences and connection settings\n        </Paragraph>\n      </div>\n\n      <Card>\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={settings}\n          onFinish={handleSaveSettings}\n        >\n          {/* API Configuration */}\n          <Title level={4}>API Configuration</Title>\n          \n          <Form.Item\n            name=\"api_url\"\n            label=\"API URL\"\n            rules={[\n              { required: true, message: 'Please input API URL!' },\n              { type: 'url', message: 'Please enter a valid URL!' },\n            ]}\n            extra=\"The base URL for the trading system API\"\n          >\n            <Input placeholder=\"http://localhost:8000\" />\n          </Form.Item>\n\n          <div style={{ marginBottom: '16px' }}>\n            <Alert\n              message={\n                <Space>\n                  <span>API Status:</span>\n                  {isConnected ? (\n                    <span style={{ color: '#52c41a' }}>\n                      <CheckCircleOutlined /> Connected\n                    </span>\n                  ) : (\n                    <span style={{ color: '#ff4d4f' }}>\n                      Disconnected\n                    </span>\n                  )}\n                  <Button \n                    type=\"link\" \n                    size=\"small\" \n                    onClick={checkApiConnection}\n                    icon={<ReloadOutlined />}\n                  >\n                    Test Connection\n                  </Button>\n                </Space>\n              }\n              type={isConnected ? 'success' : 'warning'}\n              showIcon\n            />\n          </div>\n\n          <Divider />\n\n          {/* Trading Configuration */}\n          <Title level={4}>Trading Configuration</Title>\n          \n          <Form.Item\n            name=\"initial_capital\"\n            label=\"Default Initial Capital\"\n            rules={[{ required: true, message: 'Please input initial capital!' }]}\n            extra=\"Default amount for new backtests\"\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              min={1000}\n              max={10000000}\n              formatter={(value) => `$ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n              parser={(value) => {\n                const num = Number(value!.replace(/\\$\\s?|(,*)/g, ''));\n                return Math.max(1000, Math.min(10000000, num)) as any;\n              }}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"commission\"\n            label=\"Default Commission Rate\"\n            rules={[{ required: true, message: 'Please input commission rate!' }]}\n            extra=\"Commission rate as a decimal (e.g., 0.001 for 0.1%)\"\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              min={0}\n              max={0.1}\n              step={0.0001}\n              precision={4}\n            />\n          </Form.Item>\n\n          <Divider />\n\n          {/* UI Configuration */}\n          <Title level={4}>User Interface</Title>\n          \n          <Form.Item\n            name=\"auto_refresh\"\n            label=\"Auto Refresh\"\n            valuePropName=\"checked\"\n            extra=\"Automatically refresh data at regular intervals\"\n          >\n            <Switch />\n          </Form.Item>\n\n          <Form.Item\n            name=\"refresh_interval\"\n            label=\"Refresh Interval (ms)\"\n            rules={[{ required: true, message: 'Please input refresh interval!' }]}\n            extra=\"How often to refresh data when auto refresh is enabled\"\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              min={1000}\n              max={60000}\n              step={1000}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"theme\"\n            label=\"Theme\"\n            extra=\"Choose your preferred theme\"\n          >\n            <Select>\n              <Option value=\"light\">Light</Option>\n              <Option value=\"dark\">Dark</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"language\"\n            label=\"Language\"\n            extra=\"Choose your preferred language\"\n          >\n            <Select>\n              <Option value=\"en\">English</Option>\n              <Option value=\"zh\">中文</Option>\n            </Select>\n          </Form.Item>\n\n          <Divider />\n\n          {/* Action Buttons */}\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Space>\n              <Button \n                type=\"primary\" \n                htmlType=\"submit\" \n                icon={<SaveOutlined />}\n                loading={loading}\n              >\n                Save Settings\n              </Button>\n              <Button onClick={handleResetSettings}>\n                Reset to Defaults\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default Settings;"], "names": ["Title", "Paragraph", "Typography", "Option", "Select", "Settings", "form", "Form", "useForm", "loading", "setLoading", "useState", "isConnected", "setIsConnected", "checkHealth", "useApi", "settings", "setSettings", "api_url", "initial_capital", "DEFAULT_CONFIG", "INITIAL_CAPITAL", "commission", "COMMISSION", "refresh_interval", "REFRESH_INTERVAL", "auto_refresh", "theme", "language", "useEffect", "savedSettings", "localStorage", "getItem", "parsed", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkApiConnection", "async", "connected", "error", "children", "jsxDEV", "style", "marginBottom", "level", "fileName", "lineNumber", "columnNumber", "this", "type", "Card", "layout", "initialValues", "onFinish", "values", "setItem", "stringify", "message", "success", "<PERSON><PERSON>", "name", "label", "rules", "required", "extra", "Input", "placeholder", "<PERSON><PERSON>", "Space", "color", "CheckCircleOutlined", "<PERSON><PERSON>", "size", "onClick", "icon", "ReloadOutlined", "showIcon", "Divider", "InputNumber", "width", "min", "max", "formatter", "value", "replace", "parser", "num", "Number", "Math", "step", "precision", "valuePropName", "Switch", "htmlType", "SaveOutlined", "defaultSettings", "info"], "mappings": "sVAmBA,MAAMA,MAAEA,EAAAC,UAAOA,GAAcC,GACvBC,OAAEA,GAAWC,EAYbC,EAAqB,KACzB,MAAOC,GAAQC,EAAKC,WACbC,EAASC,GAAcC,EAAAA,UAAS,IAChCC,EAAaC,GAAkBF,EAAAA,UAAS,IACzCG,YAAEA,GAAgBC,KAEjBC,EAAUC,GAAeN,WAAyB,CACvDO,QAAS,wBACTC,gBAAiBC,EAAeC,gBAChCC,WAAYF,EAAeG,WAC3BC,iBAAkBJ,EAAeK,iBACjCC,cAAc,EACdC,MAAO,QACPC,SAAU,OAGZC,EAAAA,UAAU,KAER,MAAMC,EAAgBC,aAAaC,QAAQ,2BAC3C,GAAIF,EAAe,CACjB,MAAMG,EAASC,KAAKC,MAAML,GAC1Bb,EAAYgB,GACZ3B,EAAK8B,eAAeH,EACtB,CAGAI,KACC,IAEH,MAAMA,EAAqBC,UACzB,IACE,MAAMC,QAAkBzB,IACxBD,EAAe0B,EACjB,OAASC,GACP3B,GAAe,EACjB,GAsCF,gBACG,MAAA,CACC4B,SAAA,CAAAC,EAAAA,OAAC,MAAA,CAAIC,MAAO,CAAEC,aAAc,QAC1BH,SAAA,CAAAC,SAAC1C,EAAA,CAAM6C,MAAO,EAAGJ,SAAA,wBAAjB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,GACAP,SAACzC,EAAA,CAAUiD,KAAK,YAAYT,SAAA,0EAA5B,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,YAOCE,EAAA,CACCV,SAAAC,EAAAA,OAACnC,EAAA,CACCD,OACA8C,OAAO,WACPC,cAAerC,EACfsC,SAjDmBhB,MAAOiB,IAChC7C,GAAW,GACX,IAEEqB,aAAayB,QAAQ,0BAA2BtB,KAAKuB,UAAUF,IAC/DtC,EAAYsC,GACZG,EAAQC,QAAQ,+BAGZJ,EAAOrC,UAAYF,EAASE,eACxBmB,GAEV,OAASG,GACPkB,EAAQlB,MAAM,0BAChB,CAAA,QACE9B,GAAW,EACb,GAoCM+B,SAAA,CAAAC,SAAC1C,EAAA,CAAM6C,MAAO,EAAGJ,SAAA,0BAAjB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAEAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,UACLC,MAAM,UACNC,MAAO,CACL,CAAEC,UAAU,EAAMN,QAAS,yBAC3B,CAAER,KAAM,MAAOQ,QAAS,8BAE1BO,MAAM,0CAENxB,WAAAC,OAACwB,EAAA,CAAMC,YAAY,8BAAnB,GAAA,EAAA,CAAArB,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SATF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YAYC,MAAA,CAAIN,MAAO,CAAEC,aAAc,QAC1BH,SAAAC,EAAAA,OAAC0B,EAAA,CACCV,iBACGW,EAAA,CACC5B,SAAA,CAAAC,EAAAA,OAAC,QAAKD,SAAA,oBAAN,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACCrC,EACC8B,EAAAA,OAAC,OAAA,CAAKC,MAAO,CAAE2B,MAAO,WACpB7B,SAAA,GAAAC,OAAC6B,EAAA,QAAD,GAAA,EAAA,CAAAzB,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAAuB,oBADzB,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YAIC,OAAA,CAAKN,MAAO,CAAE2B,MAAO,WAAa7B,SAAA,qBAAnC,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAIFP,EAAAA,OAAC8B,EAAA,CACCtB,KAAK,OACLuB,KAAK,QACLC,QAASrC,EACTsC,cAAOC,EAAA,CAAA,OAAD,GAAA,EAAA,CAAA9B,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACPR,SAAA,wBALD,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAXF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAqBFC,KAAMtC,EAAc,UAAY,UAChCiE,UAAQ,QAxBV,GAAA,EAAA,CAAA/B,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YA6BC6B,EAAA,QAAD,GAAA,EAAA,CAAAhC,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAGAP,SAAC1C,EAAA,CAAM6C,MAAO,EAAGJ,SAAA,8BAAjB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAEAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,kBACLC,MAAM,0BACNC,MAAO,CAAC,CAAEC,UAAU,EAAMN,QAAS,kCACnCO,MAAM,mCAENxB,SAAAC,EAAAA,OAACqC,EAAA,CACCpC,MAAO,CAAEqC,MAAO,QAChBC,IAAK,IACLC,IAAK,IACLC,UAAYC,GAAU,KAAKA,IAAQC,QAAQ,wBAAyB,KACpEC,OAASF,IACP,MAAMG,EAAMC,OAAOJ,EAAOC,QAAQ,cAAe,KACjD,OAAOI,KAAKP,IAAI,IAAMO,KAAKR,IAAI,IAAUM,WAP7C,GAAA,EAAA,CAAAzC,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SANF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAkBAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,aACLC,MAAM,0BACNC,MAAO,CAAC,CAAEC,UAAU,EAAMN,QAAS,kCACnCO,MAAM,sDAENxB,SAAAC,EAAAA,OAACqC,EAAA,CACCpC,MAAO,CAAEqC,MAAO,QAChBC,IAAK,EACLC,IAAK,GACLQ,KAAM,KACNC,UAAW,QALb,GAAA,EAAA,CAAA7C,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SANF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YAeC6B,EAAA,QAAD,GAAA,EAAA,CAAAhC,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAGAP,SAAC1C,EAAA,CAAM6C,MAAO,EAAGJ,SAAA,uBAAjB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAEAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,eACLC,MAAM,eACN8B,cAAc,UACd3B,MAAM,kDAENxB,kBAACoD,EAAA,CAAA,OAAD,GAAA,EAAA,CAAA/C,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SANF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GASAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,mBACLC,MAAM,wBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMN,QAAS,mCACnCO,MAAM,yDAENxB,SAAAC,EAAAA,OAACqC,EAAA,CACCpC,MAAO,CAAEqC,MAAO,QAChBC,IAAK,IACLC,IAAK,IACLQ,KAAM,UAJR,GAAA,EAAA,CAAA5C,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SANF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAcAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,QACLC,MAAM,QACNG,MAAM,8BAENxB,kBAACrC,EAAA,CACCqC,SAAA,CAAAC,SAACvC,EAAA,CAAOiF,MAAM,QAAQ3C,SAAA,cAAtB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAP,SAACvC,EAAA,CAAOiF,MAAM,OAAO3C,SAAA,aAArB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAWAP,EAAAA,OAACnC,EAAKqD,KAAL,CACCC,KAAK,WACLC,MAAM,WACNG,MAAM,iCAENxB,kBAACrC,EAAA,CACCqC,SAAA,CAAAC,SAACvC,EAAA,CAAOiF,MAAM,KAAK3C,SAAA,gBAAnB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAP,SAACvC,EAAA,CAAOiF,MAAM,KAAK3C,SAAA,WAAnB,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YAWC6B,EAAA,QAAD,GAAA,EAAA,CAAAhC,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAGAP,EAAAA,OAACnC,EAAKqD,KAAL,CAAUjB,MAAO,CAAEC,aAAc,GAChCH,SAAAC,EAAAA,OAAC2B,EAAA,CACC5B,SAAA,CAAAC,EAAAA,OAAC8B,EAAA,CACCtB,KAAK,UACL4C,SAAS,SACTnB,cAAOoB,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAjD,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACNxC,UACDgC,SAAA,sBALD,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAQAP,SAAC8B,EAAA,CAAOE,QAjLQ,KAC1B,MAAMsB,EAAkC,CACtC9E,QAAS,wBACTC,gBAAiBC,EAAeC,gBAChCC,WAAYF,EAAeG,WAC3BC,iBAAkBJ,EAAeK,iBACjCC,cAAc,EACdC,MAAO,QACPC,SAAU,MAGZX,EAAY+E,GACZ1F,EAAK8B,eAAe4D,GACpBtC,EAAQuC,KAAK,+BAoKmCxD,SAAA,0BAAtC,GAAA,EAAA,CAAAK,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UATF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UA7IF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,UARF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC"}