{"version": 3, "file": "format-DdDemiMk.js", "sources": ["../../src/utils/format.ts"], "sourcesContent": ["import dayjs from 'dayjs';\n\n// Number formatting utilities\nexport const formatNumber = (value: number, decimals: number = 2): string => {\n  return value.toFixed(decimals);\n};\n\nexport const formatPercentage = (value: number, decimals: number = 2): string => {\n  return `${(value * 100).toFixed(decimals)}%`;\n};\n\nexport const formatCurrency = (value: number, currency: string = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(value);\n};\n\nexport const formatLargeNumber = (value: number): string => {\n  if (value >= 1e9) return `${(value / 1e9).toFixed(1)}B`;\n  if (value >= 1e6) return `${(value / 1e6).toFixed(1)}M`;\n  if (value >= 1e3) return `${(value / 1e3).toFixed(1)}K`;\n  return value.toString();\n};\n\n// Date formatting utilities\nexport const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {\n  return dayjs(date).format(format);\n};\n\nexport const formatDateTime = (date: string | Date): string => {\n  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');\n};\n\nexport const formatRelativeTime = (date: string | Date): string => {\n  const now = dayjs();\n  const target = dayjs(date);\n  const diffMinutes = now.diff(target, 'minute');\n  \n  if (diffMinutes < 1) return 'just now';\n  if (diffMinutes < 60) return `${diffMinutes} minutes ago`;\n  \n  const diffHours = now.diff(target, 'hour');\n  if (diffHours < 24) return `${diffHours} hours ago`;\n  \n  const diffDays = now.diff(target, 'day');\n  if (diffDays < 7) return `${diffDays} days ago`;\n  \n  return target.format('YYYY-MM-DD');\n};\n\n// Strategy type formatting\nexport const formatStrategyType = (type: string): string => {\n  const typeMap: Record<string, string> = {\n    'moving_average': 'Moving Average',\n    'rsi': 'RSI',\n    'bollinger_bands': 'Bollinger Bands',\n    'macd': 'MACD',\n    'custom': 'Custom Strategy',\n  };\n  return typeMap[type] || type;\n};\n\n// Color utilities for charts and indicators\nexport const getPerformanceColor = (value: number): string => {\n  if (value > 0) return '#52c41a'; // Green for positive\n  if (value < 0) return '#ff4d4f'; // Red for negative\n  return '#8c8c8c'; // Gray for neutral\n};\n\nexport const getRiskColor = (risk: 'low' | 'medium' | 'high'): string => {\n  const colorMap = {\n    low: '#52c41a',\n    medium: '#faad14',\n    high: '#ff4d4f',\n  };\n  return colorMap[risk];\n};"], "names": ["formatNumber", "value", "decimals", "toFixed", "formatPercentage", "formatCurrency", "currency", "Intl", "NumberFormat", "style", "format", "formatDate", "date", "dayjs", "formatStrategyType", "type", "moving_average", "rsi", "bollinger_bands", "macd", "custom", "getPerformanceColor"], "mappings": "4CAGO,MAAMA,EAAe,CAACC,EAAeC,EAAmB,IACtDD,EAAME,QAAQD,GAGVE,EAAmB,CAACH,EAAeC,EAAmB,IAC1D,IAAY,IAARD,GAAaE,QAAQD,MAGrBG,EAAiB,CAACJ,EAAeK,EAAmB,QACxD,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPH,aACCI,OAAOT,GAWCU,EAAa,CAACC,EAAqBF,EAAiB,eACxDG,EAAMD,GAAMF,OAAOA,GAyBfI,EAAsBC,IACO,CACtCC,eAAkB,iBAClBC,IAAO,MACPC,gBAAmB,kBACnBC,KAAQ,OACRC,OAAU,mBAEGL,IAASA,GAIbM,EAAuBpB,GAC9BA,EAAQ,EAAU,UAClBA,EAAQ,EAAU,UACf"}