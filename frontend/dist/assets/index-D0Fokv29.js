const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-CykE3o7k.js","assets/react-vendor-eITpvC6N.js","assets/useApi-edF36RG5.js","assets/utils-vendor-C-1G2k3o.js","assets/ui-vendor-BjEg0IST.js","assets/format-DdDemiMk.js","assets/store-vendor-84rN_0kr.js","assets/Strategies-BcSnHVxN.js","assets/export-jIOAvY8l.js","assets/Backtest-B_y4aKob.js","assets/Settings-B2j9IhFO.js"])))=>i.map(i=>d[i]);
import{e,f as r,g as t,r as o,u as n,h as s,R as a,i,j as c,N as l,B as u}from"./react-vendor-eITpvC6N.js";import{R as d,B as p,T as f,a as m,M as y,S as h,b as v,L as b,c as P,d as j,e as g,f as x,g as N,h as E,i as _,j as S,k,C as O,A as D}from"./ui-vendor-BjEg0IST.js";import{c as R}from"./store-vendor-84rN_0kr.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver(e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)}).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();var w,T,A={exports:{}},U={};function C(){return w||(w=1,function(){var r=e(),t=Symbol.for("react.element"),o=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen"),y=Symbol.iterator;var h=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function v(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),o=1;o<r;o++)t[o-1]=arguments[o];!function(e,r,t){var o=h.ReactDebugCurrentFrame.getStackAddendum();""!==o&&(r+="%s",t=t.concat([o]));var n=t.map(function(e){return String(e)});n.unshift("Warning: "+r),Function.prototype.apply.call(console[e],console,n)}("error",e,t)}var b;function P(e){return e.displayName||"Context"}function j(e){if(null==e)return null;if("number"==typeof e.tag&&v("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case n:return"Fragment";case o:return"Portal";case a:return"Profiler";case s:return"StrictMode";case u:return"Suspense";case d:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case c:return P(e)+".Consumer";case i:return P(e._context)+".Provider";case l:return function(e,r,t){var o=e.displayName;if(o)return o;var n=r.displayName||r.name||"";return""!==n?t+"("+n+")":t}(e,e.render,"ForwardRef");case p:var r=e.displayName||null;return null!==r?r:j(e.type)||"Memo";case f:var t=e,m=t._payload,y=t._init;try{return j(y(m))}catch(h){return null}}return null}b=Symbol.for("react.module.reference");var g,x,N,E,_,S,k,O=Object.assign,D=0;function R(){}R.__reactDisabledLog=!0;var w,T=h.ReactCurrentDispatcher;function A(e,r,t){if(void 0===w)try{throw Error()}catch(n){var o=n.stack.trim().match(/\n( *(at )?)/);w=o&&o[1]||""}return"\n"+w+e}var C,V=!1,$="function"==typeof WeakMap?WeakMap:Map;function L(e,r){if(!e||V)return"";var t,o=C.get(e);if(void 0!==o)return o;V=!0;var n,s=Error.prepareStackTrace;Error.prepareStackTrace=void 0,n=T.current,T.current=null,function(){if(0===D){g=console.log,x=console.info,N=console.warn,E=console.error,_=console.group,S=console.groupCollapsed,k=console.groupEnd;var e={configurable:!0,enumerable:!0,value:R,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}D++}();try{if(r){var a=function(){throw Error()};if(Object.defineProperty(a.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(a,[])}catch(m){t=m}Reflect.construct(e,[],a)}else{try{a.call()}catch(m){t=m}e.call(a.prototype)}}else{try{throw Error()}catch(m){t=m}e()}}catch(y){if(y&&t&&"string"==typeof y.stack){for(var i=y.stack.split("\n"),c=t.stack.split("\n"),l=i.length-1,u=c.length-1;l>=1&&u>=0&&i[l]!==c[u];)u--;for(;l>=1&&u>=0;l--,u--)if(i[l]!==c[u]){if(1!==l||1!==u)do{if(l--,--u<0||i[l]!==c[u]){var d="\n"+i[l].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),"function"==typeof e&&C.set(e,d),d}}while(l>=1&&u>=0);break}}}finally{V=!1,T.current=n,function(){if(0===--D){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:O({},e,{value:g}),info:O({},e,{value:x}),warn:O({},e,{value:N}),error:O({},e,{value:E}),group:O({},e,{value:_}),groupCollapsed:O({},e,{value:S}),groupEnd:O({},e,{value:k})})}D<0&&v("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=s}var p=e?e.displayName||e.name:"",f=p?A(p):"";return"function"==typeof e&&C.set(e,f),f}function I(e,r,t){if(null==e)return"";if("function"==typeof e)return L(e,!(!(o=e.prototype)||!o.isReactComponent));var o;if("string"==typeof e)return A(e);switch(e){case u:return A("Suspense");case d:return A("SuspenseList")}if("object"==typeof e)switch(e.$$typeof){case l:return L(e.render,!1);case p:return I(e.type,r,t);case f:var n=e,s=n._payload,a=n._init;try{return I(a(s),r,t)}catch(i){}}return""}C=new $;var F=Object.prototype.hasOwnProperty,M={},K=h.ReactDebugCurrentFrame;function B(e){if(e){var r=e._owner,t=I(e.type,e._source,r?r.type:null);K.setExtraStackFrame(t)}else K.setExtraStackFrame(null)}var Y=Array.isArray;function z(e){return Y(e)}function H(e){return""+e}function W(e){if(function(e){try{return H(e),!1}catch(r){return!0}}(e))return v("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(e){return"function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}(e)),H(e)}var J,q,X,G=h.ReactCurrentOwner,Q={key:!0,ref:!0,__self:!0,__source:!0};X={};function Z(e,r,o,n,s){var a,i={},c=null,l=null;for(a in void 0!==o&&(W(o),c=""+o),function(e){if(F.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return void 0!==e.key}(r)&&(W(r.key),c=""+r.key),function(e){if(F.call(e,"ref")){var r=Object.getOwnPropertyDescriptor(e,"ref").get;if(r&&r.isReactWarning)return!1}return void 0!==e.ref}(r)&&(l=r.ref,function(e,r){if("string"==typeof e.ref&&G.current&&r&&G.current.stateNode!==r){var t=j(G.current.type);X[t]||(v('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',j(G.current.type),e.ref),X[t]=!0)}}(r,s)),r)F.call(r,a)&&!Q.hasOwnProperty(a)&&(i[a]=r[a]);if(e&&e.defaultProps){var u=e.defaultProps;for(a in u)void 0===i[a]&&(i[a]=u[a])}if(c||l){var d="function"==typeof e?e.displayName||e.name||"Unknown":e;c&&function(e,r){var t=function(){J||(J=!0,v("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}(i,d),l&&function(e,r){var t=function(){q||(q=!0,v("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"ref",{get:t,configurable:!0})}(i,d)}return function(e,r,o,n,s,a,i){var c={$$typeof:t,type:e,key:r,ref:o,props:i,_owner:a,_store:{}};return Object.defineProperty(c._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(c,"_self",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(c,"_source",{configurable:!1,enumerable:!1,writable:!1,value:s}),Object.freeze&&(Object.freeze(c.props),Object.freeze(c)),c}(e,c,l,s,n,G.current,i)}var ee,re=h.ReactCurrentOwner,te=h.ReactDebugCurrentFrame;function oe(e){if(e){var r=e._owner,t=I(e.type,e._source,r?r.type:null);te.setExtraStackFrame(t)}else te.setExtraStackFrame(null)}function ne(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}function se(){if(re.current){var e=j(re.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}ee=!1;var ae={};function ie(e,r){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var t=function(e){var r=se();if(!r){var t="string"==typeof e?e:e.displayName||e.name;t&&(r="\n\nCheck the top-level render call using <"+t+">.")}return r}(r);if(!ae[t]){ae[t]=!0;var o="";e&&e._owner&&e._owner!==re.current&&(o=" It was passed a child from "+j(e._owner.type)+"."),oe(e),v('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,o),oe(null)}}}function ce(e,r){if("object"==typeof e)if(z(e))for(var t=0;t<e.length;t++){var o=e[t];ne(o)&&ie(o,r)}else if(ne(e))e._store&&(e._store.validated=!0);else if(e){var n=function(e){if(null===e||"object"!=typeof e)return null;var r=y&&e[y]||e["@@iterator"];return"function"==typeof r?r:null}(e);if("function"==typeof n&&n!==e.entries)for(var s,a=n.call(e);!(s=a.next()).done;)ne(s.value)&&ie(s.value,r)}}function le(e){var r,t=e.type;if(null!=t&&"string"!=typeof t){if("function"==typeof t)r=t.propTypes;else{if("object"!=typeof t||t.$$typeof!==l&&t.$$typeof!==p)return;r=t.propTypes}if(r){var o=j(t);!function(e,r,t,o,n){var s=Function.call.bind(F);for(var a in e)if(s(e,a)){var i=void 0;try{if("function"!=typeof e[a]){var c=Error((o||"React class")+": "+t+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[a]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw c.name="Invariant Violation",c}i=e[a](r,a,o,t,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(l){i=l}!i||i instanceof Error||(B(n),v("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",o||"React class",t,a,typeof i),B(null)),i instanceof Error&&!(i.message in M)&&(M[i.message]=!0,B(n),v("Failed %s type: %s",t,i.message),B(null))}}(r,e.props,"prop",o,e)}else if(void 0!==t.PropTypes&&!ee){ee=!0,v("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",j(t)||"Unknown")}"function"!=typeof t.getDefaultProps||t.getDefaultProps.isReactClassApproved||v("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}var ue={};var de=function(e,r,o,y,h,P){var g=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===a||e===s||e===u||e===d||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===p||e.$$typeof===i||e.$$typeof===c||e.$$typeof===l||e.$$typeof===b||void 0!==e.getModuleId)}(e);if(!g){var x="";(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(x+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var N,E=function(e){return void 0!==e?"\n\nCheck your code at "+e.fileName.replace(/^.*[\\\/]/,"")+":"+e.lineNumber+".":""}(h);x+=E||se(),null===e?N="null":z(e)?N="array":void 0!==e&&e.$$typeof===t?(N="<"+(j(e.type)||"Unknown")+" />",x=" Did you accidentally export a JSX literal instead of a component?"):N=typeof e,v("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",N,x)}var _=Z(e,r,o,h,P);if(null==_)return _;if(g){var S=r.children;if(void 0!==S)if(y)if(z(S)){for(var k=0;k<S.length;k++)ce(S[k],e);Object.freeze&&Object.freeze(S)}else v("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ce(S,e)}if(F.call(r,"key")){var O=j(e),D=Object.keys(r).filter(function(e){return"key"!==e}),R=D.length>0?"{key: someKey, "+D.join(": ..., ")+": ...}":"{key: someKey}";if(!ue[O+R])v('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',R,O,D.length>0?"{"+D.join(": ..., ")+": ...}":"{}",O),ue[O+R]=!0}return e===n?function(e){for(var r=Object.keys(e.props),t=0;t<r.length;t++){var o=r[t];if("children"!==o&&"key"!==o){oe(e),v("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",o),oe(null);break}}null!==e.ref&&(oe(e),v("Invalid attribute `ref` supplied to `React.Fragment`."),oe(null))}(_):le(_),_};U.Fragment=n,U.jsxDEV=de}()),U}var V,$=(T||(T=1,A.exports=C()),A.exports),L={};var I,F=function(){if(V)return L;V=1;var e=r(),t=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;return L.createRoot=function(r,o){t.usingClientEntryPoint=!0;try{return e.createRoot(r,o)}finally{t.usingClientEntryPoint=!1}},L.hydrateRoot=function(r,o,n){t.usingClientEntryPoint=!0;try{return e.hydrateRoot(r,o,n)}finally{t.usingClientEntryPoint=!1}},L}(),M={},K={exports:{}};function B(){return I||(I=1,(e=K).exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports),K.exports;var e}var Y,z={};var H,W={},J={},q={},X={exports:{}},G={exports:{}},Q={exports:{}},Z={exports:{}};function ee(){return H||(H=1,function(e){function r(t){return e.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,r(t)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(Z)),Z.exports}var re,te,oe,ne,se={exports:{}};function ae(){return te||(te=1,e=Q,r=ee().default,t=function(){return re||(re=1,e=se,r=ee().default,e.exports=function(e,t){if("object"!=r(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports),se.exports;var e,r}(),e.exports=function(e){var o=t(e,"string");return"symbol"==r(o)?o:o+""},e.exports.__esModule=!0,e.exports.default=e.exports),Q.exports;var e,r,t}function ie(){return ne||(ne=1,function(e){var r=function(){return oe||(oe=1,e=G,r=ae(),e.exports=function(e,t,o){return(t=r(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},e.exports.__esModule=!0,e.exports.default=e.exports),G.exports;var e,r}();function t(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}e.exports=function(e){for(var o=1;o<arguments.length;o++){var n=null!=arguments[o]?arguments[o]:{};o%2?t(Object(n),!0).forEach(function(t){r(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e},e.exports.__esModule=!0,e.exports.default=e.exports}(X)),X.exports}var ce,le,ue={};function de(){if(le)return q;le=1;var e=B().default;Object.defineProperty(q,"__esModule",{value:!0}),q.default=void 0;var r=e(ie()),t=(ce||(ce=1,Object.defineProperty(ue,"__esModule",{value:!0}),ue.commonLocale=void 0,ue.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),ue),o=(0,r.default)((0,r.default)({},t.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});return q.default=o,q}var pe,fe,me,ye,he,ve,be={};function Pe(){if(pe)return be;pe=1,Object.defineProperty(be,"__esModule",{value:!0}),be.default=void 0;return be.default={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]},be}function je(){if(fe)return J;fe=1;var e=B().default;Object.defineProperty(J,"__esModule",{value:!0}),J.default=void 0;var r=e(de()),t=e(Pe());const o={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},r.default),timePickerLocale:Object.assign({},t.default)};return o.lang.ok="确定",J.default=o,J}function ge(){if(me)return W;me=1;var e=B().default;Object.defineProperty(W,"__esModule",{value:!0}),W.default=void 0;var r=e(je());return W.default=r.default,W}function xe(){if(ye)return M;ye=1;var e=B().default;Object.defineProperty(M,"__esModule",{value:!0}),M.default=void 0;var r=e((Y||(Y=1,Object.defineProperty(z,"__esModule",{value:!0}),z.default=void 0,z.default={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"}),z)),t=e(ge()),o=e(je()),n=e(Pe());const s="${label}不是一个有效的${type}",a={locale:"zh-cn",Pagination:r.default,DatePicker:o.default,TimePicker:n.default,Calendar:t.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:s,method:s,array:s,object:s,number:s,date:s,boolean:s,integer:s,float:s,regexp:s,email:s,url:s,hex:s},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};return M.default=a,M}function Ne(){return ve?he:(ve=1,he=xe())}const Ee=t(Ne());class _e extends o.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){}handleReload=()=>{window.location.reload()};render(){return this.state.hasError?$.jsxDEV("div",{style:{padding:"50px",textAlign:"center"},children:$.jsxDEV(d,{status:"error",title:"Something went wrong",subTitle:this.state.error?.message||"An unexpected error occurred",extra:$.jsxDEV(p,{type:"primary",onClick:this.handleReload,children:"Reload Page"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/ErrorBoundary.tsx",lineNumber:40,columnNumber:15},this)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/ErrorBoundary.tsx",lineNumber:35,columnNumber:11},this)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/ErrorBoundary.tsx",lineNumber:34,columnNumber:9},this):this.props.children}}const Se={BASE_URL:"/",DEV:!0,MODE:"production",PROD:!1,SSR:!1,VITE_API_BASE_URL:"http://localhost:8000",VITE_APP_TITLE:"量化交易系统",VITE_APP_VERSION:"3.0.0",VITE_USER_NODE_ENV:"development"},ke=new Map,Oe=e=>{const r=ke.get(e);return r?Object.fromEntries(Object.entries(r.stores).map(([e,r])=>[e,r.getState()])):{}},De=(e,r={})=>(t,o,n)=>{const{enabled:s,anonymousActionType:a,store:i,...c}=r;let l;try{l=(null!=s?s:"production"!==(Se?"production":void 0))&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(y){}if(!l)return e(t,o,n);const{connection:u,...d}=((e,r,t)=>{if(void 0===e)return{type:"untracked",connection:r.connect(t)};const o=ke.get(t.name);if(o)return{type:"tracked",store:e,...o};const n={connection:r.connect(t),stores:{}};return ke.set(t.name,n),{type:"tracked",store:e,...n}})(i,l,c);let p=!0;n.setState=(e,r,s)=>{const l=t(e,r);if(!p)return l;const d=void 0===s?{type:a||"anonymous"}:"string"==typeof s?{type:s}:s;return void 0===i?(null==u||u.send(d,o()),l):(null==u||u.send({...d,type:`${i}/${d.type}`},{...Oe(c.name),[i]:n.getState()}),l)};const f=(...e)=>{const r=p;p=!1,t(...e),p=r},m=e(n.setState,o,n);if("untracked"===d.type?null==u||u.init(m):(d.stores[d.store]=n,null==u||u.init(Object.fromEntries(Object.entries(d.stores).map(([e,r])=>[e,e===d.store?m:r.getState()])))),n.dispatchFromDevtools&&"function"==typeof n.dispatch){let e=!1;const r=n.dispatch;n.dispatch=(...t)=>{"production"===(Se?"production":void 0)||"__setState"!==t[0].type||e||(e=!0),r(...t)}}return u.subscribe(e=>{var r;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return;return Re(e.payload,e=>{if("__setState"===e.type){if(void 0===i)return void f(e.state);Object.keys(e.state).length;const r=e.state[i];if(null==r)return;return void(JSON.stringify(n.getState())!==JSON.stringify(r)&&f(r))}n.dispatchFromDevtools&&"function"==typeof n.dispatch&&n.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":return f(m),void 0===i?null==u?void 0:u.init(n.getState()):null==u?void 0:u.init(Oe(c.name));case"COMMIT":return void 0===i?void(null==u||u.init(n.getState())):null==u?void 0:u.init(Oe(c.name));case"ROLLBACK":return Re(e.state,e=>{if(void 0===i)return f(e),void(null==u||u.init(n.getState()));f(e[i]),null==u||u.init(Oe(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Re(e.state,e=>{void 0!==i?JSON.stringify(n.getState())!==JSON.stringify(e[i])&&f(e[i]):f(e)});case"IMPORT_STATE":{const{nextLiftedState:t}=e.payload,o=null==(r=t.computedStates.slice(-1)[0])?void 0:r.state;if(!o)return;return f(void 0===i?o:o[i]),void(null==u||u.send(null,t))}case"PAUSE_RECORDING":return p=!p}return}}),m},Re=(e,r)=>{let t;try{t=JSON.parse(e)}catch(o){}void 0!==t&&r(t)},we=R()(De(e=>({loading:{strategies:!1,backtest:!1,data:!1},strategies:[],backtestResults:[],error:null,setLoading:(r,t)=>e(e=>({loading:{...e.loading,[r]:t}})),setStrategies:r=>e({strategies:r}),addStrategy:r=>e(e=>({strategies:[...e.strategies,r]})),removeStrategy:r=>e(e=>({strategies:e.strategies.filter(e=>e.id!==r)})),setBacktestResults:r=>e({backtestResults:r}),addBacktestResult:r=>e(e=>({backtestResults:[r,...e.backtestResults]})),setError:r=>e({error:r}),clearError:()=>e({error:null})}),{name:"trading-app-store"})),{Text:Te}=f,Ae=({shortcuts:e})=>{const[r,t]=o.useState(!1),n=e=>{const r=[];return e.ctrlKey&&r.push("Ctrl"),e.altKey&&r.push("Alt"),e.shiftKey&&r.push("Shift"),r.push(e.key),r.join(" + ")};return $.jsxDEV($.Fragment,{children:[$.jsxDEV(p,{type:"text",icon:$.jsxDEV(m,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:35,columnNumber:15},void 0),onClick:()=>t(!0),title:"Keyboard Shortcuts",size:"small"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:33,columnNumber:7},void 0),$.jsxDEV(y,{title:"Keyboard Shortcuts",open:r,onCancel:()=>t(!1),footer:null,width:500,children:$.jsxDEV("div",{children:[$.jsxDEV(Te,{type:"secondary",style:{marginBottom:"16px",display:"block"},children:"Use these keyboard shortcuts to navigate faster:"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:49,columnNumber:11},void 0),$.jsxDEV(h,{direction:"vertical",style:{width:"100%"},size:"middle",children:e.map((r,t)=>$.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 0",borderBottom:t<e.length-1?"1px solid #f0f0f0":"none"},children:[$.jsxDEV(Te,{children:r.description},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:65,columnNumber:17},void 0),$.jsxDEV(v,{style:{fontFamily:"monospace"},children:n(r)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:66,columnNumber:17},void 0)]},t,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:55,columnNumber:15},void 0))},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:53,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:48,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:41,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/KeyboardShortcutsHelp.tsx",lineNumber:32,columnNumber:5},void 0)},Ue=e=>(o.useEffect(()=>{const r=r=>{const t=e.find(e=>r.key.toLowerCase()===e.key.toLowerCase()&&!!r.ctrlKey==!!e.ctrlKey&&!!r.altKey==!!e.altKey&&!!r.shiftKey==!!e.shiftKey);t&&(r.preventDefault(),t.callback())};return document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[e]),e),Ce={CREATE_STRATEGY:{key:"n",ctrlKey:!0,description:"Create new strategy (Ctrl+N)"},RUN_BACKTEST:{key:"r",ctrlKey:!0,description:"Run backtest (Ctrl+R)"},REFRESH:{key:"F5",description:"Refresh data (F5)"},EXPORT:{key:"e",ctrlKey:!0,description:"Export data (Ctrl+E)"},SETTINGS:{key:",",ctrlKey:!0,description:"Open settings (Ctrl+,)"}},{Header:Ve,Sider:$e,Content:Le}=b,{Title:Ie}=f,Fe=({children:e})=>{const[r,t]=o.useState(!1),a=n(),i=s(),{loading:c,error:l}=we(),u=[{key:"/",icon:$.jsxDEV(N,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:32,columnNumber:13},void 0),label:"Dashboard"},{key:"/strategies",icon:$.jsxDEV(E,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:37,columnNumber:13},void 0),label:"Strategies"},{key:"/backtest",icon:$.jsxDEV(_,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:42,columnNumber:13},void 0),label:"Backtest"},{key:"/settings",icon:$.jsxDEV(S,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:47,columnNumber:13},void 0),label:"Settings"}],d=Object.values(c).some(Boolean);return $.jsxDEV(b,{style:{minHeight:"100vh"},children:[$.jsxDEV($e,{trigger:null,collapsible:!0,collapsed:r,style:{background:"#fff",boxShadow:"2px 0 8px 0 rgba(29,35,41,.05)"},children:[$.jsxDEV("div",{style:{padding:"16px",textAlign:"center",borderBottom:"1px solid #f0f0f0"},children:$.jsxDEV(Ie,{level:4,style:{margin:0,color:"#1890ff"},children:r?"TS":"Trading System"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:74,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:69,columnNumber:9},void 0),$.jsxDEV(P,{mode:"inline",selectedKeys:[i.pathname],items:u,onClick:({key:e})=>{a(e)},style:{border:"none"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:79,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:60,columnNumber:7},void 0),$.jsxDEV(b,{children:[$.jsxDEV(Ve,{style:{padding:"0 16px",background:"#fff",boxShadow:"0 2px 8px 0 rgba(29,35,41,.05)",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[$.jsxDEV(p,{type:"text",icon:r?$.jsxDEV(j,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:99,columnNumber:31},void 0):$.jsxDEV(g,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:99,columnNumber:56},void 0),onClick:()=>t(!r),style:{fontSize:"16px"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:97,columnNumber:11},void 0),$.jsxDEV(h,{children:[$.jsxDEV(Ae,{shortcuts:Object.values(Ce)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:105,columnNumber:13},void 0),l&&$.jsxDEV(x,{status:"error",text:"API Error"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:107,columnNumber:15},void 0),d&&$.jsxDEV(x,{status:"processing",text:"Loading..."},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:110,columnNumber:15},void 0),$.jsxDEV(x,{status:"success",text:"Connected"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:112,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:104,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:89,columnNumber:9},void 0),$.jsxDEV(Le,{style:{margin:"16px",padding:"24px",background:"#fff",borderRadius:"8px",minHeight:"calc(100vh - 112px)"},children:e},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:116,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:88,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppLayout.tsx",lineNumber:59,columnNumber:5},void 0)},Me={},Ke=function(e,r,t){let o=Promise.resolve();if(r&&r.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),n=t?.nonce||t?.getAttribute("nonce");o=e(r.map(e=>{if((e=function(e){return"/"+e}(e))in Me)return;Me[e]=!0;const r=e.endsWith(".css"),t=r?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${t}`))return;const o=document.createElement("link");return o.rel=r?"stylesheet":"modulepreload",r||(o.as="script"),o.crossOrigin="",o.href=e,n&&o.setAttribute("nonce",n),document.head.appendChild(o),r?new Promise((r,t)=>{o.addEventListener("load",r),o.addEventListener("error",()=>t(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function n(e){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=e,window.dispatchEvent(r),!r.defaultPrevented)throw e}return o.then(r=>{for(const e of r||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})},Be=a.lazy(()=>Ke(()=>import("./Dashboard-CykE3o7k.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),Ye=a.lazy(()=>Ke(()=>import("./Strategies-BcSnHVxN.js"),__vite__mapDeps([7,1,2,3,4,5,8,6]))),ze=a.lazy(()=>Ke(()=>import("./Backtest-B_y4aKob.js"),__vite__mapDeps([9,1,4,2,3,5,8,6]))),He=a.lazy(()=>Ke(()=>import("./Settings-B2j9IhFO.js"),__vite__mapDeps([10,1,2,3,4,6]))),We=()=>$.jsxDEV("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:$.jsxDEV(k,{size:"large"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:18,columnNumber:5},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:12,columnNumber:3},void 0),Je=()=>$.jsxDEV(o.Suspense,{fallback:$.jsxDEV(We,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:24,columnNumber:25},void 0),children:$.jsxDEV(i,{children:[$.jsxDEV(c,{path:"/",element:$.jsxDEV(Be,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:26,columnNumber:34},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:26,columnNumber:9},void 0),$.jsxDEV(c,{path:"/strategies",element:$.jsxDEV(Ye,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:27,columnNumber:44},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:27,columnNumber:9},void 0),$.jsxDEV(c,{path:"/backtest",element:$.jsxDEV(ze,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:28,columnNumber:42},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:28,columnNumber:9},void 0),$.jsxDEV(c,{path:"/settings",element:$.jsxDEV(He,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:29,columnNumber:42},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:29,columnNumber:9},void 0),$.jsxDEV(c,{path:"*",element:$.jsxDEV(l,{to:"/",replace:!0},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:30,columnNumber:34},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:30,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:25,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/components/AppRouter.tsx",lineNumber:24,columnNumber:5},void 0),qe=()=>$.jsxDEV(O,{locale:Ee,theme:{token:{colorPrimary:"#1890ff",borderRadius:6}},children:$.jsxDEV(D,{children:$.jsxDEV(u,{children:$.jsxDEV(_e,{children:$.jsxDEV(Fe,{children:$.jsxDEV(Je,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:25,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:24,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:23,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:22,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:21,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/App.tsx",lineNumber:12,columnNumber:5},void 0);F.createRoot(document.getElementById("root")).render($.jsxDEV(o.StrictMode,{children:$.jsxDEV(qe,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/main.tsx",lineNumber:8,columnNumber:5},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/main.tsx",lineNumber:7,columnNumber:3},void 0));export{Ce as C,Ue as a,$ as j,we as u};
//# sourceMappingURL=index-D0Fokv29.js.map
