{"version": 3, "file": "Backtest-B_y4aKob.js", "sources": ["../../src/pages/Backtest.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { \n  Card, \n  Table, \n  Button, \n  Space, \n  Modal, \n  Form, \n  Select, \n  DatePicker, \n  InputNumber,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Tag,\n} from 'antd';\nimport { PlayCircleOutlined, HistoryOutlined, DownloadOutlined } from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\nimport { useAppStore } from '@/store';\nimport { useApi } from '@/hooks/useApi';\nimport { useKeyboardShortcuts, COMMON_SHORTCUTS } from '@/hooks/useKeyboardShortcuts';\nimport { \n  formatDate, \n  formatPercentage, \n  formatCurrency, \n  formatNumber,\n  getPerformanceColor,\n} from '@/utils/format';\nimport { validateBacktestRequest } from '@/utils/validation';\nimport { exportBacktestResultsToCSV } from '@/utils/export';\nimport { DEFAULT_CONFIG } from '@/config/api';\nimport type { BacktestResult, BacktestRequest } from '@/types';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\nconst Backtest: React.FC = () => {\n  const { strategies, backtestResults, loading } = useAppStore();\n  const { loadStrategies, loadBacktestHistory, runBacktest } = useApi();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    loadStrategies();\n    loadBacktestHistory();\n  }, [loadStrategies, loadBacktestHistory]);\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts([\n    {\n      ...COMMON_SHORTCUTS.RUN_BACKTEST,\n      callback: () => strategies.length > 0 && setIsModalVisible(true),\n    },\n    {\n      ...COMMON_SHORTCUTS.REFRESH,\n      callback: () => loadBacktestHistory(),\n    },\n    {\n      ...COMMON_SHORTCUTS.EXPORT,\n      callback: () => backtestResults.length > 0 && exportBacktestResultsToCSV(backtestResults),\n    },\n  ]);\n\n  const handleRunBacktest = async (values: any) => {\n    const [startDate, endDate] = values.dateRange;\n    \n    const validationError = validateBacktestRequest(\n      values.strategy_id,\n      values.symbol,\n      startDate.format('YYYY-MM-DD'),\n      endDate.format('YYYY-MM-DD'),\n      values.initial_capital\n    );\n\n    if (validationError) {\n      return;\n    }\n\n    const request: BacktestRequest = {\n      strategy_id: values.strategy_id,\n      symbol: values.symbol.toUpperCase(),\n      start_date: startDate.format('YYYY-MM-DD'),\n      end_date: endDate.format('YYYY-MM-DD'),\n      initial_capital: values.initial_capital,\n    };\n\n    const result = await runBacktest(request);\n    if (result) {\n      setIsModalVisible(false);\n      form.resetFields();\n    }\n  };\n\n  const getPerformanceTag = (totalReturn: number) => {\n    if (totalReturn > 0.1) return <Tag color=\"green\">Excellent</Tag>;\n    if (totalReturn > 0.05) return <Tag color=\"blue\">Good</Tag>;\n    if (totalReturn > 0) return <Tag color=\"orange\">Positive</Tag>;\n    return <Tag color=\"red\">Negative</Tag>;\n  };\n\n  const columns: ColumnsType<BacktestResult> = [\n    {\n      title: 'Symbol',\n      dataIndex: 'symbol',\n      key: 'symbol',\n      sorter: (a, b) => a.symbol.localeCompare(b.symbol),\n    },\n    {\n      title: 'Strategy',\n      dataIndex: 'strategy_id',\n      key: 'strategy_id',\n      render: (strategyId: string) => {\n        const strategy = strategies.find(s => s.id === strategyId);\n        return strategy ? strategy.name : 'Unknown';\n      },\n    },\n    {\n      title: 'Period',\n      key: 'period',\n      render: (_, record) => (\n        <div>\n          <div>{formatDate(record.start_date)}</div>\n          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>\n            to {formatDate(record.end_date)}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: 'Initial Capital',\n      dataIndex: 'initial_capital',\n      key: 'initial_capital',\n      render: (value: number) => formatCurrency(value),\n      sorter: (a, b) => a.initial_capital - b.initial_capital,\n    },\n    {\n      title: 'Final Capital',\n      dataIndex: 'final_capital',\n      key: 'final_capital',\n      render: (value: number) => formatCurrency(value),\n      sorter: (a, b) => a.final_capital - b.final_capital,\n    },\n    {\n      title: 'Total Return',\n      dataIndex: 'total_return',\n      key: 'total_return',\n      render: (value: number) => (\n        <span style={{ color: getPerformanceColor(value), fontWeight: 500 }}>\n          {formatPercentage(value)}\n        </span>\n      ),\n      sorter: (a, b) => a.total_return - b.total_return,\n    },\n    {\n      title: 'Sharpe Ratio',\n      dataIndex: 'sharpe_ratio',\n      key: 'sharpe_ratio',\n      render: (value: number) => formatNumber(value, 3),\n      sorter: (a, b) => a.sharpe_ratio - b.sharpe_ratio,\n    },\n    {\n      title: 'Max Drawdown',\n      dataIndex: 'max_drawdown',\n      key: 'max_drawdown',\n      render: (value: number) => (\n        <span style={{ color: '#ff4d4f' }}>\n          {formatPercentage(Math.abs(value))}\n        </span>\n      ),\n      sorter: (a, b) => a.max_drawdown - b.max_drawdown,\n    },\n    {\n      title: 'Trades',\n      dataIndex: 'trade_count',\n      key: 'trade_count',\n      sorter: (a, b) => a.trade_count - b.trade_count,\n    },\n    {\n      title: 'Performance',\n      dataIndex: 'total_return',\n      key: 'performance',\n      render: (value: number) => getPerformanceTag(value),\n    },\n    {\n      title: 'Created',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (date: string) => formatDate(date),\n      sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),\n    },\n  ];\n\n  // Calculate summary statistics\n  const totalBacktests = backtestResults.length;\n  const avgReturn = totalBacktests > 0 \n    ? backtestResults.reduce((sum, result) => sum + result.total_return, 0) / totalBacktests\n    : 0;\n  const bestReturn = totalBacktests > 0\n    ? Math.max(...backtestResults.map(result => result.total_return))\n    : 0;\n  const worstReturn = totalBacktests > 0\n    ? Math.min(...backtestResults.map(result => result.total_return))\n    : 0;\n\n  return (\n    <div>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '24px',\n      }}>\n        <div>\n          <Title level={2}>Backtest Results</Title>\n          <p style={{ color: '#8c8c8c', margin: 0 }}>\n            Run backtests and analyze strategy performance\n          </p>\n        </div>\n        <Space>\n          <Button \n            icon={<HistoryOutlined />}\n            onClick={() => loadBacktestHistory()}\n            loading={loading.backtest}\n          >\n            Refresh\n          </Button>\n          {backtestResults.length > 0 && (\n            <Button \n              icon={<DownloadOutlined />}\n              onClick={() => exportBacktestResultsToCSV(backtestResults)}\n            >\n              Export CSV\n            </Button>\n          )}\n          <Button \n            type=\"primary\" \n            icon={<PlayCircleOutlined />}\n            onClick={() => setIsModalVisible(true)}\n            disabled={strategies.length === 0}\n          >\n            Run Backtest\n          </Button>\n        </Space>\n      </div>\n\n      {/* Summary Statistics */}\n      {totalBacktests > 0 && (\n        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n          <Col xs={24} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Total Backtests\"\n                value={totalBacktests}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Average Return\"\n                value={formatPercentage(avgReturn)}\n                valueStyle={{ color: getPerformanceColor(avgReturn) }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Best Return\"\n                value={formatPercentage(bestReturn)}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={6}>\n            <Card>\n              <Statistic\n                title=\"Worst Return\"\n                value={formatPercentage(worstReturn)}\n                valueStyle={{ color: '#ff4d4f' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={backtestResults}\n          rowKey=\"id\"\n          loading={loading.backtest}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} backtests`,\n          }}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n\n      <Modal\n        title=\"Run New Backtest\"\n        open={isModalVisible}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleRunBacktest}\n          initialValues={{\n            initial_capital: DEFAULT_CONFIG.INITIAL_CAPITAL,\n          }}\n        >\n          <Form.Item\n            name=\"strategy_id\"\n            label=\"Strategy\"\n            rules={[{ required: true, message: 'Please select a strategy!' }]}\n          >\n            <Select placeholder=\"Select strategy\">\n              {strategies.map(strategy => (\n                <Option key={strategy.id} value={strategy.id}>\n                  {strategy.name} ({strategy.type})\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"symbol\"\n            label=\"Symbol\"\n            rules={[{ required: true, message: 'Please input symbol!' }]}\n          >\n            <Select\n              placeholder=\"Select or enter symbol\"\n              showSearch\n              allowClear\n              options={[\n                { value: 'AAPL', label: 'AAPL - Apple Inc.' },\n                { value: 'GOOGL', label: 'GOOGL - Alphabet Inc.' },\n                { value: 'MSFT', label: 'MSFT - Microsoft Corp.' },\n                { value: 'TSLA', label: 'TSLA - Tesla Inc.' },\n                { value: 'AMZN', label: 'AMZN - Amazon.com Inc.' },\n              ]}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"dateRange\"\n            label=\"Date Range\"\n            rules={[{ required: true, message: 'Please select date range!' }]}\n          >\n            <RangePicker\n              style={{ width: '100%' }}\n              disabledDate={(current) => current && current > dayjs().endOf('day')}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"initial_capital\"\n            label=\"Initial Capital\"\n            rules={[{ required: true, message: 'Please input initial capital!' }]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              min={1000}\n              max={10000000}\n              formatter={(value) => `$ ${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n              parser={(value) => {\n                const num = Number(value!.replace(/\\$\\s?|(,*)/g, ''));\n                return Math.max(1000, Math.min(10000000, num)) as any;\n              }}\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button onClick={() => {\n                setIsModalVisible(false);\n                form.resetFields();\n              }}>\n                Cancel\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading.backtest}>\n                Run Backtest\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Backtest;"], "names": ["Title", "Typography", "Option", "Select", "RangePicker", "DatePicker", "Backtest", "strategies", "backtestResults", "loading", "useAppStore", "loadStrategies", "loadBacktestHistory", "runBacktest", "useApi", "isModalVisible", "setIsModalVisible", "useState", "form", "Form", "useForm", "useEffect", "useKeyboardShortcuts", "COMMON_SHORTCUTS", "RUN_BACKTEST", "callback", "length", "REFRESH", "EXPORT", "exportBacktestResultsToCSV", "columns", "title", "dataIndex", "key", "sorter", "a", "b", "symbol", "localeCompare", "render", "strategyId", "strategy", "find", "s", "id", "name", "_", "record", "children", "jsxDEV", "formatDate", "start_date", "fileName", "lineNumber", "columnNumber", "this", "style", "fontSize", "color", "end_date", "value", "formatCurrency", "initial_capital", "final_capital", "getPerformanceColor", "fontWeight", "formatPercentage", "total_return", "formatNumber", "sharpe_ratio", "Math", "abs", "max_drawdown", "trade_count", "getPerformanceTag", "totalReturn", "Tag", "date", "Date", "created_at", "getTime", "totalBacktests", "avgReturn", "reduce", "sum", "result", "bestReturn", "max", "map", "worstReturn", "min", "display", "justifyContent", "alignItems", "marginBottom", "level", "margin", "Space", "<PERSON><PERSON>", "icon", "HistoryOutlined", "onClick", "backtest", "DownloadOutlined", "type", "PlayCircleOutlined", "disabled", "Row", "gutter", "Col", "xs", "sm", "Card", "Statistic", "valueStyle", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "Modal", "open", "onCancel", "resetFields", "footer", "width", "layout", "onFinish", "async", "values", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "validateBacktestRequest", "strategy_id", "format", "request", "toUpperCase", "initialValues", "DEFAULT_CONFIG", "INITIAL_CAPITAL", "<PERSON><PERSON>", "label", "rules", "required", "message", "placeholder", "showSearch", "allowClear", "options", "disabledDate", "current", "dayjs", "endOf", "InputNumber", "formatter", "replace", "parser", "num", "Number", "textAlign", "htmlType"], "mappings": "0fAmCA,MAAMA,MAAEA,GAAUC,GACZC,OAAEA,GAAWC,GACbC,YAAEA,GAAgBC,EAElBC,EAAqB,KACzB,MAAMC,WAAEA,EAAAC,gBAAYA,EAAAC,QAAiBA,GAAYC,KAC3CC,eAAEA,EAAAC,oBAAgBA,EAAAC,YAAqBA,GAAgBC,KACtDC,EAAgBC,GAAqBC,EAAAA,UAAS,IAC9CC,GAAQC,EAAKC,UAEpBC,EAAAA,UAAU,KACRV,IACAC,KACC,CAACD,EAAgBC,IAGpBU,EAAqB,CACnB,IACKC,EAAiBC,aACpBC,SAAU,IAAMlB,EAAWmB,OAAS,GAAKV,GAAkB,IAE7D,IACKO,EAAiBI,QACpBF,SAAU,IAAMb,KAElB,IACKW,EAAiBK,OACpBH,SAAU,IAAMjB,EAAgBkB,OAAS,GAAKG,EAA2BrB,MAI7E,MAqCMsB,EAAuC,CAC3C,CACEC,MAAO,SACPC,UAAW,SACXC,IAAK,SACLC,OAAQ,CAACC,EAAGC,IAAMD,EAAEE,OAAOC,cAAcF,EAAEC,SAE7C,CACEN,MAAO,WACPC,UAAW,cACXC,IAAK,cACLM,OAASC,IACP,MAAMC,EAAWlC,EAAWmC,KAAKC,GAAKA,EAAEC,KAAOJ,GAC/C,OAAOC,EAAWA,EAASI,KAAO,YAGtC,CACEd,MAAO,SACPE,IAAK,SACLM,OAAQ,CAACO,EAAGC,aACT,MAAA,CACCC,SAAA,CAAAC,SAAC,MAAA,CAAKD,SAAAE,EAAWH,EAAOI,kBAAxB,GAAA,EAAA,CAAAC,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAN,SAAC,OAAIO,MAAO,CAAEC,SAAU,OAAQC,MAAO,WAAaV,SAAA,CAAA,MAC9CE,EAAWH,EAAOY,iBADxB,GAAA,EAAA,CAAAP,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,IAQJ,CACExB,MAAO,kBACPC,UAAW,kBACXC,IAAK,kBACLM,OAASqB,GAAkBC,EAAeD,GAC1C1B,OAAQ,CAACC,EAAGC,IAAMD,EAAE2B,gBAAkB1B,EAAE0B,iBAE1C,CACE/B,MAAO,gBACPC,UAAW,gBACXC,IAAK,gBACLM,OAASqB,GAAkBC,EAAeD,GAC1C1B,OAAQ,CAACC,EAAGC,IAAMD,EAAE4B,cAAgB3B,EAAE2B,eAExC,CACEhC,MAAO,eACPC,UAAW,eACXC,IAAK,eACLM,OAASqB,GACPX,SAAC,OAAA,CAAKO,MAAO,CAAEE,MAAOM,EAAoBJ,GAAQK,WAAY,KAC3DjB,SAAAkB,EAAiBN,SADpB,GAAA,EAAA,CAAAR,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAIFrB,OAAQ,CAACC,EAAGC,IAAMD,EAAEgC,aAAe/B,EAAE+B,cAEvC,CACEpC,MAAO,eACPC,UAAW,eACXC,IAAK,eACLM,OAASqB,GAAkBQ,EAAaR,EAAO,GAC/C1B,OAAQ,CAACC,EAAGC,IAAMD,EAAEkC,aAAejC,EAAEiC,cAEvC,CACEtC,MAAO,eACPC,UAAW,eACXC,IAAK,eACLM,OAASqB,GACPX,EAAAA,OAAC,QAAKO,MAAO,CAAEE,MAAO,WACnBV,SAAAkB,EAAiBI,KAAKC,IAAIX,UAD7B,GAAA,EAAA,CAAAR,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAIFrB,OAAQ,CAACC,EAAGC,IAAMD,EAAEqC,aAAepC,EAAEoC,cAEvC,CACEzC,MAAO,SACPC,UAAW,cACXC,IAAK,cACLC,OAAQ,CAACC,EAAGC,IAAMD,EAAEsC,YAAcrC,EAAEqC,aAEtC,CACE1C,MAAO,cACPC,UAAW,eACXC,IAAK,cACLM,OAASqB,IAAkBc,OAxFJC,EAwFsBf,GAvF7B,YAAagB,EAAA,CAAIlB,MAAM,QAAQV,SAAA,kBAAnB,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,GAAAC,aAAA,SAAAC,GAC1BoB,EAAc,aAAcC,EAAA,CAAIlB,MAAM,OAAOV,SAAA,aAAlB,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,GAAAC,aAAA,SAAAC,GAC3BoB,EAAc,WAAWC,EAAA,CAAIlB,MAAM,SAASV,SAAA,iBAApB,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACrBN,EAAAA,OAAC2B,EAAA,CAAIlB,MAAM,MAAMV,SAAA,iBAAjB,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAJiB,IAACoB,IA0FzB,CACE5C,MAAO,UACPC,UAAW,aACXC,IAAK,aACLM,OAASsC,GAAiB3B,EAAW2B,GACrC3C,OAAQ,CAACC,EAAGC,IAAM,IAAI0C,KAAK3C,EAAE4C,YAAYC,UAAY,IAAIF,KAAK1C,EAAE2C,YAAYC,YAK1EC,EAAiBzE,EAAgBkB,OACjCwD,EAAYD,EAAiB,EAC/BzE,EAAgB2E,OAAO,CAACC,EAAKC,IAAWD,EAAMC,EAAOlB,aAAc,GAAKc,EACxE,EACEK,EAAaL,EAAiB,EAChCX,KAAKiB,OAAO/E,EAAgBgF,IAAIH,GAAUA,EAAOlB,eACjD,EACEsB,EAAcR,EAAiB,EACjCX,KAAKoB,OAAOlF,EAAgBgF,IAAIH,GAAUA,EAAOlB,eACjD,EAEJ,gBACG,MAAA,CACCnB,SAAA,CAAAC,SAAC,OAAIO,MAAO,CACVmC,QAAS,OACTC,eAAgB,gBAChBC,WAAY,SACZC,aAAc,QAEd9C,SAAA,CAAAC,SAAC,MAAA,CACCD,SAAA,CAAAC,SAACjD,EAAA,CAAM+F,MAAO,EAAG/C,SAAA,yBAAjB,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACAN,EAAAA,OAAC,KAAEO,MAAO,CAAEE,MAAO,UAAWsC,OAAQ,GAAKhD,SAAA,uDAA3C,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,YAMC0C,EAAA,CACCjD,SAAA,CAAAC,EAAAA,OAACiD,EAAA,CACCC,cAAOC,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAhD,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN8C,QAAS,IAAMzF,IACfH,QAASA,EAAQ6F,SAClBtD,SAAA,gBAJD,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOC/C,EAAgBkB,OAAS,GACxBuB,EAAAA,OAACiD,EAAA,CACCC,cAAOI,EAAA,CAAA,OAAD,GAAA,EAAA,CAAAnD,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN8C,QAAS,IAAMxE,EAA2BrB,GAC3CwC,SAAA,mBAHD,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAOFN,EAAAA,OAACiD,EAAA,CACCM,KAAK,UACLL,cAAOM,EAAA,CAAA,OAAD,GAAA,EAAA,CAAArD,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GACN8C,QAAS,IAAMrF,GAAkB,GACjC0F,SAAgC,IAAtBnG,EAAWmB,OACtBsB,SAAA,qBALD,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAhBF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,UAZF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAwCC0B,EAAiB,GAChBhC,EAAAA,OAAC0D,EAAA,CAAIC,OAAQ,CAAC,GAAI,IAAKpD,MAAO,CAAEsC,aAAc,QAC5C9C,SAAA,CAAAC,EAAAA,OAAC4D,GAAIC,GAAI,GAAIC,GAAI,EACf/D,kBAACgE,EAAA,CACChE,SAAAC,EAAAA,OAACgE,EAAA,CACClF,MAAM,kBACN6B,MAAOqB,EACPiC,WAAY,CAAExD,MAAO,iBAHvB,GAAA,EAAA,CAAAN,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YASCsD,EAAA,CAAIC,GAAI,GAAIC,GAAI,EACf/D,kBAACgE,EAAA,CACChE,SAAAC,EAAAA,OAACgE,EAAA,CACClF,MAAM,iBACN6B,MAAOM,EAAiBgB,GACxBgC,WAAY,CAAExD,MAAOM,EAAoBkB,UAH3C,GAAA,EAAA,CAAA9B,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YASCsD,EAAA,CAAIC,GAAI,GAAIC,GAAI,EACf/D,kBAACgE,EAAA,CACChE,SAAAC,EAAAA,OAACgE,EAAA,CACClF,MAAM,cACN6B,MAAOM,EAAiBoB,GACxB4B,WAAY,CAAExD,MAAO,iBAHvB,GAAA,EAAA,CAAAN,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,YASCsD,EAAA,CAAIC,GAAI,GAAIC,GAAI,EACf/D,kBAACgE,EAAA,CACChE,SAAAC,EAAAA,OAACgE,EAAA,CACClF,MAAM,eACN6B,MAAOM,EAAiBuB,GACxByB,WAAY,CAAExD,MAAO,iBAHvB,GAAA,EAAA,CAAAN,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UA5BF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,YAwCDyD,EAAA,CACChE,SAAAC,EAAAA,OAACkE,EAAA,CACCrF,UACAsF,WAAY5G,EACZ6G,OAAO,KACP5G,QAASA,EAAQ6F,SACjBgB,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAYC,GAAU,SAASA,eAEjCC,OAAQ,CAAEC,EAAG,YAXf,GAAA,EAAA,CAAAzE,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,GAgBAN,EAAAA,OAAC6E,EAAA,CACC/F,MAAM,mBACNgG,KAAMhH,EACNiH,SAAU,KACRhH,GAAkB,GAClBE,EAAK+G,eAEPC,OAAQ,KACRC,MAAO,IAEPnF,SAAAC,EAAAA,OAAC9B,EAAA,CACCD,OACAkH,OAAO,WACPC,SA7PkBC,MAAOC,IAC/B,MAAOC,EAAWC,GAAWF,EAAOG,UAUpC,GARwBC,EACtBJ,EAAOK,YACPL,EAAOlG,OACPmG,EAAUK,OAAO,cACjBJ,EAAQI,OAAO,cACfN,EAAOzE,iBAIP,OAGF,MAAMgF,EAA2B,CAC/BF,YAAaL,EAAOK,YACpBvG,OAAQkG,EAAOlG,OAAO0G,cACtB5F,WAAYqF,EAAUK,OAAO,cAC7BlF,SAAU8E,EAAQI,OAAO,cACzB/E,gBAAiByE,EAAOzE,uBAGLjD,EAAYiI,KAE/B9H,GAAkB,GAClBE,EAAK+G,gBAoODe,cAAe,CACblF,gBAAiBmF,EAAeC,iBAGlClG,SAAA,CAAAC,EAAAA,OAAC9B,EAAKgI,KAAL,CACCtG,KAAK,cACLuG,MAAM,WACNC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS,8BAEnCvG,SAAAC,EAAAA,OAAC9C,EAAA,CAAOqJ,YAAY,kBACjBxG,SAAAzC,EAAWiF,IAAI/C,KACdQ,OAAC/C,EAAA,CAAyB0D,MAAOnB,EAASG,GACvCI,SAAA,CAAAP,EAASI,KAAK,KAAGJ,EAAS+D,KAAK,MADrB/D,EAASG,IAAtB,EAAA,CAAAQ,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAFJ,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAcAN,EAAAA,OAAC9B,EAAKgI,KAAL,CACCtG,KAAK,SACLuG,MAAM,SACNC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS,yBAEnCvG,SAAAC,EAAAA,OAAC9C,EAAA,CACCqJ,YAAY,yBACZC,YAAU,EACVC,YAAU,EACVC,QAAS,CACP,CAAE/F,MAAO,OAAQwF,MAAO,qBACxB,CAAExF,MAAO,QAASwF,MAAO,yBACzB,CAAExF,MAAO,OAAQwF,MAAO,0BACxB,CAAExF,MAAO,OAAQwF,MAAO,qBACxB,CAAExF,MAAO,OAAQwF,MAAO,iCAT5B,GAAA,EAAA,CAAAhG,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAmBAN,EAAAA,OAAC9B,EAAKgI,KAAL,CACCtG,KAAK,YACLuG,MAAM,aACNC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS,8BAEnCvG,SAAAC,EAAAA,OAAC7C,EAAA,CACCoD,MAAO,CAAE2E,MAAO,QAChByB,aAAeC,GAAYA,GAAWA,EAAUC,IAAQC,MAAM,aAFhE,GAAA,EAAA,CAAA3G,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAWAN,EAAAA,OAAC9B,EAAKgI,KAAL,CACCtG,KAAK,kBACLuG,MAAM,kBACNC,MAAO,CAAC,CAAEC,UAAU,EAAMC,QAAS,kCAEnCvG,SAAAC,EAAAA,OAAC+G,EAAA,CACCxG,MAAO,CAAE2E,MAAO,QAChBzC,IAAK,IACLH,IAAK,IACL0E,UAAYrG,GAAU,KAAKA,IAAQsG,QAAQ,wBAAyB,KACpEC,OAASvG,IACP,MAAMwG,EAAMC,OAAOzG,EAAOsG,QAAQ,cAAe,KACjD,OAAO5F,KAAKiB,IAAI,IAAMjB,KAAKoB,IAAI,IAAU0E,WAP7C,GAAA,EAAA,CAAAhH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SALF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,KAiBAN,OAAC9B,EAAKgI,KAAL,CAAU3F,MAAO,CAAEsC,aAAc,EAAGwE,UAAW,SAC9CtH,WAAAC,OAACgD,EAAA,CACCjD,SAAA,CAAAC,SAACiD,EAAA,CAAOG,QAAS,KACfrF,GAAkB,GAClBE,EAAK+G,eACJjF,SAAA,eAHH,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,GAMAN,EAAAA,OAACiD,GAAOM,KAAK,UAAU+D,SAAS,SAAS9J,QAASA,EAAQ6F,SAAUtD,SAAA,qBAApE,GAAA,EAAA,CAAAI,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UAPF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,SADF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,SAAAC,UArEF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,SAVF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC,UAlGF,GAAA,EAAA,CAAAH,SAAA,mFAAAC,WAAA,IAAAC,aAAA,QAAAC"}