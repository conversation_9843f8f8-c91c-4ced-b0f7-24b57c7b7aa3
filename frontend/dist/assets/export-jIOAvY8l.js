import{G as e}from"./ui-vendor-BjEg0IST.js";const t=e=>e&&0!==e.trim().length?e.length>50?"Strategy name must be less than 50 characters":null:"Strategy name is required",r=(e,t)=>{switch(e){case"moving_average":if(!t.short_window||t.short_window<1)return"Short window must be a positive number";if(!t.long_window||t.long_window<1)return"Long window must be a positive number";if(t.short_window>=t.long_window)return"Short window must be less than long window";break;case"rsi":if(!t.period||t.period<1||t.period>100)return"RSI period must be between 1 and 100";if(!t.overbought||t.overbought<50||t.overbought>100)return"Overbought level must be between 50 and 100";if(!t.oversold||t.oversold<0||t.oversold>50)return"Oversold level must be between 0 and 50"}return null},a=(t,r,a,i,n)=>{if(!t)return"Strategy is required";if(!r||0===r.trim().length)return"Symbol is required";if(!a)return"Start date is required";if(!i)return"End date is required";const o=e(a),d=e(i);return o.isValid()?d.isValid()?o.isAfter(d)?"Start date must be before end date":d.isAfter(e())?"End date cannot be in the future":n<=0?"Initial capital must be positive":n>1e7?"Initial capital cannot exceed $10,000,000":null:"Invalid end date":"Invalid start date"},i=e=>{if(0===e.length)return;const t=[["ID","Strategy ID","Symbol","Start Date","End Date","Initial Capital","Final Capital","Total Return (%)","Sharpe Ratio","Max Drawdown (%)","Trade Count","Created At"].join(","),...e.map(e=>[e.id,e.strategy_id,e.symbol,e.start_date,e.end_date,e.initial_capital,e.final_capital,(100*e.total_return).toFixed(2),e.sharpe_ratio.toFixed(3),(100*Math.abs(e.max_drawdown)).toFixed(2),e.trade_count,e.created_at].join(","))].join("\n");o(t,"backtest-results.csv")},n=e=>{if(0===e.length)return;const t=[["ID","Name","Type","Parameters","Created At"].join(","),...e.map(e=>[e.id,`"${e.name}"`,e.type,`"${JSON.stringify(e.parameters)}"`,e.created_at].join(","))].join("\n");o(t,"strategies.csv")},o=(e,t)=>{const r=new Blob([e],{type:"text/csv;charset=utf-8;"});d(r,t)},d=(e,t)=>{const r=document.createElement("a"),a=URL.createObjectURL(e);r.setAttribute("href",a),r.setAttribute("download",t),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a)};export{r as a,i as b,a as c,n as e,t as v};
//# sourceMappingURL=export-jIOAvY8l.js.map
