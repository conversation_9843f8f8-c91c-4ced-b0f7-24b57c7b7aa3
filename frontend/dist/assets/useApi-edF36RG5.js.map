{"version": 3, "file": "useApi-edF36RG5.js", "sources": ["../../src/config/api.ts", "../../src/services/api.ts", "../../src/hooks/useApi.ts"], "sourcesContent": ["// API configuration\nexport const API_CONFIG = {\n  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',\n  ENDPOINTS: {\n    STRATEGIES: '/api/strategies',\n    BACKTEST: '/api/backtest',\n    DATA: '/api/data',\n    HEALTH: '/api/health',\n  },\n  TIMEOUT: 30000,\n} as const;\n\nexport const DEFAULT_CONFIG = {\n  INITIAL_CAPITAL: 100000,\n  COMMISSION: 0.001,\n  REFRESH_INTERVAL: 5000,\n} as const;", "import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { API_CONFIG } from '@/config/api';\nimport type { \n  Strategy, \n  BacktestResult, \n  MarketData, \n  StrategyRequest, \n  BacktestRequest, \n  ApiResponse,\n  ApiError \n} from '@/types';\n\nclass ApiClient {\n  private client: AxiosInstance;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => response,\n      (error) => {\n        const apiError: ApiError = {\n          message: error.response?.data?.message || error.message || 'Unknown error',\n          status: error.response?.status || 500,\n          details: error.response?.data,\n        };\n        console.error('API Error:', apiError);\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  // Health check\n  async healthCheck(): Promise<boolean> {\n    try {\n      await this.client.get(API_CONFIG.ENDPOINTS.HEALTH);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  // Strategy management\n  async getStrategies(): Promise<Strategy[]> {\n    const response = await this.client.get<ApiResponse<Strategy[]>>(\n      API_CONFIG.ENDPOINTS.STRATEGIES\n    );\n    return response.data.data;\n  }\n\n  async createStrategy(strategy: StrategyRequest): Promise<Strategy> {\n    const response = await this.client.post<ApiResponse<Strategy>>(\n      API_CONFIG.ENDPOINTS.STRATEGIES,\n      strategy\n    );\n    return response.data.data;\n  }\n\n  async deleteStrategy(id: string): Promise<void> {\n    await this.client.delete(`${API_CONFIG.ENDPOINTS.STRATEGIES}/${id}`);\n  }\n\n  // Backtest operations\n  async runBacktest(request: BacktestRequest): Promise<BacktestResult> {\n    const response = await this.client.post<ApiResponse<BacktestResult>>(\n      API_CONFIG.ENDPOINTS.BACKTEST,\n      request\n    );\n    return response.data.data;\n  }\n\n  async getBacktestHistory(): Promise<BacktestResult[]> {\n    const response = await this.client.get<ApiResponse<BacktestResult[]>>(\n      `${API_CONFIG.ENDPOINTS.BACKTEST}/history`\n    );\n    return response.data.data;\n  }\n\n  // Market data\n  async getMarketData(\n    symbol: string, \n    startDate: string, \n    endDate: string\n  ): Promise<MarketData[]> {\n    const response = await this.client.get<ApiResponse<MarketData[]>>(\n      `${API_CONFIG.ENDPOINTS.DATA}/${symbol}`,\n      {\n        params: { start_date: startDate, end_date: endDate }\n      }\n    );\n    return response.data.data;\n  }\n}\n\nexport const apiClient = new ApiClient();", "import { useCallback } from 'react';\nimport { message } from 'antd';\nimport { apiClient } from '@/services/api';\nimport { useAppStore } from '@/store';\nimport type { StrategyRequest, BacktestRequest } from '@/types';\n\nexport const useApi = () => {\n  const {\n    setLoading,\n    setStrategies,\n    addStrategy,\n    removeStrategy,\n    setBacktestResults,\n    addBacktestResult,\n    setError,\n    clearError,\n  } = useAppStore();\n\n  // Generic error handler\n  const handleError = useCallback((error: any, action: string) => {\n    console.error(`Error in ${action}:`, error);\n    setError(error);\n    message.error(error.message || `Failed to ${action}`);\n  }, [setError]);\n\n  // Strategy operations\n  const loadStrategies = useCallback(async () => {\n    try {\n      setLoading('strategies', true);\n      clearError();\n      const strategies = await apiClient.getStrategies();\n      setStrategies(strategies);\n      return strategies;\n    } catch (error) {\n      handleError(error, 'load strategies');\n      return [];\n    } finally {\n      setLoading('strategies', false);\n    }\n  }, [setLoading, setStrategies, clearError, handleError]);\n\n  const createStrategy = useCallback(async (request: StrategyRequest) => {\n    try {\n      setLoading('strategies', true);\n      clearError();\n      const strategy = await apiClient.createStrategy(request);\n      addStrategy(strategy);\n      message.success('Strategy created successfully');\n      return strategy;\n    } catch (error) {\n      handleError(error, 'create strategy');\n      return null;\n    } finally {\n      setLoading('strategies', false);\n    }\n  }, [setLoading, addStrategy, clearError, handleError]);\n\n  const deleteStrategy = useCallback(async (id: string) => {\n    try {\n      setLoading('strategies', true);\n      clearError();\n      await apiClient.deleteStrategy(id);\n      removeStrategy(id);\n      message.success('Strategy deleted successfully');\n      return true;\n    } catch (error) {\n      handleError(error, 'delete strategy');\n      return false;\n    } finally {\n      setLoading('strategies', false);\n    }\n  }, [setLoading, removeStrategy, clearError, handleError]);\n\n  // Backtest operations\n  const runBacktest = useCallback(async (request: BacktestRequest) => {\n    try {\n      setLoading('backtest', true);\n      clearError();\n      const result = await apiClient.runBacktest(request);\n      addBacktestResult(result);\n      message.success('Backtest completed successfully');\n      return result;\n    } catch (error) {\n      handleError(error, 'run backtest');\n      return null;\n    } finally {\n      setLoading('backtest', false);\n    }\n  }, [setLoading, addBacktestResult, clearError, handleError]);\n\n  const loadBacktestHistory = useCallback(async () => {\n    try {\n      setLoading('backtest', true);\n      clearError();\n      const results = await apiClient.getBacktestHistory();\n      setBacktestResults(results);\n      return results;\n    } catch (error) {\n      handleError(error, 'load backtest history');\n      return [];\n    } finally {\n      setLoading('backtest', false);\n    }\n  }, [setLoading, setBacktestResults, clearError, handleError]);\n\n  // Market data operations\n  const loadMarketData = useCallback(async (\n    symbol: string, \n    startDate: string, \n    endDate: string\n  ) => {\n    try {\n      setLoading('data', true);\n      clearError();\n      const data = await apiClient.getMarketData(symbol, startDate, endDate);\n      return data;\n    } catch (error) {\n      handleError(error, 'load market data');\n      return [];\n    } finally {\n      setLoading('data', false);\n    }\n  }, [setLoading, clearError, handleError]);\n\n  // Health check\n  const checkHealth = useCallback(async () => {\n    try {\n      return await apiClient.healthCheck();\n    } catch (error) {\n      console.error('Health check failed:', error);\n      return false;\n    }\n  }, []);\n\n  return {\n    // Strategy operations\n    loadStrategies,\n    createStrategy,\n    deleteStrategy,\n    \n    // Backtest operations\n    runBacktest,\n    loadBacktestHistory,\n    \n    // Data operations\n    loadMarketData,\n    \n    // System operations\n    checkHealth,\n  };\n};"], "names": ["API_CONFIG", "STRATEGIES", "BACKTEST", "DATA", "HEALTH", "DEFAULT_CONFIG", "INITIAL_CAPITAL", "COMMISSION", "REFRESH_INTERVAL", "apiClient", "client", "constructor", "this", "axios", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "apiError", "message", "data", "status", "details", "healthCheck", "get", "getStrategies", "createStrategy", "strategy", "post", "deleteStrategy", "id", "delete", "runBacktest", "getBacktestHistory", "getMarketData", "symbol", "startDate", "endDate", "params", "start_date", "end_date", "useApi", "setLoading", "setStrategies", "addStrategy", "removeStrategy", "setBacktestResults", "addBacktestResult", "setError", "clearError", "useAppStore", "handleError", "useCallback", "action", "loadStrategies", "async", "strategies", "success", "result", "loadBacktestHistory", "results", "loadMarketData", "checkHealth"], "mappings": "kLACO,MAAMA,EAC+B,wBAD/BA,EAEA,CACTC,WAAY,kBACZC,SAAU,gBACVC,KAAM,YACNC,OAAQ,eANCJ,EAQF,IAGEK,EAAiB,CAC5BC,gBAAiB,IACjBC,WAAY,KACZC,iBAAkB,KC+Fb,MAAMC,EAAY,IAlGzB,MACUC,OAER,WAAAC,GACEC,KAAKF,OAASG,EAAMC,OAAO,CACzBC,QAASf,EACTgB,QAAShB,EACTiB,QAAS,CACP,eAAgB,sBAKpBL,KAAKF,OAAOQ,aAAaC,QAAQC,IAC9BC,GAEQA,EAERC,GAAUC,QAAQC,OAAOF,IAI5BV,KAAKF,OAAOQ,aAAaO,SAASL,IAC/BK,GAA4BA,EAC5BH,IACC,MAAMI,EAAqB,CACzBC,QAASL,EAAMG,UAAUG,MAAMD,SAAWL,EAAMK,SAAW,gBAC3DE,OAAQP,EAAMG,UAAUI,QAAU,IAClCC,QAASR,EAAMG,UAAUG,MAG3B,OAAOL,QAAQC,OAAOE,IAG5B,CAGA,iBAAMK,GACJ,IAEE,aADMnB,KAAKF,OAAOsB,IAAIhC,EAAqBI,SACpC,CACT,CAAA,MACE,OAAO,CACT,CACF,CAGA,mBAAM6B,GAIJ,aAHuBrB,KAAKF,OAAOsB,IACjChC,EAAqBC,aAEP2B,KAAKA,IACvB,CAEA,oBAAMM,CAAeC,GAKnB,aAJuBvB,KAAKF,OAAO0B,KACjCpC,EAAqBC,WACrBkC,IAEcP,KAAKA,IACvB,CAEA,oBAAMS,CAAeC,SACb1B,KAAKF,OAAO6B,OAAO,GAAGvC,EAAqBC,cAAcqC,IACjE,CAGA,iBAAME,CAAYrB,GAKhB,aAJuBP,KAAKF,OAAO0B,KACjCpC,EAAqBE,SACrBiB,IAEcS,KAAKA,IACvB,CAEA,wBAAMa,GAIJ,aAHuB7B,KAAKF,OAAOsB,IACjC,GAAGhC,EAAqBE,qBAEV0B,KAAKA,IACvB,CAGA,mBAAMc,CACJC,EACAC,EACAC,GAQA,aANuBjC,KAAKF,OAAOsB,IACjC,GAAGhC,EAAqBG,QAAQwC,IAChC,CACEG,OAAQ,CAAEC,WAAYH,EAAWI,SAAUH,MAG/BjB,KAAKA,IACvB,GCrGWqB,EAAS,KACpB,MAAMC,WACJA,EAAAC,cACAA,EAAAC,YACAA,EAAAC,eACAA,EAAAC,mBACAA,EAAAC,kBACAA,EAAAC,SACAA,EAAAC,WACAA,GACEC,IAGEC,EAAcC,EAAAA,YAAY,CAACtC,EAAYuC,KAE3CL,EAASlC,GACTK,EAAQL,MAAMA,EAAMK,SAAW,aAAakC,MAC3C,CAACL,IA+GJ,MAAO,CAELM,eA9GqBF,EAAAA,YAAYG,UACjC,IACEb,EAAW,cAAc,GACzBO,IACA,MAAMO,QAAmBvD,EAAUwB,gBAEnC,OADAkB,EAAca,GACPA,CACT,OAAS1C,GAEP,OADAqC,EAAYrC,EAAO,mBACZ,EACT,CAAA,QACE4B,EAAW,cAAc,EAC3B,GACC,CAACA,EAAYC,EAAeM,EAAYE,IAkGzCzB,eAhGqB0B,cAAYG,MAAO5C,IACxC,IACE+B,EAAW,cAAc,GACzBO,IACA,MAAMtB,QAAiB1B,EAAUyB,eAAef,GAGhD,OAFAiC,EAAYjB,GACZR,EAAQsC,QAAQ,iCACT9B,CACT,OAASb,GAEP,OADAqC,EAAYrC,EAAO,mBACZ,IACT,CAAA,QACE4B,EAAW,cAAc,EAC3B,GACC,CAACA,EAAYE,EAAaK,EAAYE,IAmFvCtB,eAjFqBuB,cAAYG,MAAOzB,IACxC,IAME,OALAY,EAAW,cAAc,GACzBO,UACMhD,EAAU4B,eAAeC,GAC/Be,EAAef,GACfX,EAAQsC,QAAQ,kCACT,CACT,OAAS3C,GAEP,OADAqC,EAAYrC,EAAO,oBACZ,CACT,CAAA,QACE4B,EAAW,cAAc,EAC3B,GACC,CAACA,EAAYG,EAAgBI,EAAYE,IAsE1CnB,YAnEkBoB,cAAYG,MAAO5C,IACrC,IACE+B,EAAW,YAAY,GACvBO,IACA,MAAMS,QAAezD,EAAU+B,YAAYrB,GAG3C,OAFAoC,EAAkBW,GAClBvC,EAAQsC,QAAQ,mCACTC,CACT,OAAS5C,GAEP,OADAqC,EAAYrC,EAAO,gBACZ,IACT,CAAA,QACE4B,EAAW,YAAY,EACzB,GACC,CAACA,EAAYK,EAAmBE,EAAYE,IAsD7CQ,oBApD0BP,EAAAA,YAAYG,UACtC,IACEb,EAAW,YAAY,GACvBO,IACA,MAAMW,QAAgB3D,EAAUgC,qBAEhC,OADAa,EAAmBc,GACZA,CACT,OAAS9C,GAEP,OADAqC,EAAYrC,EAAO,yBACZ,EACT,CAAA,QACE4B,EAAW,YAAY,EACzB,GACC,CAACA,EAAYI,EAAoBG,EAAYE,IA0C9CU,eAvCqBT,EAAAA,YAAYG,MACjCpB,EACAC,EACAC,KAEA,IACEK,EAAW,QAAQ,GACnBO,IAEA,aADmBhD,EAAUiC,cAAcC,EAAQC,EAAWC,EAEhE,OAASvB,GAEP,OADAqC,EAAYrC,EAAO,oBACZ,EACT,CAAA,QACE4B,EAAW,QAAQ,EACrB,GACC,CAACA,EAAYO,EAAYE,IA0B1BW,YAvBkBV,EAAAA,YAAYG,UAC9B,IACE,aAAatD,EAAUsB,aACzB,OAAST,GAEP,OAAO,CACT,GACC"}