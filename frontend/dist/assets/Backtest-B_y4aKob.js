import{u as e,a as r,j as t,C as o}from"./index-D0Fokv29.js";import{r as s}from"./react-vendor-eITpvC6N.js";import{F as a,T as n,S as c,B as l,D as i,t as d,s as m,l as u,m as P,n as h,o as y,v as j,M as N,w as f,E as b,G as x,y as v,b as p}from"./ui-vendor-BjEg0IST.js";import{u as g,D as k}from"./useApi-edF36RG5.js";import{g as D,f as B,d as E,a as U,c as V}from"./format-DdDemiMk.js";import{b as _,c as w}from"./export-jIOAvY8l.js";import"./store-vendor-84rN_0kr.js";import"./utils-vendor-C-1G2k3o.js";const{Title:I}=n,{Option:S}=f,{RangePicker:C}=b,M=()=>{const{strategies:n,backtestResults:b,loading:M}=e(),{loadStrategies:R,loadBacktestHistory:T,runBacktest:A}=g(),[Y,F]=s.useState(!1),[L]=a.useForm();s.useEffect(()=>{R(),T()},[R,T]),r([{...o.RUN_BACKTEST,callback:()=>n.length>0&&F(!0)},{...o.REFRESH,callback:()=>T()},{...o.EXPORT,callback:()=>b.length>0&&_(b)}]);const O=[{title:"Symbol",dataIndex:"symbol",key:"symbol",sorter:(e,r)=>e.symbol.localeCompare(r.symbol)},{title:"Strategy",dataIndex:"strategy_id",key:"strategy_id",render:e=>{const r=n.find(r=>r.id===e);return r?r.name:"Unknown"}},{title:"Period",key:"period",render:(e,r)=>t.jsxDEV("div",{children:[t.jsxDEV("div",{children:V(r.start_date)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:125,columnNumber:11},void 0),t.jsxDEV("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:["to ",V(r.end_date)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:126,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:124,columnNumber:9},void 0)},{title:"Initial Capital",dataIndex:"initial_capital",key:"initial_capital",render:e=>E(e),sorter:(e,r)=>e.initial_capital-r.initial_capital},{title:"Final Capital",dataIndex:"final_capital",key:"final_capital",render:e=>E(e),sorter:(e,r)=>e.final_capital-r.final_capital},{title:"Total Return",dataIndex:"total_return",key:"total_return",render:e=>t.jsxDEV("span",{style:{color:D(e),fontWeight:500},children:B(e)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:151,columnNumber:9},void 0),sorter:(e,r)=>e.total_return-r.total_return},{title:"Sharpe Ratio",dataIndex:"sharpe_ratio",key:"sharpe_ratio",render:e=>U(e,3),sorter:(e,r)=>e.sharpe_ratio-r.sharpe_ratio},{title:"Max Drawdown",dataIndex:"max_drawdown",key:"max_drawdown",render:e=>t.jsxDEV("span",{style:{color:"#ff4d4f"},children:B(Math.abs(e))},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:169,columnNumber:9},void 0),sorter:(e,r)=>e.max_drawdown-r.max_drawdown},{title:"Trades",dataIndex:"trade_count",key:"trade_count",sorter:(e,r)=>e.trade_count-r.trade_count},{title:"Performance",dataIndex:"total_return",key:"performance",render:e=>{return(r=e)>.1?t.jsxDEV(p,{color:"green",children:"Excellent"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:98,columnNumber:35},void 0):r>.05?t.jsxDEV(p,{color:"blue",children:"Good"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:99,columnNumber:36},void 0):r>0?t.jsxDEV(p,{color:"orange",children:"Positive"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:100,columnNumber:33},void 0):t.jsxDEV(p,{color:"red",children:"Negative"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:101,columnNumber:12},void 0);var r}},{title:"Created",dataIndex:"created_at",key:"created_at",render:e=>V(e),sorter:(e,r)=>new Date(e.created_at).getTime()-new Date(r.created_at).getTime()}],G=b.length,z=G>0?b.reduce((e,r)=>e+r.total_return,0)/G:0,q=G>0?Math.max(...b.map(e=>e.total_return)):0,$=G>0?Math.min(...b.map(e=>e.total_return)):0;return t.jsxDEV("div",{children:[t.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[t.jsxDEV("div",{children:[t.jsxDEV(I,{level:2,children:"Backtest Results"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:217,columnNumber:11},void 0),t.jsxDEV("p",{style:{color:"#8c8c8c",margin:0},children:"Run backtests and analyze strategy performance"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:218,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:216,columnNumber:9},void 0),t.jsxDEV(c,{children:[t.jsxDEV(l,{icon:t.jsxDEV(i,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:224,columnNumber:19},void 0),onClick:()=>T(),loading:M.backtest,children:"Refresh"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:223,columnNumber:11},void 0),b.length>0&&t.jsxDEV(l,{icon:t.jsxDEV(d,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:232,columnNumber:21},void 0),onClick:()=>_(b),children:"Export CSV"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:231,columnNumber:13},void 0),t.jsxDEV(l,{type:"primary",icon:t.jsxDEV(m,{},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:240,columnNumber:19},void 0),onClick:()=>F(!0),disabled:0===n.length,children:"Run Backtest"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:238,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:222,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:210,columnNumber:7},void 0),G>0&&t.jsxDEV(u,{gutter:[16,16],style:{marginBottom:"24px"},children:[t.jsxDEV(P,{xs:24,sm:6,children:t.jsxDEV(h,{children:t.jsxDEV(y,{title:"Total Backtests",value:G,valueStyle:{color:"#1890ff"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:254,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:253,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:252,columnNumber:11},void 0),t.jsxDEV(P,{xs:24,sm:6,children:t.jsxDEV(h,{children:t.jsxDEV(y,{title:"Average Return",value:B(z),valueStyle:{color:D(z)}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:263,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:262,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:261,columnNumber:11},void 0),t.jsxDEV(P,{xs:24,sm:6,children:t.jsxDEV(h,{children:t.jsxDEV(y,{title:"Best Return",value:B(q),valueStyle:{color:"#52c41a"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:272,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:271,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:270,columnNumber:11},void 0),t.jsxDEV(P,{xs:24,sm:6,children:t.jsxDEV(h,{children:t.jsxDEV(y,{title:"Worst Return",value:B($),valueStyle:{color:"#ff4d4f"}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:281,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:280,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:279,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:251,columnNumber:9},void 0),t.jsxDEV(h,{children:t.jsxDEV(j,{columns:O,dataSource:b,rowKey:"id",loading:M.backtest,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`Total ${e} backtests`},scroll:{x:1200}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:292,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:291,columnNumber:7},void 0),t.jsxDEV(N,{title:"Run New Backtest",open:Y,onCancel:()=>{F(!1),L.resetFields()},footer:null,width:600,children:t.jsxDEV(a,{form:L,layout:"vertical",onFinish:async e=>{const[r,t]=e.dateRange;if(w(e.strategy_id,e.symbol,r.format("YYYY-MM-DD"),t.format("YYYY-MM-DD"),e.initial_capital))return;const o={strategy_id:e.strategy_id,symbol:e.symbol.toUpperCase(),start_date:r.format("YYYY-MM-DD"),end_date:t.format("YYYY-MM-DD"),initial_capital:e.initial_capital};await A(o)&&(F(!1),L.resetFields())},initialValues:{initial_capital:k.INITIAL_CAPITAL},children:[t.jsxDEV(a.Item,{name:"strategy_id",label:"Strategy",rules:[{required:!0,message:"Please select a strategy!"}],children:t.jsxDEV(f,{placeholder:"Select strategy",children:n.map(e=>t.jsxDEV(S,{value:e.id,children:[e.name," (",e.type,")"]},e.id,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:332,columnNumber:17},void 0))},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:330,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:325,columnNumber:11},void 0),t.jsxDEV(a.Item,{name:"symbol",label:"Symbol",rules:[{required:!0,message:"Please input symbol!"}],children:t.jsxDEV(f,{placeholder:"Select or enter symbol",showSearch:!0,allowClear:!0,options:[{value:"AAPL",label:"AAPL - Apple Inc."},{value:"GOOGL",label:"GOOGL - Alphabet Inc."},{value:"MSFT",label:"MSFT - Microsoft Corp."},{value:"TSLA",label:"TSLA - Tesla Inc."},{value:"AMZN",label:"AMZN - Amazon.com Inc."}]},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:344,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:339,columnNumber:11},void 0),t.jsxDEV(a.Item,{name:"dateRange",label:"Date Range",rules:[{required:!0,message:"Please select date range!"}],children:t.jsxDEV(C,{style:{width:"100%"},disabledDate:e=>e&&e>x().endOf("day")},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:363,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:358,columnNumber:11},void 0),t.jsxDEV(a.Item,{name:"initial_capital",label:"Initial Capital",rules:[{required:!0,message:"Please input initial capital!"}],children:t.jsxDEV(v,{style:{width:"100%"},min:1e3,max:1e7,formatter:e=>`$ ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>{const r=Number(e.replace(/\$\s?|(,*)/g,""));return Math.max(1e3,Math.min(1e7,r))}},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:374,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:369,columnNumber:11},void 0),t.jsxDEV(a.Item,{style:{marginBottom:0,textAlign:"right"},children:t.jsxDEV(c,{children:[t.jsxDEV(l,{onClick:()=>{F(!1),L.resetFields()},children:"Cancel"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:388,columnNumber:15},void 0),t.jsxDEV(l,{type:"primary",htmlType:"submit",loading:M.backtest,children:"Run Backtest"},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:394,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:387,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:386,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:317,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:307,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/PycharmProjects/PythonProject/frontend/src/pages/Backtest.tsx",lineNumber:209,columnNumber:5},void 0)};export{M as default};
//# sourceMappingURL=Backtest-B_y4aKob.js.map
