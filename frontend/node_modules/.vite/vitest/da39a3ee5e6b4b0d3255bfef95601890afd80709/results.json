{"version": "3.2.4", "results": [[":src/utils/__tests__/lazyLoadPerformance.test.ts", {"duration": 0, "failed": true}], [":src/utils/__tests__/SystemDiagnostics.test.ts", {"duration": 0, "failed": true}], [":src/components/common/__tests__/EnhancedFeatureStatusIndicator.test.tsx", {"duration": 0, "failed": true}], [":src/utils/__tests__/enhancedFeatureStatus.test.ts", {"duration": 0, "failed": true}], [":src/hooks/__tests__/useFeatureStatus.test.ts", {"duration": 0, "failed": true}], [":src/utils/__tests__/typeGuards.test.ts", {"duration": 9.643584000000033, "failed": false}], [":src/components/data/__tests__/EconomicCalendar.test.tsx", {"duration": 0, "failed": true}], [":src/components/common/__tests__/LoadingIndicator.test.tsx", {"duration": 0, "failed": true}], [":src/components/charts/__tests__/ResponsiveChart.test.tsx", {"duration": 0, "failed": true}], [":src/components/backtest/__tests__/TaskStatisticsPanel.test.tsx", {"duration": 115.86512500000026, "failed": false}], [":src/hooks/__tests__/useChartManager.test.ts", {"duration": 0, "failed": true}]]}