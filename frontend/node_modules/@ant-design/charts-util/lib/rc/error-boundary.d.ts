import React from 'react';
export declare class ErrorBoundary extends React.Component<any> {
    static getDerivedStateFromError(error: Error): {
        hasError: boolean;
        error: Error;
    };
    static getDerivedStateFromProps(nextProps: any, state: any): {
        children: any;
        hasError: boolean;
        error: any;
    };
    state: {
        hasError: boolean;
        error?: Error;
    };
    renderError: (e: Error) => any;
    render(): any;
}
