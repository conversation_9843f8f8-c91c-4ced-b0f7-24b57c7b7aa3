{"name": "@ant-design/charts-util", "version": "0.0.2", "description": "charts utils", "bugs": {"url": "https://github.com/ant-design/ant-design-charts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-charts.git"}, "main": "lib/index.js", "unpkg": "dist/plots.min.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["/lib", "/es", "/dist"], "peerDependencies": {"react": ">=16.8.4", "react-dom": ">=16.8.4"}, "license": "MIT", "devDependencies": {"@types/jest": "^26.0.24", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1"}, "dependencies": {"lodash": "^4.17.21"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"start": "pnpm lib:es --w", "build": "run-s clean lib dist", "build:lib": "run-s clean lib", "ci": "pnpm run build && pnpm run test:coverage", "clean": "rimraf lib es dist", "dist": "webpack --config webpack.config.js --mode production", "profile": "webpack --config webpack.config.js --mode production --profile --json > stats.json", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.prod.json --target ES5 --module commonjs --outDir lib", "lib:es": "tsc -p tsconfig.prod.json --target ES5 --module ESNext --outDir es", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx  --fix --format=pretty ./src && pnpm run lint:prettier", "lint:prettier": "pnpm run prettier &&  git diff && prettier --version && prettier --check \"src/**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "prettier": "prettier --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "test": "jest", "test:live": "DEBUG_MODE=1 jest --watch ./tests/utils/use-chart.test.tsx --no-cache", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}