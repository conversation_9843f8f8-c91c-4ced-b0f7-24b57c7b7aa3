#!/usr/bin/env python3
"""
量化交易系统高级功能演示
展示机器学习、实时数据流、高级风险管理、投资组合优化等功能
"""

from datetime import datetime
from pathlib import Path
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core import TradingSystem

def print_section(title: str):
    """打印章节标题"""
    print("\n" + "=" * 70)
    print(f"  {title}")
    print("=" * 70)

    def print_subsection(title: str):
    """打印子章节标题"""
    print(f"\n🔹 {title}")
    print("-" * 50)

    def demo_ml_strategy_engine():
    """演示机器学习策略引擎"""
    print_section("机器学习策略引擎演示")

    trading_system = TradingSystem()
    trading_system.start()

    symbol = "AAPL"

    try:
        # 训练随机森林分类器
        print_subsection("训练随机森林分类器")
        print(f"正在为 {symbol} 训练随机森林模型...")

        rf_result = trading_system.train_ml_model(symbol, "random_forest", days=180)

        if "error" not in rf_result:
            print(f"✅ 模型训练成功")
            print(f"   准确率: {rf_result.get('accuracy', 0):.4f}")
            print(f"   精确率: {rf_result.get('precision', 0):.4f}")
            print(f"   召回率: {rf_result.get('recall', 0):.4f}")
            print(f"   F1分数: {rf_result.get('f1_score', 0):.4f}")
            print(f"   训练样本: {rf_result.get('training_samples', 0)}")
            print(f"   特征数量: {rf_result.get('feature_count', 0)}")

            # 显示重要特征
            top_features = rf_result.get('top_features', [])[:5]
            if top_features:
                print(f"   前5个重要特征:")
                for i, (feature, importance) in enumerate(top_features, 1):
                    print(f"     {i}. {feature}: {importance:.4f}")
                    else:
            print(f"❌ 模型训练失败: {rf_result['error']}")

        # 训练梯度提升回归器
        print_subsection("训练梯度提升回归器")
        print(f"正在为 {symbol} 训练梯度提升模型...")

        gb_result = trading_system.train_ml_model(symbol, "gradient_boosting", days=180)

        if "error" not in gb_result:
            print(f"✅ 模型训练成功")
            print(f"   R²分数: {gb_result.get('r2_score', 0):.4f}")
            print(f"   RMSE: {gb_result.get('rmse', 0):.6f}")
            print(f"   MAE: {gb_result.get('mae', 0):.6f}")
            print(f"   训练样本: {gb_result.get('training_samples', 0)}")
            else:
            print(f"❌ 模型训练失败: {gb_result['error']}")

        # ML预测
        print_subsection("机器学习预测")
        ml_info = trading_system.get_ml_model_info()

        if ml_info.get("loaded_models"):
            print(f"已加载模型: {len(ml_info['loaded_models'])} 个")

            for model_key in ml_info["loaded_models"][:2]:  # 测试前2个模型
                print(f"\n使用模型 {model_key} 进行预测:")

                prediction = trading_system.predict_with_ml(model_key, symbol)
            if "error" not in prediction:
                    print(f"   预测值: {prediction.get('prediction', 0):.4f}")

                    # 如果是分类器，显示概率
                    probabilities = prediction.get('probabilities', {})
                if probabilities:
                        print(f"   类别概率:")
                    for class_name, prob in probabilities.items():
                            print(f"     {class_name}: {prob:.4f}")

                # 生成交易信号
                signals = trading_system.generate_ml_signals(model_key, symbol)
                if signals:
                    print(f"   生成信号: {len(signals)} 个")
                    for signal in signals[:2]:  # 显示前2个信号
                        print(f"     {signal.get('type', 'unknown')} @ {signal.get('price', 0):.2f}")
                        print(f"     置信度: {signal.get('confidence', 0):.2f}")
                        print(f"     理由: {signal.get('reason', 'N/A')}")
                    else:
            print("没有可用的ML模型")

            finally:
        trading_system.stop()

    def demo_realtime_data_stream():
    """演示实时数据流处理"""
    print_section("实时数据流处理演示")

    trading_system = TradingSystem()
    trading_system.start()

    try:
        # 等待数据流启动
        print("⏳ 等待实时数据流启动...")
        time.sleep(3)

        # 获取流统计
        print_subsection("数据流统计")
        stream_stats = trading_system.get_stream_statistics()

        print(f"流处理状态: {'活跃' if stream_stats.get('is_streaming') else '非活跃'}")
        print(f"监控品种数: {stream_stats.get('symbols_count', 0)}")
        print(f"总tick数: {stream_stats.get('total_ticks', 0)}")
        print(f"WebSocket客户端: {stream_stats.get('websocket_clients', 0)}")

        # 获取实时数据
        print_subsection("实时数据示例")
        symbols = ["AAPL", "GOOGL", "MSFT"]

        for symbol in symbols:
            realtime_data = trading_system.get_realtime_data(symbol)

            if "error" not in realtime_data:
                market_data = realtime_data.get("market_data", {})
                indicators = realtime_data.get("indicators", {})

                print(f"\n📊 {symbol} 实时数据:")
                if market_data:
                    print(f"   最新价格: {market_data.get('last_price', 0):.2f}")
                    print(f"   成交量: {market_data.get('volume', 0):.0f}")
                    print(f"   VWAP: {market_data.get('vwap', 0):.2f}")
                    print(f"   Tick数量: {market_data.get('tick_count', 0)}")

                    if indicators:
                    print(f"   实时指标:")
                    for key, value in list(indicators.items())[:5]:  # 显示前5个指标
                    if isinstance(value, (int, float)):
                            print(f"     {key}: {value:.4f}")
                        else:
                print(f"❌ 获取 {symbol} 实时数据失败")

        # 等待更多数据
        print("\n⏳ 等待更多实时数据...")
        time.sleep(5)

        # 再次检查统计
        updated_stats = trading_system.get_stream_statistics()
        print(f"\n📈 更新后统计:")
        print(f"总tick数: {updated_stats.get('total_ticks', 0)}")

        symbol_stats = updated_stats.get('symbol_statistics', {})
        for symbol, stats in list(symbol_stats.items())[:3]:
            print(f"   {symbol}: {stats.get('tick_count', 0)} ticks")

            finally:
        trading_system.stop()

    def demo_advanced_risk_management():
    """演示高级风险管理"""
    print_section("高级风险管理演示")

    trading_system = TradingSystem()
    trading_system.start()

    try:
        # 模拟投资组合
        positions = {
            "AAPL": {"value": 50000, "weight": 0.5, "asset_class": "equity"},
            "GOOGL": {"value": 30000, "weight": 0.3, "asset_class": "equity"},
            "MSFT": {"value": 20000, "weight": 0.2, "asset_class": "equity"}
        }

        symbols = list(positions.keys())

        # 计算投资组合VaR
        print_subsection("投资组合VaR计算")
        print("正在计算投资组合风险价值...")

        var_result = trading_system.calculate_portfolio_var(positions, symbols)

        if "error" not in var_result:
            print(f"✅ VaR计算完成")
            print(f"   投资组合VaR (95%): {var_result.get('portfolio_var', 0):.4f}")
            print(f"   期望损失 (ES): {var_result.get('portfolio_es', 0):.4f}")
            print(f"   分散化比率: {var_result.get('diversification_ratio', 0):.4f}")

            # 成分VaR
            component_vars = var_result.get('component_vars', {})
            if component_vars:
                print(f"   成分VaR:")
                for symbol, var in component_vars.items():
                    print(f"     {symbol}: {var:.4f}")
                    else:
            print(f"❌ VaR计算失败: {var_result['error']}")

        # 压力测试
        print_subsection("压力测试")
        scenarios = ["market_crash", "interest_rate_shock", "inflation_surge"]

        for scenario in scenarios:
            print(f"\n🧪 {scenario} 场景:")
            stress_result = trading_system.run_stress_test(positions, scenario)

            if "error" not in stress_result:
                total_pnl = stress_result.get('portfolio_pnl_percentage', 0)
                print(f"   投资组合损益: {total_pnl:.2f}%")

                worst_position = stress_result.get('worst_position')
                best_position = stress_result.get('best_position')

                if worst_position:
                    print(f"   最差仓位: {worst_position}")
                    if best_position:
                    print(f"   最佳仓位: {best_position}")
                    else:
                print(f"   ❌ 压力测试失败: {stress_result['error']}")

        # 流动性风险评估
        print_subsection("流动性风险评估")
        print("正在评估流动性风险...")

        liquidity_result = trading_system.assess_liquidity_risk(positions)

        if "error" not in liquidity_result:
            print(f"✅ 流动性评估完成")
            print(f"   投资组合流动性比率: {liquidity_result.get('portfolio_liquidity_ratio', 0):.2f}")
            print(f"   高风险仓位数: {liquidity_result.get('liquidity_warnings', 0)}")

            position_liquidity = liquidity_result.get('position_liquidity', {})
            for symbol, metrics in position_liquidity.items():
                risk_level = metrics.get('risk_level', '未知')
                liquidation_days = metrics.get('estimated_liquidation_days', 0)
                print(f"   {symbol}: {risk_level}风险, 预计清仓{liquidation_days:.1f}天")
                else:
            print(f"❌ 流动性评估失败: {liquidity_result['error']}")

        # 综合风险报告
        print_subsection("综合风险监控报告")
        print("正在生成风险监控报告...")

        risk_report = trading_system.generate_risk_report(positions)

        if "error" not in risk_report:
            print(f"✅ 风险报告生成完成")

            portfolio_summary = risk_report.get('portfolio_summary', {})
            print(f"   投资组合价值: ${portfolio_summary.get('total_value', 0):,.0f}")
            print(f"   仓位数量: {portfolio_summary.get('position_count', 0)}")

            warnings = risk_report.get('warnings', [])
            print(f"   风险警告: {len(warnings)} 个")

            for warning in warnings[:3]:  # 显示前3个警告
                severity = warning.get('severity', '未知')
                message = warning.get('message', '')
                print(f"     [{severity}] {message}")

            recommendations = risk_report.get('recommendations', [])
            if recommendations:
                print(f"   建议:")
                for rec in recommendations[:2]:
                    print(f"     • {rec}")
                    else:
            print(f"❌ 风险报告生成失败: {risk_report['error']}")

            finally:
        trading_system.stop()

    def demo_portfolio_optimization():
    """演示投资组合优化"""
    print_section("投资组合优化演示")

    trading_system = TradingSystem()
    trading_system.start()

    try:
        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]

        # 均值-方差优化
        print_subsection("均值-方差优化")
        print(f"正在优化投资组合: {', '.join(symbols)}")

        mv_result = trading_system.optimize_portfolio(symbols, "mean_variance")

        if "error" not in mv_result and mv_result.get("optimization_success"):
            print(f"✅ 均值-方差优化完成")
            print(f"   预期收益率: {mv_result.get('expected_return', 0):.4f}")
            print(f"   预期波动率: {mv_result.get('expected_volatility', 0):.4f}")
            print(f"   夏普比率: {mv_result.get('sharpe_ratio', 0):.4f}")

            optimal_weights = mv_result.get('optimal_weights', {})
            print(f"   最优权重:")
            for symbol, weight in optimal_weights.items():
                print(f"     {symbol}: {weight:.4f} ({weight*100:.1f}%)")
                else:
            error_msg = mv_result.get('error', '未知错误')
            print(f"❌ 均值-方差优化失败: {error_msg}")

        # 风险平价优化
        print_subsection("风险平价优化")
        print("正在进行风险平价优化...")

        rp_result = trading_system.optimize_portfolio(symbols, "risk_parity")

        if "error" not in rp_result and rp_result.get("optimization_success"):
            print(f"✅ 风险平价优化完成")
            print(f"   投资组合波动率: {rp_result.get('portfolio_volatility', 0):.4f}")

            optimal_weights = rp_result.get('optimal_weights', {})
            risk_contributions = rp_result.get('risk_contributions', {})

            print(f"   权重和风险贡献:")
            for symbol in symbols:
                weight = optimal_weights.get(symbol, 0)
                risk_contrib = risk_contributions.get(symbol, 0)
                print(f"     {symbol}: 权重{weight:.4f}, 风险贡献{risk_contrib:.4f}")
                else:
            error_msg = rp_result.get('error', '未知错误')
            print(f"❌ 风险平价优化失败: {error_msg}")

        # 层次风险平价
        print_subsection("层次风险平价优化")
        print("正在进行层次风险平价优化...")

        hrp_result = trading_system.optimize_portfolio(symbols, "hierarchical_risk_parity")

        if "error" not in hrp_result and hrp_result.get("optimization_success"):
            print(f"✅ 层次风险平价优化完成")
            print(f"   投资组合收益率: {hrp_result.get('portfolio_return', 0):.4f}")
            print(f"   投资组合波动率: {hrp_result.get('portfolio_volatility', 0):.4f}")
            print(f"   夏普比率: {hrp_result.get('sharpe_ratio', 0):.4f}")

            optimal_weights = hrp_result.get('optimal_weights', {})
            cluster_order = hrp_result.get('cluster_order', [])

            print(f"   聚类顺序: {' -> '.join(cluster_order)}")
            print(f"   HRP权重:")
            for symbol, weight in optimal_weights.items():
                print(f"     {symbol}: {weight:.4f} ({weight*100:.1f}%)")
                else:
            error_msg = hrp_result.get('error', '未知错误')
            print(f"❌ 层次风险平价优化失败: {error_msg}")

        # 比较不同优化方法
        print_subsection("优化方法比较")
        print("正在比较不同的优化方法...")

        methods = ["mean_variance", "risk_parity", "maximum_diversification"]
        comparison_result = trading_system.compare_portfolio_methods(symbols, methods)

        if "error" not in comparison_result:
            print(f"✅ 方法比较完成")

            best_method = comparison_result.get('best_method', '未知')
            ranking = comparison_result.get('ranking', [])

            print(f"   最佳方法: {best_method}")
            print(f"   排名: {' > '.join(ranking)}")

            comparison_results = comparison_result.get('comparison_results', {})
            print(f"   详细比较:")

            for method, metrics in comparison_results.items():
                sharpe = metrics.get('sharpe_ratio', 0)
                volatility = metrics.get('volatility', 0)
                print(f"     {method}: 夏普比率{sharpe:.4f}, 波动率{volatility:.4f}")
                else:
            error_msg = comparison_result.get('error', '未知错误')
            print(f"❌ 方法比较失败: {error_msg}")

            finally:
        trading_system.stop()

    def demo_comprehensive_analysis():
    """演示综合分析功能"""
    print_section("综合分析功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    try:
        symbol = "AAPL"

        print_subsection(f"{symbol} 综合分析")
        print("正在进行全面的综合分析...")

        # 等待系统完全启动
        time.sleep(3)

        analysis_result = trading_system.comprehensive_analysis(symbol)

        if "error" not in analysis_result:
            print(f"✅ 综合分析完成")

            # 市场分析
            market_analysis = analysis_result.get('market_analysis', {})
            if market_analysis and "error" not in market_analysis:
                print(f"\n📊 市场分析:")
                print(f"   市场状态: {market_analysis.get('market_state', '未知')}")
                print(f"   趋势方向: {market_analysis.get('trend_direction', '未知')}")
                print(f"   波动率: {market_analysis.get('volatility', 0):.2f}%")
                print(f"   RSI: {market_analysis.get('rsi', 0):.2f}")

            # 策略推荐
            recommendations = analysis_result.get('strategy_recommendations', [])
            if recommendations:
                print(f"\n🎯 策略推荐:")
                for i, rec in enumerate(recommendations, 1):
                    name = rec.get('name', '未知策略')
                    score = rec.get('score', 0)
                    print(f"   {i}. {name} (评分: {score:.1f})")

            # 实时数据
            realtime_data = analysis_result.get('realtime_data', {})
            if realtime_data and "error" not in realtime_data:
                market_data = realtime_data.get('market_data', {})
                if market_data:
                    print(f"\n📈 实时数据:")
                    print(f"   最新价格: {market_data.get('last_price', 0):.2f}")
                    print(f"   成交量: {market_data.get('volume', 0):.0f}")
                    print(f"   Tick数量: {market_data.get('tick_count', 0)}")

            # ML预测
            ml_predictions = analysis_result.get('ml_predictions', {})
            if ml_predictions:
                print(f"\n🤖 ML预测:")
                for model_key, prediction in ml_predictions.items():
                    pred_value = prediction.get('prediction', 0)
                    print(f"   {model_key}: {pred_value:.4f}")
                    else:
            print(f"❌ 综合分析失败: {analysis_result['error']}")

        # 系统能力概览
        print_subsection("系统能力概览")
        capabilities = trading_system.get_system_capabilities()

        print(f"系统版本: {capabilities.get('version', '未知')}")

        core_capabilities = capabilities.get('capabilities', {})
        print(f"核心功能:")
        for feature, enabled in core_capabilities.items():
            status = "✅" if enabled else "❌"
            print(f"   {status} {feature}")

        ml_info = capabilities.get('ml_models', {})
        print(f"机器学习:")
        print(f"   支持的模型: {', '.join(ml_info.get('supported_types', []))}")
        print(f"   已加载模型: {ml_info.get('loaded_models', 0)} 个")

        optimization_methods = capabilities.get('optimization_methods', [])
        print(f"优化方法: {len(optimization_methods)} 种")

        realtime_features = capabilities.get('realtime_features', {})
        streaming_active = realtime_features.get('streaming_active', False)
        print(f"实时数据流: {'✅ 活跃' if streaming_active else '❌ 非活跃'}")

        finally:
        trading_system.stop()

    def main():
    """主演示函数"""
    print("🚀 量化交易系统高级功能全面演示")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

    demos = [
        ("机器学习策略引擎", demo_ml_strategy_engine),
        ("实时数据流处理", demo_realtime_data_stream),
        ("高级风险管理", demo_advanced_risk_management),
        ("投资组合优化", demo_portfolio_optimization),
        ("综合分析功能", demo_comprehensive_analysis),
    ]

    try:
        for demo_name, demo_func in demos:
            print(f"\n🎬 开始演示: {demo_name}")
            demo_func()
            print(f"✅ {demo_name} 演示完成")

            # 演示间隔
            time.sleep(2)

        # 总结
        print_section("演示总结")
        print("🎉 所有高级功能演示完成！")

        print("\n📋 演示内容回顾:")
        print("✅ 机器学习策略引擎 - 随机森林和梯度提升模型")
        print("✅ 实时数据流处理 - WebSocket数据推送和流式计算")
        print("✅ 高级风险管理 - VaR计算、压力测试、流动性评估")
        print("✅ 投资组合优化 - 多种优化算法和方法比较")
        print("✅ 综合分析功能 - 集成所有功能的全面分析")

        print("\n💡 系统特色:")
        print("🤖 AI驱动的智能交易策略")
        print("📊 实时数据流和指标计算")
        print("🛡️ 全面的风险管理体系")
        print("📈 现代投资组合理论实现")
        print("🔧 高度模块化和可扩展")

        print("\n🌐 访问方式:")
        print("- Web界面: http://localhost:3000")
        print("- API文档: http://localhost:8000/docs")
        print("- 启动命令: python run.py")

        except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
        except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

        if __name__ == "__main__":
    main()
