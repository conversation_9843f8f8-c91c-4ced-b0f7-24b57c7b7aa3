#!/usr/bin/env python3
"""
量化交易系统增强功能演示
展示新增的市场分析、策略推荐、性能监控等功能
"""

from datetime import datetime, timedelta
from pathlib import Path
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import config
from core import TradingSystem

def print_section(title: str):
    """打印章节标题"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

    def print_subsection(title: str):
    """打印子章节标题"""
    print(f"\n📊 {title}")
    print("-" * 40)

    def demo_market_analysis():
    """演示市场分析功能"""
    print_section("市场分析功能演示")

    # 初始化交易系统
    trading_system = TradingSystem()
    trading_system.start()

    symbol = "AAPL"
    print(f"正在分析 {symbol} 的市场状况...")

    # 市场状况分析
    market_analysis = trading_system.analyze_market_conditions(symbol, days=60)

    if "error" not in market_analysis:
        print_subsection("市场状况分析结果")
        print(f"市场状态: {market_analysis.get('market_state', '未知')}")
        print(f"趋势方向: {market_analysis.get('trend_direction', '未知')}")
        print(f"趋势强度: {market_analysis.get('trend_strength', 0):.2f}%")
        print(f"波动率: {market_analysis.get('volatility', 0):.2f}%")
        print(f"RSI: {market_analysis.get('rsi', 0):.2f}")
        print(f"MACD信号: {market_analysis.get('macd_signal', '未知')}")
        print(f"成交量活跃度: {market_analysis.get('volume_activity', '未知')}")

        # 详细指标
        indicators = market_analysis.get('indicators', {})
        if indicators:
            print_subsection("详细技术指标")
            for key, value in indicators.items():
                print(f"{key}: {value}")
                else:
        print(f"❌ 市场分析失败: {market_analysis['error']}")

    trading_system.stop()
    return market_analysis

    def demo_strategy_recommendations():
    """演示策略推荐功能"""
    print_section("策略推荐功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    symbol = "AAPL"
    print(f"正在为 {symbol} 生成策略推荐...")

    # 获取策略推荐
    recommendations = trading_system.get_strategy_recommendations(symbol)

    if recommendations and "error" not in recommendations[0]:
        print_subsection("推荐策略列表")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"\n{i}. {rec.get('name', '未知策略')}")
            print(f"   策略类型: {rec.get('type', '未知')}")
            print(f"   推荐评分: {rec.get('score', 0):.1f}/100")
            print(f"   风险等级: {rec.get('risk_level', '未知')}")
            print(f"   预期收益: {rec.get('expected_return', '未知')}")
            print(f"   持仓周期: {rec.get('holding_period', '未知')}")
            print(f"   推荐理由: {rec.get('reason', '无')}")

            # 显示参数
            params = rec.get('parameters', {})
            if params:
                print(f"   策略参数: {params}")
                else:
        print("❌ 策略推荐失败")

    trading_system.stop()
    return recommendations

    def demo_enhanced_backtest():
    """演示增强回测功能"""
    print_section("增强回测功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    # 使用推荐的策略进行回测
    strategy_config = {
        "name": "演示移动平均策略",
        "type": "moving_average",
        "parameters": {
    "short_window": 10,
    "long_window": 30
        }
    }

    symbol = "AAPL"
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')

    print(f"正在运行 {symbol} 的增强回测...")
    print(f"回测期间: {start_date} 到 {end_date}")
    print(f"策略: {strategy_config['name']}")

    # 运行增强回测
    result = trading_system.run_enhanced_backtest(
        strategy_config=strategy_config,
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_capital=100000
    )

    if "error" not in result:
        print_subsection("回测结果")
        metrics = result.get('metrics', {})
        print(f"总收益率: {metrics.get('total_return', 0):.2f}%")
        print(f"年化收益率: {metrics.get('annual_return', 0):.2f}%")
        print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
        print(f"最大回撤: {metrics.get('max_drawdown', 0):.2f}%")
        print(f"胜率: {metrics.get('win_rate', 0):.1f}%")
        print(f"交易次数: {metrics.get('total_trades', 0)}")

        # 策略评估
        evaluation = result.get('strategy_evaluation', {})
        if evaluation and "error" not in evaluation:
            print_subsection("策略性能评估")
            perf_data = evaluation.get('performance_data', {})
            print(f"性能评级: {evaluation.get('performance_grade', '未知')}")
            print(f"历史测试次数: {evaluation.get('historical_count', 0)}")
            else:
        print(f"❌ 增强回测失败: {result['error']}")

    trading_system.stop()
    return result

    def demo_performance_monitoring():
    """演示性能监控功能"""
    print_section("性能监控功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    print("正在收集系统性能数据...")

    # 等待一段时间让性能监控收集数据
    time.sleep(3)

    # 获取性能指标
    performance = trading_system.get_performance_metrics()

    if "error" not in performance:
        print_subsection("系统性能指标")

        current = performance.get('current_metrics', {})
        if current:
            cpu = current.get('cpu', {})
            memory = current.get('memory', {})
            disk = current.get('disk', {})

            print(f"CPU使用率: {cpu.get('percent', 0):.1f}%")
            print(f"CPU核心数: {cpu.get('count', 0)}")
            print(f"内存使用率: {memory.get('percent', 0):.1f}%")
            print(f"内存总量: {memory.get('total_gb', 0):.1f}GB")
            print(f"可用内存: {memory.get('available_gb', 0):.1f}GB")
            print(f"磁盘使用率: {disk.get('percent', 0):.1f}%")
            print(f"磁盘可用空间: {disk.get('free_gb', 0):.1f}GB")

        # 健康评分
        health_score = performance.get('health_score', 0)
        print(f"\n系统健康评分: {health_score}/100")

        if health_score >= 85:
            print("✅ 系统运行状态优秀")
            elif health_score >= 70:
            print("✅ 系统运行状态良好")
            elif health_score >= 50:
            print("⚠️  系统运行状态一般")
            else:
            print("❌ 系统运行状态需要关注")

        # 监控状态
        monitoring_status = performance.get('monitoring_status', 'inactive')
        print(f"性能监控状态: {monitoring_status}")

        uptime = performance.get('uptime', '未知')
        print(f"系统运行时间: {uptime}")

        else:
        print(f"❌ 获取性能指标失败: {performance['error']}")

    trading_system.stop()
    return performance

    def demo_cache_optimization():
    """演示缓存优化功能"""
    print_section("缓存优化功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    # 获取缓存统计
    print_subsection("缓存统计信息")
    cache_stats = trading_system.get_cache_statistics()

    if "error" not in cache_stats:
        print(f"缓存条目数: {cache_stats.get('total_entries', 0)}")
        print(f"缓存大小: {cache_stats.get('total_size_mb', 0):.2f}MB")
        print(f"最大缓存大小: {cache_stats.get('max_size_mb', 0):.2f}MB")
        print(f"平均访问次数: {cache_stats.get('avg_access_count', 0):.1f}")

        # 内存缓存
        memory_cache = cache_stats.get('memory_cache', {})
        if memory_cache:
            print(f"内存缓存条目: {memory_cache.get('entries', 0)}")
            print(f"内存缓存大小: {memory_cache.get('size_mb', 0):.2f}MB")

        # 性能统计
        performance = cache_stats.get('performance', {})
        if performance:
            print(f"缓存命中次数: {performance.get('cache_hits', 0)}")
            print(f"缓存未命中次数: {performance.get('cache_misses', 0)}")
            print(f"缓存命中率: {performance.get('hit_rate_percent', 0):.1f}%")

    # 运行系统优化
    print_subsection("系统优化")
    print("正在优化系统性能...")

    optimization_result = trading_system.optimize_system_performance()

    if "error" not in optimization_result:
        actions = optimization_result.get('actions', [])
        print(f"执行的优化操作: {', '.join(actions)}")

        cache_opt = optimization_result.get('cache_optimization', {})
        if cache_opt and "actions_taken" in cache_opt:
            print(f"缓存优化操作: {len(cache_opt['actions_taken'])} 项")
            print(f"释放空间: {cache_opt.get('space_freed_mb', 0):.2f}MB")
            print(f"清理条目: {cache_opt.get('entries_removed', 0)} 个")
            else:
        print(f"❌ 系统优化失败: {optimization_result['error']}")

    trading_system.stop()
    return optimization_result

    def demo_strategy_insights():
    """演示策略洞察功能"""
    print_section("策略洞察功能演示")

    trading_system = TradingSystem()
    trading_system.start()

    symbol = "AAPL"
    print(f"正在生成 {symbol} 的策略洞察...")

    # 获取策略洞察
    insights = trading_system.get_strategy_insights(symbol)

    if "error" not in insights:
        print_subsection("策略洞察结果")
        print(f"已测试策略总数: {insights.get('total_strategies_tested', 0)}")

        # 历史最佳策略
        top_strategies = insights.get('top_performing_strategies', [])
        if top_strategies:
            print_subsection("历史最佳策略")
            for i, strategy in enumerate(top_strategies[:3], 1):
                print(f"{i}. {strategy.get('strategy', '未知')}")
                print(f"   综合评分: {strategy.get('score', 0):.2f}")
                print(f"   平均收益: {strategy.get('avg_return', 0):.2f}%")
                print(f"   夏普比率: {strategy.get('avg_sharpe', 0):.3f}")
                print(f"   测试次数: {strategy.get('test_count', 0)}")

        # 市场洞察
        market_insights = insights.get('market_insights', {})
        if market_insights and "error" not in market_insights:
            print_subsection("当前市场洞察")
            print(f"市场状态: {market_insights.get('market_state', '未知')}")
            print(f"趋势方向: {market_insights.get('trend_direction', '未知')}")
            print(f"波动率: {market_insights.get('volatility', 0):.2f}%")

    # 生成策略报告
    print_subsection("策略分析报告")
    report = trading_system.generate_strategy_report(symbol)

    # 显示报告的前几行
    report_lines = report.split('\n')
    for line in report_lines[:20]:  # 显示前20行
        print(line)

    if len(report_lines) > 20:
        print("...")
        print(f"(报告共 {len(report_lines)} 行，仅显示前20行)")

    trading_system.stop()
    return insights, report

    def main():
    """主演示函数"""
    print("🚀 量化交易系统增强功能演示")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 1. 市场分析演示
        market_analysis = demo_market_analysis()

        # 2. 策略推荐演示
        recommendations = demo_strategy_recommendations()

        # 3. 增强回测演示
        backtest_result = demo_enhanced_backtest()

        # 4. 性能监控演示
        performance = demo_performance_monitoring()

        # 5. 缓存优化演示
        optimization = demo_cache_optimization()

        # 6. 策略洞察演示
        insights, report = demo_strategy_insights()

        # 总结
        print_section("演示总结")
        print("✅ 市场分析功能 - 完成")
        print("✅ 策略推荐功能 - 完成")
        print("✅ 增强回测功能 - 完成")
        print("✅ 性能监控功能 - 完成")
        print("✅ 缓存优化功能 - 完成")
        print("✅ 策略洞察功能 - 完成")

        print("\n🎉 所有增强功能演示完成！")
        print("\n💡 提示:")
        print("- 这些功能已集成到Web界面和API中")
        print("- 可以通过 http://localhost:8000/docs 查看完整API文档")
        print("- 运行 python run.py 启动完整系统")

        except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
        except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

        if __name__ == "__main__":
    main()
