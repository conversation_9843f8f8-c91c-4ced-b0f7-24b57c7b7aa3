#!/usr/bin/env python3
"""
量化交易系统 v4.0+ 全面功能演示
展示所有新增的企业级功能，包括实时处理、风险管理、ML预测等
"""
from datetime import datetime, timedelta
import logging
import os
import sys
import time

import numpy as np
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入所有新增模块
from core.advanced_indicators import AdvancedIndicators
from core.advanced_strategies import AdvancedStrategies, SignalType
from core.ml_predictor import ml_predictor
from core.performance_optimizer import performance_optimizer, profile_performance, cache_result
from core.realtime_processor import start_realtime_processing, stop_realtime_processing
from core.risk_manager import risk_manager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_comprehensive_data(days=30, interval_minutes=60):
    """生成全面的市场数据"""
    print("📊 生成全面市场数据...")

    start_date = datetime.now() - timedelta(days=days)
    periods = days * 24 * (60 // interval_minutes)
    dates = pd.date_range(start_date, periods=periods, freq=f'{interval_minutes}min')

    np.random.seed(42)

    # 生成多个交易对的数据
    symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT']
    base_prices = [50000, 3000, 1.5, 25]

    all_data = {}

    for symbol, base_price in zip(symbols, base_prices):
        prices = [base_price]
        volumes = []

        for i in range(1, periods):
            # 添加趋势、周期性和随机性
            trend = 0.0001 * np.sin(i * 0.01)
            cycle = 0.0005 * np.sin(i * 0.1)
            noise = np.random.normal(0, 0.02)

            change = trend + cycle + noise
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

            # 生成相关的成交量
            volatility = abs(change)
            base_volume = 1000 * (base_price / 1000)  # 根据价格调整基础成交量
            volume = base_volume * (1 + volatility * 10) * np.random.uniform(0.5, 2.0)
            volumes.append(int(volume))

        volumes.append(int(base_volume * np.random.uniform(0.5, 2.0)))

        # 生成OHLCV数据
        data = []
        for i in range(len(prices)):
            price = prices[i]
            volume = volumes[i]

            high_factor = 1 + abs(np.random.normal(0, 0.005))
            low_factor = 1 - abs(np.random.normal(0, 0.005))

            high = price * high_factor
            low = price * low_factor

            open_price = prices[i-1] if i > 0 else price
            close_price = price

            data.append({
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })

        all_data[symbol] = pd.DataFrame(data, index=dates)

    print(f"✅ 生成了 {len(symbols)} 个交易对的数据，每个包含 {len(dates)} 条记录")
    return all_data

@profile_performance

def demo_advanced_strategies_comprehensive(all_data):
    """全面演示高级策略"""
    print("\n🧠 高级交易策略全面演示")
    print("=" * 60)

    strategies = AdvancedStrategies()
    all_results = {}

    for symbol, data in all_data.items():
        print(f"\n📈 分析 {symbol}:")

        # 获取所有策略信号
        all_signals = strategies.get_all_signals(data)
        consensus_signals = strategies.get_consensus_signals(data, min_consensus=2)

        # 统计分析
        total_signals = sum(len(signals) for signals in all_signals.values())
        buy_signals = 0
        sell_signals = 0

        for strategy_signals in all_signals.values():
            for signal in strategy_signals:
                if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                    buy_signals += 1
                    elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                    sell_signals += 1

        print(f"   总信号数: {total_signals}")
        print(f"   买入信号: {buy_signals}")
        print(f"   卖出信号: {sell_signals}")
        print(f"   共识信号: {len(consensus_signals)}")

        # 计算信号质量
        if consensus_signals:
            avg_confidence = sum(s.confidence for s in consensus_signals) / len(consensus_signals)
            print(f"   平均置信度: {avg_confidence:.2%}")

        all_results[symbol] = {
            'total_signals': total_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'consensus_signals': len(consensus_signals),
            'avg_confidence': avg_confidence if consensus_signals else 0
        }

    return all_results

@profile_performance

def demo_advanced_indicators_comprehensive(all_data):
    """全面演示高级指标"""
    print("\n📊 高级技术指标全面演示")
    print("=" * 60)

    indicators = AdvancedIndicators()
    all_results = {}

    for symbol, data in all_data.items():
        print(f"\n🔍 分析 {symbol} 技术指标:")

        # 计算所有指标
        all_indicators = indicators.get_all_indicators(data)
        summary = indicators.get_indicator_summary(data)

        print(f"   计算指标类别: {len(all_indicators)}")

        # 显示关键指标
        if 'vwap' in all_indicators and 'vwap_20' in all_indicators['vwap']:
            current_price = data['close'].iloc[-1]
            vwap_price = all_indicators['vwap']['vwap_20'].iloc[-1]
            deviation = (current_price - vwap_price) / vwap_price * 100
            print(f"   VWAP偏离: {deviation:.2f}%")

            if 'volatility_indicators' in all_indicators:
            vol_data = all_indicators['volatility_indicators']
            if 'historical_volatility' in vol_data and not vol_data['historical_volatility'].empty:
                vol = vol_data['historical_volatility'].iloc[-1]
                print(f"   历史波动率: {vol:.2f}%")

                if 'trend_strength' in all_indicators:
            trend_data = all_indicators['trend_strength']
            trend_score = trend_data.get('trend_score', 0)
            print(f"   趋势强度评分: {trend_score}/6")

        all_results[symbol] = {
            'indicator_count': len(all_indicators),
            'summary': summary
        }

    return all_results

    def demo_realtime_processing():
    """演示实时数据处理"""
    print("\n⚡ 实时数据处理演示")
    print("=" * 60)

    print("🚀 启动实时处理系统...")

    # 启动实时处理
    processor = start_realtime_processing()

    print("📡 实时处理已启动，收集数据中...")

    # 运行一段时间收集数据
    for i in range(10):
        time.sleep(1)
        stats = processor.get_statistics()

        if stats:
            print(f"   数据点: {stats.get('total_ticks', 0)}, "
            f"交易对: {len(stats.get('symbols', []))}, "
            f"缓冲区: {stats.get('buffer_size', 0)}")

    # 获取最终统计
    final_stats = processor.get_statistics()
    print(f"\n📊 实时处理统计:")
    print(f"   总tick数: {final_stats.get('total_ticks', 0)}")
    print(f"   交易对数: {len(final_stats.get('symbols', []))}")
    print(f"   处理状态: {final_stats.get('processing_status', 'unknown')}")

    # 停止实时处理
    stop_realtime_processing()
    print("⏹️  实时处理已停止")

    return final_stats

    def demo_risk_management(all_data):
    """演示风险管理"""
    print("\n🛡️ 风险管理系统演示")
    print("=" * 60)

    # 初始化风险管理器
    risk_manager.portfolio.cash = 100000  # 重置资金
    risk_manager.portfolio.positions.clear()  # 清空持仓

    print(f"💰 初始资金: ${risk_manager.portfolio.cash:,.2f}")

    # 模拟一些交易
    trades = [
        ('BTCUSDT', 0.5, 50000),
        ('ETHUSDT', 2.0, 3000),
        ('ADAUSDT', 10000, 1.5),
        ('DOTUSDT', 100, 25)
    ]

    print("\n📈 模拟交易:")
    for symbol, quantity, price in trades:
        # 检查是否可以开仓
        can_open, message = risk_manager.can_open_position(symbol, quantity, price)

        if can_open:
            risk_manager.add_position(symbol, quantity, price, 'LONG')
            print(f"   ✅ {symbol}: 买入 {quantity} @ ${price}")
            else:
            print(f"   ❌ {symbol}: {message}")

    # 更新价格（模拟市场变化）
    price_updates = {}
    for symbol, data in all_data.items():
        current_price = data['close'].iloc[-1]
        # 模拟价格变化
        price_change = np.random.normal(0, 0.05)  # 5%标准差
        new_price = current_price * (1 + price_change)
        price_updates[symbol] = new_price

    risk_manager.update_prices(price_updates)

    # 获取风险报告
    risk_report = risk_manager.get_risk_report()

    print(f"\n📊 风险报告:")
    print(f"   投资组合价值: ${risk_report['portfolio_value']:,.2f}")
    print(f"   总盈亏: ${risk_report['total_pnl']:,.2f} ({risk_report['total_pnl_pct']:.2%})")
    print(f"   现金比例: {risk_report['cash_ratio']:.2%}")
    print(f"   持仓数量: {risk_report['positions_count']}")

    # 显示持仓详情
    if risk_report['positions']:
        print("\n   持仓详情:")
        for symbol, pos_info in risk_report['positions'].items():
            print(f"     {symbol}: 权重={pos_info['weight']:.2%}, "
            f"盈亏={pos_info['pnl_pct']:.2%}")

    # 显示风险警报
    if risk_report['recent_alerts']:
        print(f"\n⚠️  最近警报: {len(risk_report['recent_alerts'])} 个")
        for alert in risk_report['recent_alerts'][-3:]:
            print(f"     {alert['level']}: {alert['message']}")

    return risk_report

@cache_result(ttl=300)

def demo_ml_predictions(all_data):
    """演示机器学习预测"""
    print("\n🤖 机器学习预测演示")
    print("=" * 60)

    results = {}

    for symbol, data in all_data.items():
        print(f"\n🔮 {symbol} ML预测:")

        try:
            # 训练模型
            print("   🏋️  训练模型...")
            performance = ml_predictor.train_model(data, 'random_forest', 5, retrain=True)

            print(f"   📊 模型性能:")
            print(f"      R²: {performance.r2:.4f}")
            print(f"      方向准确率: {performance.accuracy:.4f}")
            print(f"      训练样本: {performance.training_samples}")

            # 进行预测
            prediction = ml_predictor.predict(data, 'random_forest', 5, symbol)

            if prediction:
                print(f"   🎯 预测结果:")
                print(f"      当前价格: ${prediction.current_price:.4f}")
                print(f"      预测价格: ${prediction.predicted_price:.4f}")
                print(f"      置信度: {prediction.confidence:.2%}")
                print(f"      预测变化: {((prediction.predicted_price / prediction.current_price - 1) * 100):+.2f}%")

                results[symbol] = {
                    'current_price': prediction.current_price,
                    'predicted_price': prediction.predicted_price,
                    'confidence': prediction.confidence,
                    'change_pct': (prediction.predicted_price / prediction.current_price - 1) * 100,
                    'model_performance': {
                'r2': performance.r2,
                'accuracy': performance.accuracy
                    }
                }
                else:
                print("   ❌ 预测失败")

                except Exception as e:
            print(f"   ❌ 训练/预测失败: {e}")

    return results

    def demo_performance_optimization():
    """演示性能优化"""
    print("\n⚡ 性能优化系统演示")
    print("=" * 60)

    # 启动性能监控
    performance_optimizer.monitor.start_monitoring(interval=0.5)

    print("🔍 性能监控已启动")

    # 演示缓存功能
    print("\n💾 缓存系统测试:")

    @cache_result(ttl=60)

    def expensive_calculation(size):
        print(f"   执行复杂计算 (大小: {size})...")
        data = np.random.random(size)
        result = np.sum(data ** 2) + np.mean(data ** 3)
        time.sleep(0.05)  # 模拟计算时间
        return result

    # 第一次调用
    start_time = time.time()
    result1 = expensive_calculation(50000)
    time1 = time.time() - start_time
    print(f"   第一次调用: {time1:.3f}s")

    # 第二次调用（缓存）
    start_time = time.time()
    result2 = expensive_calculation(50000)
    time2 = time.time() - start_time
    print(f"   第二次调用: {time2:.3f}s (缓存)")
    print(f"   加速比: {time1/time2:.1f}x")

    # 并行处理演示
    print("\n🔄 并行处理测试:")

    def cpu_task(n):
        return sum(i**2 for i in range(n))

    tasks = [5000, 10000, 15000, 20000]

    # 串行执行
    start_time = time.time()
    serial_results = [cpu_task(n) for n in tasks]
    serial_time = time.time() - start_time
    print(f"   串行执行: {serial_time:.3f}s")

    # 并行执行
    start_time = time.time()
    parallel_results = performance_optimizer.parallel_execute(cpu_task, tasks, use_processes=False)
    parallel_time = time.time() - start_time
    print(f"   并行执行: {parallel_time:.3f}s")

    if parallel_results == serial_results and parallel_time > 0:
        print(f"   加速比: {serial_time/parallel_time:.1f}x")

    # 等待收集性能数据
    time.sleep(2)

    # 获取性能报告
    performance_optimizer.monitor.stop_monitoring()
    report = performance_optimizer.get_performance_report()

    print(f"\n📈 性能报告:")
    if 'summary' in report:
        summary = report['summary']
        print(f"   监控函数: {summary.get('total_functions_monitored', 0)}")
        print(f"   总调用: {summary.get('total_calls', 0)}")
        print(f"   平均执行时间: {summary.get('avg_execution_time', 0):.4f}s")

        if 'system_stats' in report and report['system_stats']:
        sys_stats = report['system_stats']
        print(f"   CPU使用率: {sys_stats['cpu']['average']:.1f}%")
        print(f"   内存使用率: {sys_stats['memory']['average']:.1f}%")

    return report

    def demo_comprehensive_integration(all_data):
    """演示系统综合集成"""
    print("\n🔗 系统综合集成演示")
    print("=" * 60)

    integration_results = {}

    for symbol, data in all_data.items():
        print(f"\n🎯 {symbol} 综合分析:")

        current_price = data['close'].iloc[-1]

        # 策略分析
        strategies = AdvancedStrategies()
        all_signals = strategies.get_all_signals(data)
        consensus_signals = strategies.get_consensus_signals(data, min_consensus=2)

        # 指标分析
        indicators = AdvancedIndicators()
        indicator_summary = indicators.get_indicator_summary(data)

        # 风险评估
        position_size = risk_manager.get_position_sizing_recommendation(
            symbol, 0.75, 0.3  # 假设75%置信度，30%波动率
        )

        # 统计信号
        buy_signals = sum(
            len([s for s in signals if s.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]])
            for signals in all_signals.values()
        )
        sell_signals = sum(
            len([s for s in signals if s.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]])
            for signals in all_signals.values()
        )

        # 市场情绪
        if buy_signals > sell_signals * 1.5:
            sentiment = "强烈看涨"
            elif sell_signals > buy_signals * 1.5:
            sentiment = "强烈看跌"
            elif buy_signals > sell_signals:
            sentiment = "温和看涨"
            elif sell_signals > buy_signals:
            sentiment = "温和看跌"
            else:
            sentiment = "中性"

        print(f"   当前价格: ${current_price:.4f}")
        print(f"   市场情绪: {sentiment}")
        print(f"   信号统计: 买入{buy_signals} | 卖出{sell_signals} | 共识{len(consensus_signals)}")
        print(f"   建议仓位: {position_size:.2%}")

        # 技术指标确认
        if indicator_summary and 'indicators' in indicator_summary:
            print("   技术确认:")
            for ind_name, ind_data in indicator_summary['indicators'].items():
                if 'signal' in ind_data:
                    print(f"     {ind_name}: {ind_data['signal']}")

        integration_results[symbol] = {
            'price': current_price,
            'sentiment': sentiment,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'consensus_signals': len(consensus_signals),
            'recommended_position': position_size,
            'technical_signals': indicator_summary.get('indicators', {})
        }

    return integration_results

def generate_final_report(strategy_results, indicator_results, realtime_stats,
risk_report, ml_results, performance_report, integration_results):
    """生成最终报告"""
    print("\n📋 最终综合报告")
    print("=" * 60)

    # 系统概览
    print("🎯 系统功能验证:")
    print(f"   ✅ 高级策略: {len(strategy_results)} 个交易对分析完成")
    print(f"   ✅ 技术指标: {len(indicator_results)} 个交易对指标计算完成")
    print(f"   ✅ 实时处理: {realtime_stats.get('total_ticks', 0)} 个数据点处理")
    print(f"   ✅ 风险管理: {risk_report['positions_count']} 个持仓管理")
    print(f"   ✅ ML预测: {len(ml_results)} 个模型预测完成")
    print(f"   ✅ 性能优化: 系统监控和优化正常")

    # 交易建议汇总
    print(f"\n💡 交易建议汇总:")
    for symbol, result in integration_results.items():
        print(f"   {symbol}:")
        print(f"     市场情绪: {result['sentiment']}")
        print(f"     建议仓位: {result['recommended_position']:.2%}")
        print(f"     信号强度: {result['buy_signals'] + result['sell_signals']} 个信号")

    # ML预测汇总
    if ml_results:
        print(f"\n🤖 ML预测汇总:")
        for symbol, pred in ml_results.items():
            change_pct = pred['change_pct']
            direction = "📈" if change_pct > 0 else "📉"
            print(f"   {symbol}: {direction} {change_pct:+.2f}% (置信度: {pred['confidence']:.2%})")

    # 风险状况
    print(f"\n🛡️ 风险状况:")
    print(f"   投资组合价值: ${risk_report['portfolio_value']:,.2f}")
    print(f"   总盈亏: {risk_report['total_pnl_pct']:.2%}")
    print(f"   风险警报: {len(risk_report['recent_alerts'])} 个")

    # 性能统计
    if performance_report and 'summary' in performance_report:
        summary = performance_report['summary']
        print(f"\n⚡ 性能统计:")
        print(f"   监控函数: {summary.get('total_functions_monitored', 0)} 个")
        print(f"   函数调用: {summary.get('total_calls', 0)} 次")
        print(f"   平均执行时间: {summary.get('avg_execution_time', 0):.4f}s")

    # 系统评分
    total_signals = sum(r['buy_signals'] + r['sell_signals'] for r in strategy_results.values())
    total_consensus = sum(r['consensus_signals'] for r in integration_results.values())
    ml_accuracy = np.mean([r['model_performance']['accuracy'] for r in ml_results.values()]) if ml_results else 0

    system_score = min(100, (
        (total_signals / 100) * 20 +  # 信号生成能力
        (total_consensus / 10) * 20 +  # 共识信号质量
        ml_accuracy * 30 +  # ML预测准确性
        (1 if realtime_stats.get('total_ticks', 0) > 0 else 0) * 15 +  # 实时处理能力
        (1 if risk_report['positions_count'] > 0 else 0) * 15  # 风险管理能力
    ))

    print(f"\n🏆 系统综合评分: {system_score:.1f}/100")

    if system_score >= 90:
        grade = "A+ (优秀)"
        elif system_score >= 80:
        grade = "A (良好)"
        elif system_score >= 70:
        grade = "B (合格)"
        else:
        grade = "C (需改进)"

    print(f"   评级: {grade}")

    def main():
    """主演示函数"""
    print("🚀 量化交易系统 v4.0+ 全面功能演示")
    print("=" * 70)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("包含: 高级策略、技术指标、实时处理、风险管理、ML预测、性能优化")

    try:
        # 1. 生成数据
        all_data = generate_comprehensive_data(days=30, interval_minutes=60)

        # 2. 高级策略演示
        strategy_results = demo_advanced_strategies_comprehensive(all_data)

        # 3. 高级指标演示
        indicator_results = demo_advanced_indicators_comprehensive(all_data)

        # 4. 实时处理演示
        realtime_stats = demo_realtime_processing()

        # 5. 风险管理演示
        risk_report = demo_risk_management(all_data)

        # 6. ML预测演示
        ml_results = demo_ml_predictions(all_data)

        # 7. 性能优化演示
        performance_report = demo_performance_optimization()

        # 8. 系统集成演示
        integration_results = demo_comprehensive_integration(all_data)

        # 9. 生成最终报告
        generate_final_report(
            strategy_results, indicator_results, realtime_stats,
            risk_report, ml_results, performance_report, integration_results
        )

        print("\n🎉 全面功能演示完成!")
        print("=" * 70)
        print("✨ 量化交易系统 v4.0+ 已达到企业级标准")
        print("🌟 所有高级功能运行正常，系统已就绪投入使用")

        # 清理资源
        performance_optimizer.cleanup()

        except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

        finally:
        # 确保清理资源
        try:
            performance_optimizer.cleanup()
            stop_realtime_processing()
            except:
            pass

            if __name__ == "__main__":
    main()
