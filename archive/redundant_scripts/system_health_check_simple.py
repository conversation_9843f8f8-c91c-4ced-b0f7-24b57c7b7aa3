#!/usr/bin/env python3
"""
Simple System Health Check Script
Checks basic system functionality for the trading system
"""

from datetime import datetime
from pathlib import Path
import json
import sys

def check_python_environment():
    """Check Python environment"""
    try:
        return {
            "status": "healthy",
            "python_version": sys.version,
            "executable": sys.executable
        }
        except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

    def check_project_structure():
    """Check project structure"""
    project_root = Path(__file__).parent.parent
    required_dirs = ["src", "backend", "frontend", "tests", "config"]

    results = {}
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        results[dir_name] = {
            "exists": dir_path.exists(),
            "is_directory": dir_path.is_dir() if dir_path.exists() else False
        }

    all_exist = all(result["exists"] for result in results.values())

    return {
        "status": "healthy" if all_exist else "warning",
        "directories": results
    }

    def check_dependencies():
    """Check key dependencies"""
    dependencies = ["pandas", "numpy", "fastapi", "pytest"]
    results = {}

    for dep in dependencies:
        try:
            __import__(dep)
            results[dep] = {"status": "available"}
            except ImportError:
            results[dep] = {"status": "missing"}

    all_available = all(result["status"] == "available" for result in results.values())

    return {
        "status": "healthy" if all_available else "warning",
        "dependencies": results
    }

    def check_configuration_files():
    """Check configuration files"""
    project_root = Path(__file__).parent.parent
    config_files = [
        "pyproject.toml",
        "frontend/package.json",
        "backend/pyproject.toml"
    ]

    results = {}
    for config_file in config_files:
        file_path = project_root / config_file
        results[config_file] = {
            "exists": file_path.exists(),
            "readable": file_path.is_file() if file_path.exists() else False
        }

    return {
        "status": "healthy",
        "config_files": results
    }

    def run_health_check():
    """Run comprehensive health check"""
    print("Running system health check...")

    health_report = {
        "timestamp": datetime.now().isoformat(),
        "overall_status": "healthy",
        "checks": {}
    }

    # Run individual checks
    checks = [
        ("python_environment", check_python_environment),
        ("project_structure", check_project_structure),
        ("dependencies", check_dependencies),
        ("configuration_files", check_configuration_files)
    ]

    for check_name, check_func in checks:
        try:
            result = check_func()
            health_report["checks"][check_name] = result

            if result["status"] in ["error", "critical"]:
                health_report["overall_status"] = "error"
                elif result["status"] == "warning" and health_report["overall_status"] == "healthy":
                health_report["overall_status"] = "warning"

                except Exception as e:
            health_report["checks"][check_name] = {
                "status": "error",
                "error": str(e)
            }
            health_report["overall_status"] = "error"

    # Print summary
    print(f"Overall Status: {health_report['overall_status'].upper()}")
    for check_name, result in health_report["checks"].items():
        status_symbol = "✅" if result["status"] == "healthy" else "⚠️" if result["status"] == "warning" else "❌"
        print(f"{status_symbol} {check_name}: {result['status']}")

    return health_report

    def main():
    """Main function"""
    try:
        health_report = run_health_check()

        # Save report
        project_root = Path(__file__).parent.parent
        reports_dir = project_root / "test_reports"
        reports_dir.mkdir(exist_ok=True)

        report_file = reports_dir / "health_check_report.json"
        with open(report_file, 'w') as f:
            json.dump(health_report, f, indent=2)

        print(f"\nHealth check report saved to: {report_file}")

        # Exit with appropriate code
        if health_report["overall_status"] == "healthy":
            sys.exit(0)
            elif health_report["overall_status"] == "warning":
            sys.exit(0)  # Warnings are acceptable
            else:
            sys.exit(1)

            except Exception as e:
        print(f"Health check failed: {e}")
        sys.exit(1)

        if __name__ == "__main__":
    main()
