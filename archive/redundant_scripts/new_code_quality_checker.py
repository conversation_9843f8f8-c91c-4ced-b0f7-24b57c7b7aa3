#!/usr/bin/env python3
"""
新的代码质量检查工具
简化版本，专注于核心质量指标
"""

import ast
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import json
import time

class NewCodeQualityChecker:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.issues = {
            'syntax': [],
            'import': [],
            'complexity': [],
            'style': []
        }
        self.metrics = {}
        
    def run_quality_check(self) -> Dict[str, Any]:
        """运行代码质量检查"""
        print("🔍 新代码质量检查工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        # 执行各项检查
        self.check_syntax(python_files)
        self.check_imports(python_files)
        self.check_basic_style(python_files)
        
        # 计算质量评分
        score = self.calculate_quality_score()
        
        # 生成报告
        report = self.generate_report(score)
        
        # 保存报告
        self.save_report(report)
        
        return report
    
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                for file_path in dir_path.rglob("*.py"):
                    # 排除特定模式
                    if not any(pattern in str(file_path) for pattern in ['__pycache__', '.git', 'venv']):
                        python_files.append(file_path)
        
        # 添加根目录的Python文件
        for file_path in self.project_root.glob("*.py"):
            if not any(pattern in str(file_path) for pattern in ['__pycache__', '.git', 'venv']):
                python_files.append(file_path)
        
        return python_files
    
    def check_syntax(self, files: List[Path]):
        """检查语法错误"""
        print("\\n🔍 检查语法错误...")
        syntax_errors = 0
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                try:
                    ast.parse(content)
                except SyntaxError as e:
                    syntax_errors += 1
                    self.issues['syntax'].append({
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': e.lineno,
                        'message': str(e)
                    })
            
            except Exception as e:
                self.issues['syntax'].append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f"文件读取错误: {e}"
                })
        
        self.metrics['syntax_errors'] = syntax_errors
        print(f"   发现 {syntax_errors} 个语法错误")
    
    def check_imports(self, files: List[Path]):
        """检查导入问题"""
        print("\\n🔍 检查导入问题...")
        import_issues = 0
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    line = line.strip()
                    
                    # 检查未使用的导入（简单检查）
                    if line.startswith('import ') or line.startswith('from '):
                        # 这里可以添加更复杂的未使用导入检查逻辑
                        pass
                    
                    # 检查相对导入
                    if 'from .' in line:
                        import_issues += 1
                        self.issues['import'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': i,
                            'message': f"相对导入: {line}"
                        })
            
            except Exception as e:
                self.issues['import'].append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f"导入检查错误: {e}"
                })
        
        self.metrics['import_issues'] = import_issues
        print(f"   发现 {import_issues} 个导入问题")
    
    def check_basic_style(self, files: List[Path]):
        """检查基本代码风格"""
        print("\\n🔍 检查代码风格...")
        style_issues = 0
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    # 检查行尾空格
                    if line.rstrip() != line.rstrip(' \\t'):
                        style_issues += 1
                        self.issues['style'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': i,
                            'message': "行尾有多余空格"
                        })
                    
                    # 检查过长的行
                    if len(line.rstrip()) > 120:
                        style_issues += 1
                        self.issues['style'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': i,
                            'message': f"行过长 ({len(line.rstrip())} 字符)"
                        })
            
            except Exception as e:
                self.issues['style'].append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f"风格检查错误: {e}"
                })
        
        self.metrics['style_issues'] = style_issues
        print(f"   发现 {style_issues} 个风格问题")
    
    def calculate_quality_score(self) -> float:
        """计算质量评分"""
        total_files = len(self.get_python_files())
        if total_files == 0:
            return 0.0
        
        # 基础分数
        score = 100.0
        
        # 语法错误扣分
        syntax_errors = self.metrics.get('syntax_errors', 0)
        score -= syntax_errors * 10  # 每个语法错误扣10分
        
        # 导入问题扣分
        import_issues = self.metrics.get('import_issues', 0)
        score -= import_issues * 2   # 每个导入问题扣2分
        
        # 风格问题扣分
        style_issues = self.metrics.get('style_issues', 0)
        score -= min(style_issues * 0.1, 20)  # 风格问题扣分，最多扣20分
        
        return max(0.0, score)
    
    def generate_report(self, score: float) -> Dict[str, Any]:
        """生成质量报告"""
        print("\\n" + "=" * 50)
        print("📊 代码质量报告")
        print("=" * 50)
        
        # 确定质量等级
        if score >= 90:
            grade = "A (优秀)"
            status = "✅ 代码质量优秀"
        elif score >= 80:
            grade = "B (良好)"
            status = "✅ 代码质量良好"
        elif score >= 70:
            grade = "C (一般)"
            status = "⚠️ 代码质量一般，建议改进"
        elif score >= 60:
            grade = "D (较差)"
            status = "❌ 代码质量较差，需要改进"
        else:
            grade = "F (差)"
            status = "❌ 代码质量很差，急需改进"
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'score': score,
            'grade': grade,
            'status': status,
            'metrics': self.metrics,
            'issues': self.issues,
            'summary': {
                'total_files': len(self.get_python_files()),
                'syntax_errors': self.metrics.get('syntax_errors', 0),
                'import_issues': self.metrics.get('import_issues', 0),
                'style_issues': self.metrics.get('style_issues', 0)
            }
        }
        
        # 打印报告
        print(f"🎯 质量评分: {score:.1f}/100")
        print(f"📊 质量等级: {grade}")
        print(f"📈 {status}")
        print(f"\\n📁 检查文件: {report['summary']['total_files']}")
        print(f"❌ 语法错误: {report['summary']['syntax_errors']}")
        print(f"📦 导入问题: {report['summary']['import_issues']}")
        print(f"🎨 风格问题: {report['summary']['style_issues']}")
        
        return report
    
    def save_report(self, report: Dict[str, Any]):
        """保存报告"""
        report_file = self.project_root / 'new_code_quality_report.json'
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\\n📄 报告已保存: {report_file}")
        
        except Exception as e:
            print(f"\\n❌ 保存报告失败: {e}")

def main():
    """主函数"""
    checker = NewCodeQualityChecker()
    report = checker.run_quality_check()
    
    # 返回退出码
    if report['score'] >= 70:
        sys.exit(0)  # 质量良好
    else:
        sys.exit(1)  # 质量需要改进

if __name__ == "__main__":
    main()