#!/usr/bin/env python3
"""
代码质量改进总结工具
运行所有质量改进工具并生成总结报告
"""
import sys
import os
import subprocess
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class QualityImprovementSummary:
    """质量改进总结器"""
    
    def __init__(self):
        self.project_root = project_root
        self.improvement_results = {}
        
    def run_all_improvements(self):
        """运行所有质量改进"""
        print("🚀 代码质量全面改进工具")
        print("=" * 60)
        print(f"改进时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"项目路径: {self.project_root}")
        print("=" * 60)
        
        # 设置环境变量
        os.environ['PYTHONPATH'] = str(self.project_root)
        
        # 执行改进步骤
        self.run_syntax_fixes()
        self.run_code_formatting()
        self.run_quality_check()
        self.run_system_optimization()
        self.run_final_tests()
        
        # 生成总结报告
        self.generate_summary_report()
        
        return self.improvement_results
    
    def run_syntax_fixes(self):
        """运行语法修复"""
        print("\n🔧 步骤1: 语法修复")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, 'scripts/syntax_fixer.py'
            ], 
            capture_output=True, 
            text=True, 
            cwd=self.project_root,
            timeout=120
            )
            
            if result.returncode == 0:
                print("✅ 语法修复完成")
                # 从输出中提取修复数量
                output = result.stdout
                syntax_fixes = self.extract_number(output, "语法修复:")
                indent_fixes = self.extract_number(output, "缩进修复:")
                
                self.improvement_results['syntax_fixes'] = {
                    'status': 'success',
                    'syntax_fixes': syntax_fixes,
                    'indent_fixes': indent_fixes,
                    'total_fixes': syntax_fixes + indent_fixes
                }
            else:
                print("❌ 语法修复失败")
                self.improvement_results['syntax_fixes'] = {
                    'status': 'failed',
                    'error': result.stderr
                }
                
        except Exception as e:
            print(f"❌ 语法修复异常: {e}")
            self.improvement_results['syntax_fixes'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_code_formatting(self):
        """运行代码格式化"""
        print("\n🎨 步骤2: 代码格式化")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, 'scripts/code_formatter.py'
            ], 
            capture_output=True, 
            text=True, 
            cwd=self.project_root,
            timeout=180
            )
            
            if result.returncode == 0:
                print("✅ 代码格式化完成")
                # 从输出中提取修复数量
                output = result.stdout
                format_fixes = self.extract_number(output, "格式化修复:")
                import_removals = self.extract_number(output, "移除导入:")
                
                self.improvement_results['code_formatting'] = {
                    'status': 'success',
                    'format_fixes': format_fixes,
                    'import_removals': import_removals,
                    'total_improvements': format_fixes + import_removals
                }
            else:
                print("❌ 代码格式化失败")
                self.improvement_results['code_formatting'] = {
                    'status': 'failed',
                    'error': result.stderr
                }
                
        except Exception as e:
            print(f"❌ 代码格式化异常: {e}")
            self.improvement_results['code_formatting'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_quality_check(self):
        """运行质量检查"""
        print("\n🔍 步骤3: 质量检查")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, 'scripts/code_quality_checker.py'
            ], 
            capture_output=True, 
            text=True, 
            cwd=self.project_root,
            timeout=120
            )
            
            # 质量检查可能返回非0退出码，但仍然有有用信息
            output = result.stdout
            
            if "最终质量评分:" in output:
                quality_score = self.extract_quality_score(output)
                total_issues = self.extract_number(output, "总问题数:")
                files_checked = self.extract_number(output, "检查文件数:")
                
                print(f"✅ 质量检查完成 - 评分: {quality_score}")
                
                self.improvement_results['quality_check'] = {
                    'status': 'success',
                    'quality_score': quality_score,
                    'total_issues': total_issues,
                    'files_checked': files_checked
                }
            else:
                print("⚠️ 质量检查部分完成")
                self.improvement_results['quality_check'] = {
                    'status': 'partial',
                    'output': output[:500]  # 前500字符
                }
                
        except Exception as e:
            print(f"❌ 质量检查异常: {e}")
            self.improvement_results['quality_check'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_system_optimization(self):
        """运行系统优化分析"""
        print("\n🔧 步骤4: 系统优化分析")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, 'scripts/system_optimizer.py'
            ], 
            capture_output=True, 
            text=True, 
            cwd=self.project_root,
            timeout=120
            )
            
            if result.returncode == 0:
                output = result.stdout
                health_score = self.extract_health_score(output)
                recommendations = self.extract_number(output, "总建议数:")
                
                print(f"✅ 系统优化分析完成 - 健康评分: {health_score}")
                
                self.improvement_results['system_optimization'] = {
                    'status': 'success',
                    'health_score': health_score,
                    'recommendations': recommendations
                }
            else:
                print("❌ 系统优化分析失败")
                self.improvement_results['system_optimization'] = {
                    'status': 'failed',
                    'error': result.stderr
                }
                
        except Exception as e:
            print(f"❌ 系统优化分析异常: {e}")
            self.improvement_results['system_optimization'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_final_tests(self):
        """运行最终测试验证"""
        print("\n🧪 步骤5: 最终测试验证")
        print("-" * 30)
        
        try:
            # 运行核心测试
            result = subprocess.run([
                sys.executable, 'scripts/run_clean_tests.py'
            ], 
            capture_output=True, 
            text=True, 
            cwd=self.project_root,
            timeout=180
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "所有核心测试通过" in output:
                    print("✅ 核心测试全部通过")
                    self.improvement_results['final_tests'] = {
                        'status': 'success',
                        'core_tests': 'passed'
                    }
                else:
                    print("⚠️ 部分测试通过")
                    self.improvement_results['final_tests'] = {
                        'status': 'partial',
                        'details': output[-200:]  # 最后200字符
                    }
            else:
                print("❌ 测试验证失败")
                self.improvement_results['final_tests'] = {
                    'status': 'failed',
                    'error': result.stderr
                }
                
        except Exception as e:
            print(f"❌ 测试验证异常: {e}")
            self.improvement_results['final_tests'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def extract_number(self, text: str, pattern: str) -> int:
        """从文本中提取数字"""
        import re
        
        # 查找模式后的数字
        match = re.search(f"{re.escape(pattern)}\\s*(\\d+)", text)
        if match:
            return int(match.group(1))
        return 0
    
    def extract_quality_score(self, text: str) -> float:
        """提取质量评分"""
        import re
        
        match = re.search(r"最终质量评分:\s*([\d.]+)/100", text)
        if match:
            return float(match.group(1))
        return 0.0
    
    def extract_health_score(self, text: str) -> float:
        """提取健康评分"""
        import re
        
        match = re.search(r"系统健康评分:\s*([\d.]+)/100", text)
        if match:
            return float(match.group(1))
        return 0.0
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "=" * 60)
        print("📊 代码质量改进总结报告")
        print("=" * 60)
        
        # 计算总体改进数量
        total_improvements = 0
        
        # 语法修复
        if 'syntax_fixes' in self.improvement_results:
            syntax_result = self.improvement_results['syntax_fixes']
            if syntax_result['status'] == 'success':
                total_improvements += syntax_result.get('total_fixes', 0)
                print(f"🔧 语法修复: {syntax_result.get('total_fixes', 0)} 个修复")
            else:
                print(f"🔧 语法修复: {syntax_result['status']}")
        
        # 代码格式化
        if 'code_formatting' in self.improvement_results:
            format_result = self.improvement_results['code_formatting']
            if format_result['status'] == 'success':
                total_improvements += format_result.get('total_improvements', 0)
                print(f"🎨 代码格式化: {format_result.get('total_improvements', 0)} 个改进")
            else:
                print(f"🎨 代码格式化: {format_result['status']}")
        
        # 质量检查
        if 'quality_check' in self.improvement_results:
            quality_result = self.improvement_results['quality_check']
            if quality_result['status'] in ['success', 'partial']:
                quality_score = quality_result.get('quality_score', 0)
                print(f"🔍 代码质量: {quality_score}/100 分")
            else:
                print(f"🔍 代码质量: {quality_result['status']}")
        
        # 系统优化
        if 'system_optimization' in self.improvement_results:
            system_result = self.improvement_results['system_optimization']
            if system_result['status'] == 'success':
                health_score = system_result.get('health_score', 0)
                print(f"🔧 系统健康: {health_score}/100 分")
            else:
                print(f"🔧 系统健康: {system_result['status']}")
        
        # 测试结果
        if 'final_tests' in self.improvement_results:
            test_result = self.improvement_results['final_tests']
            if test_result['status'] == 'success':
                print("🧪 核心测试: ✅ 全部通过")
            else:
                print(f"🧪 核心测试: {test_result['status']}")
        
        print("-" * 60)
        print(f"📈 总改进数量: {total_improvements}")
        
        # 计算总体评分
        overall_score = self.calculate_overall_score()
        print(f"🎯 总体评分: {overall_score:.1f}/100")
        
        # 评级
        if overall_score >= 90:
            grade = "A+ (优秀)"
            print("🏆 代码质量优秀！")
        elif overall_score >= 80:
            grade = "A (良好)"
            print("✅ 代码质量良好")
        elif overall_score >= 70:
            grade = "B (一般)"
            print("⚠️ 代码质量一般，建议继续改进")
        elif overall_score >= 60:
            grade = "C (需改进)"
            print("❌ 代码质量需要改进")
        else:
            grade = "D (差)"
            print("🚨 代码质量较差，需要重点关注")
        
        print(f"📊 质量等级: {grade}")
        
        # 保存详细报告
        self.save_detailed_report(overall_score, grade, total_improvements)
    
    def calculate_overall_score(self) -> float:
        """计算总体评分"""
        scores = []
        
        # 质量评分 (权重: 40%)
        if 'quality_check' in self.improvement_results:
            quality_result = self.improvement_results['quality_check']
            if quality_result['status'] in ['success', 'partial']:
                quality_score = quality_result.get('quality_score', 0)
                scores.append(('quality', quality_score, 0.4))
        
        # 系统健康评分 (权重: 30%)
        if 'system_optimization' in self.improvement_results:
            system_result = self.improvement_results['system_optimization']
            if system_result['status'] == 'success':
                health_score = system_result.get('health_score', 0)
                scores.append(('health', health_score, 0.3))
        
        # 测试通过率 (权重: 20%)
        if 'final_tests' in self.improvement_results:
            test_result = self.improvement_results['final_tests']
            if test_result['status'] == 'success':
                scores.append(('tests', 100, 0.2))
            elif test_result['status'] == 'partial':
                scores.append(('tests', 70, 0.2))
            else:
                scores.append(('tests', 0, 0.2))
        
        # 改进完成度 (权重: 10%)
        syntax_success = self.improvement_results.get('syntax_fixes', {}).get('status') == 'success'
        format_success = self.improvement_results.get('code_formatting', {}).get('status') == 'success'
        
        if syntax_success and format_success:
            scores.append(('improvements', 100, 0.1))
        elif syntax_success or format_success:
            scores.append(('improvements', 50, 0.1))
        else:
            scores.append(('improvements', 0, 0.1))
        
        # 计算加权平均
        if scores:
            weighted_sum = sum(score * weight for _, score, weight in scores)
            total_weight = sum(weight for _, _, weight in scores)
            return weighted_sum / total_weight if total_weight > 0 else 0
        
        return 0
    
    def save_detailed_report(self, overall_score: float, grade: str, total_improvements: int):
        """保存详细报告"""
        import json
        
        report_file = self.project_root / 'quality_improvement_report.json'
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'overall_score': overall_score,
            'grade': grade,
            'total_improvements': total_improvements,
            'improvement_results': self.improvement_results,
            'summary': {
                'syntax_fixes': self.improvement_results.get('syntax_fixes', {}).get('total_fixes', 0),
                'code_improvements': self.improvement_results.get('code_formatting', {}).get('total_improvements', 0),
                'quality_score': self.improvement_results.get('quality_check', {}).get('quality_score', 0),
                'health_score': self.improvement_results.get('system_optimization', {}).get('health_score', 0),
                'tests_passed': self.improvement_results.get('final_tests', {}).get('status') == 'success'
            }
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n📄 详细报告已保存: {report_file}")
            
        except Exception as e:
            print(f"\n⚠️ 无法保存详细报告: {e}")

def main():
    """主函数"""
    summary = QualityImprovementSummary()
    
    try:
        results = summary.run_all_improvements()
        
        # 判断是否成功
        success_count = sum(1 for result in results.values() 
                          if result.get('status') == 'success')
        
        if success_count >= 3:  # 至少3个步骤成功
            print("\n✅ 代码质量改进完成！")
            return True
        else:
            print("\n⚠️ 代码质量改进部分完成")
            return True  # 部分完成也算成功
            
    except Exception as e:
        print(f"\n💥 代码质量改进异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)