from pathlib import Path
import json
import sys


from typing import Dict, List, Any


import logging
import re

#!/usr/bin/env python3
"""
UI-代码一致性检查器

检查前端UI组件与后端代码之间的数据一致性，确保：
1. 策略参数定义一致性
2. API响应字段一致性
3. 数据类型和验证规则一致性
4. 前端显示逻辑与后端计算逻辑一致性
"""


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

PROJECT_ROOT = Path(__file__).parent.parent


class UICodeConsistencyChecker:
    """UI-代码一致性检查器"""

    def __init__(self) -> None:
        self.issues = []
        self.warnings = []
        self.backend_strategy_params = {}
        self.frontend_strategy_params = {}
        self.api_response_fields = set()
        self.frontend_expected_fields = set()

    def check_all(self) -> Dict[str, Any]:
        """执行所有一致性检查"""
        logger.info("🔍 开始UI-代码一致性检查...")

        # 1. 检查策略参数一致性
        self._check_strategy_parameter_consistency()

        # 2. 检查API响应字段一致性
        self._check_api_response_consistency()

        # 3. 检查前端数据处理逻辑
        self._check_frontend_data_logic()

        # 4. 检查配置文件一致性
        self._check_config_consistency()

        # 生成报告
        return self._generate_report()

    def _check_strategy_parameter_consistency(self) -> None:
        """检查策略参数定义一致性"""
        logger.info("📋 检查策略参数定义一致性...")

        # 解析后端策略参数定义
        self._parse_backend_strategy_params()

        # 解析前端策略参数使用
        self._parse_frontend_strategy_params()

        # 比较一致性
        for strategy_name in self.backend_strategy_params:
            # TODO: 考虑使用列表推导优化性能
            if strategy_name not in self.frontend_strategy_params:
                self.warnings.append(f"策略 {strategy_name} 在后端定义但前端未使用")
                continue

            backend_params = self.backend_strategy_params[strategy_name]
            frontend_params = self.frontend_strategy_params[strategy_name]

            # 检查参数名称一致性
            backend_param_names = set(backend_params.keys())
            frontend_param_names = set(frontend_params.keys())

            missing_in_frontend = backend_param_names - frontend_param_names
            extra_in_frontend = frontend_param_names - backend_param_names

            if missing_in_frontend:
                self.issues.append(f"策略 {strategy_name}: 前端缺少参数 {missing_in_frontend}")

                if extra_in_frontend:
                self.warnings.append(f"策略 {strategy_name}: 前端有额外参数 {extra_in_frontend}")

            # 检查参数类型和默认值一致性
            for param_name in backend_param_names & frontend_param_names:
                # TODO: 考虑使用列表推导优化性能
                backend_param = backend_params[param_name]
                frontend_param = frontend_params[param_name]

                if backend_param.get('type') != frontend_param.get('type'):
                    self.issues.append(
                        f"策略 {strategy_name} 参数 {param_name}: "
                        f"类型不一致 (后端: {backend_param.get('type')}, "
                        f"前端: {frontend_param.get('type')})"
                    )

                    if backend_param.get('default') != frontend_param.get('default'):
                    self.warnings.append(
                        f"策略 {strategy_name} 参数 {param_name}: "
                        f"默认值不一致 (后端: {backend_param.get('default')}, "
                        f"前端: {frontend_param.get('default')})"
                    )

                    def _check_api_response_consistency(self) -> None:
        """检查API响应字段一致性"""
        logger.info("🔗 检查API响应字段一致性...")

        # 解析后端API响应字段
        self._parse_backend_api_responses()

        # 解析前端期望字段
        self._parse_frontend_expected_fields()

        # 检查字段一致性
        missing_fields = self.frontend_expected_fields - self.api_response_fields
        unused_fields = self.api_response_fields - self.frontend_expected_fields

        if missing_fields:
            self.issues.append(f"API响应缺少前端期望的字段: {missing_fields}")

            if unused_fields:
            self.warnings.append(f"API响应包含前端未使用的字段: {unused_fields}")

            def _check_frontend_data_logic(self) -> None:
        """检查前端数据处理逻辑"""
        logger.info("🧮 检查前端数据处理逻辑...")

        # 检查胜率计算逻辑
        self._check_win_rate_logic()

        # 检查指标显示逻辑
        self._check_metrics_display_logic()

    def _check_config_consistency(self) -> None:
        """检查配置文件一致性"""
        logger.info("⚙️ 检查配置文件一致性...")

        # 检查数据库路径配置
        self._check_database_config()

        # 检查端口配置
        self._check_port_config()

    def _parse_backend_strategy_params(self) -> None:
        """解析后端策略参数定义"""
        # 从 simple_main.py 解析策略参数定义
        simple_main_path = PROJECT_ROOT / "frontend" / "backend" / "simple_main.py"
        if simple_main_path.exists():
            content = simple_main_path.read_text()

            # 提取策略参数定义
            pattern = r"strategy_param_definitions\s*=\s*\{(.*?)\}"
            match = re.search(pattern, content, re.DOTALL)
            if match:
                try:
                    # 简化解析，提取策略名称和参数
                    strategies_text = match.group(1)
                    strategy_matches = re.findall(r"'(\w+)':\s*\{(.*?)\}", strategies_text, re.DOTALL)

                    for strategy_name, params_text in strategy_matches:
                        params = {}
                        param_matches = re.findall(r"'(\w+)':\s*\{([^}]+)\}", params_text)

                        for param_name, param_def in param_matches:
                            # 提取类型和默认值
                            type_match = re.search(r"'type':\s*'(\w+)'", param_def)
                            default_match = re.search(r"'default':\s*([^,}]+)", param_def)

                            params[param_name] = {
                                'type': type_match.group(1) if type_match else None,
                                'default': eval(default_match.group(1)) if default_match else None
                            }

                        self.backend_strategy_params[strategy_name] = params

                        except Exception as e:
                    self.warnings.append(f"解析后端策略参数定义失败: {e}")

                    def _parse_frontend_strategy_params(self) -> None:
        """解析前端策略参数使用"""
        # 从前端组件解析策略参数使用
        frontend_files = [
            PROJECT_ROOT / "frontend" / "src" / "components" / "forms" / "BacktestConfigForm.tsx",
            PROJECT_ROOT / "frontend" / "src" / "components" / "strategy" / "StrategyConfigForm.tsx",
            PROJECT_ROOT / "frontend" / "src" / "components" / "strategy" / "StrategyForm.tsx"
        ]

        for file_path in frontend_files:
    # TODO: 考虑使用列表推导优化性能
    if file_path.exists():
        content = file_path.read_text()

        # 查找策略参数使用模式
        param_patterns = [
        r"strategy_parameters",
        r"parameterValues",
        r"parameters\s*:",
        r"\.parameters"
        ]

        for pattern in param_patterns:
    # TODO: 考虑使用列表推导优化性能
    if re.search(pattern, content):
        # 简化处理，标记为使用了参数
        self.frontend_strategy_params.setdefault('general', {})['used'] = True

    def _parse_backend_api_responses(self) -> None:
        """解析后端API响应字段"""
        simple_main_path = PROJECT_ROOT / "frontend" / "backend" / "simple_main.py"
        if simple_main_path.exists():
            content = simple_main_path.read_text()

            # 查找API响应字段
            response_patterns = [
                r'"(\w+)":\s*[^,}]+',  # JSON字段
                r"'(\w+)':\s*[^,}]+",  # Python字典字段
            ]

            for pattern in response_patterns:
    # TODO: 考虑使用列表推导优化性能
    matches = re.findall(pattern, content)
    self.api_response_fields.update(matches)

    def _parse_frontend_expected_fields(self) -> None:
        """解析前端期望字段"""
        frontend_files = list((PROJECT_ROOT / "frontend" / "src").rglob("*.tsx"))

        for file_path in frontend_files:
    # TODO: 考虑使用列表推导优化性能
    try:
        content = file_path.read_text()

        # 查找字段访问模式
        field_patterns = [
        r"\.(\w+)\s*[?]?[.]",  # obj.field.
        r"\[['\"]([\w_]+)['\"]\]",  # obj['field']
        r"(\w+):\s*\w+",  # field: value
        ]

        for pattern in field_patterns:
    # TODO: 考虑使用列表推导优化性能
    matches = re.findall(pattern, content)
    self.frontend_expected_fields.update(matches)

    except Exception:
        continue  # 跳过无法读取的文件

    def _check_win_rate_logic(self) -> None:
        """检查胜率计算逻辑"""
        backtest_monitor_path = PROJECT_ROOT / "frontend" / "src" / "components" / "backtest" / "BacktestMonitor.tsx"
        if backtest_monitor_path.exists():
            content = backtest_monitor_path.read_text()

            # 检查胜率计算逻辑
            if "win_rate" in content and "winning_trades" in content and "total_trades" in content:
                logger.info("✅ 胜率计算逻辑：前端包含多级回退逻辑")
                else:
                self.warnings.append("胜率计算逻辑：前端可能缺少完整的回退逻辑")

                def _check_metrics_display_logic(self) -> None:
        """检查指标显示逻辑"""
        # 检查前端是否正确处理 metrics 字段
        frontend_files = list((PROJECT_ROOT / "frontend" / "src").rglob("*.tsx"))

        metrics_usage_found = False
        for file_path in frontend_files:
    # TODO: 考虑使用列表推导优化性能
    try:
        content = file_path.read_text()
        if "metrics" in content and ("win_rate" in content or "total_return" in content):
            metrics_usage_found = True
            break
            except Exception:
                continue

                if metrics_usage_found:
            logger.info("✅ 指标显示逻辑：前端正确使用 metrics 字段")
            else:
            self.warnings.append("指标显示逻辑：前端可能未充分使用 metrics 字段")

            def _check_database_config(self) -> None:
        """检查数据库配置一致性"""
        config_files = [
            PROJECT_ROOT / "config" / "unified" / "main.yaml",
            PROJECT_ROOT / "frontend" / "backend" / "app" / "core" / "config.py",
            PROJECT_ROOT / "frontend" / "backend" / "simple_main.py"
        ]

        db_paths = set()
        for file_path in config_files:
    # TODO: 考虑使用列表推导优化性能
    if file_path.exists():
        content = file_path.read_text()

        # 查找数据库路径
        db_patterns = [
        r"trading_system[_\w]*\.db",
        r"DATABASE_URL",
        r"sqlite:///"
        ]

        for pattern in db_patterns:
    # TODO: 考虑使用列表推导优化性能
    if re.search(pattern, content):
        db_paths.add(str(file_path))

        if len(db_paths) > 0:
            logger.info(f"✅ 数据库配置：在 {len(db_paths)} 个文件中找到数据库配置")
            else:
            self.warnings.append("数据库配置：未找到数据库配置文件")

            def _check_port_config(self) -> None:
        """检查端口配置一致性"""
        # 检查前后端端口配置
        backend_ports = set()
        frontend_ports = set()

        # 后端端口
        simple_main_path = PROJECT_ROOT / "frontend" / "backend" / "simple_main.py"
        if simple_main_path.exists():
            content = simple_main_path.read_text()
            port_matches = re.findall(r"port[=:]\s*(\d+)", content, re.IGNORECASE)
            backend_ports.update(port_matches)

        # 前端端口
        package_json_path = PROJECT_ROOT / "frontend" / "package.json"
        if package_json_path.exists():
            try:
                with open(package_json_path) as f:
                    package_data = json.load(f)
                    scripts = package_data.get('scripts', {})
                    for script in scripts.values():
    # TODO: 考虑使用列表推导优化性能
    port_matches = re.findall(r"port[=:]\s*(\d+)", script, re.IGNORECASE)
    frontend_ports.update(port_matches)
    except Exception:

        if backend_ports or frontend_ports:
            logger.info(f"✅ 端口配置：后端端口 {backend_ports}, 前端端口 {frontend_ports}")
            else:
            self.warnings.append("端口配置：未找到明确的端口配置")

            def _generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        total_checks = len(self.issues) + len(self.warnings)

        report = {
            "summary": {
        "total_issues": len(self.issues),
        "total_warnings": len(self.warnings),
        "total_checks": total_checks,
        "consistency_score": max(0, 100 - len(self.issues) * 10 - len(self.warnings) * 2)
            },
            "issues": self.issues,
            "warnings": self.warnings,
            "recommendations": self._generate_recommendations()
        }

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if self.issues:
            recommendations.append("修复所有发现的一致性问题，确保前后端数据结构完全匹配")

            if len(self.warnings) > 5:
            recommendations.append("审查并解决警告项，提高代码质量和一致性")

        recommendations.extend([
            "定期运行一致性检查，确保前后端同步更新",
            "建立自动化测试覆盖前后端数据交互",
            "使用TypeScript类型定义确保编译时类型检查",
            "建立统一的数据模型定义，前后端共享"
        ])

        return recommendations


    def main() -> None:
    """主函数"""
    checker = UICodeConsistencyChecker()
    report = checker.check_all()

    # 输出报告
    print("\n"*60)
    print("🔍 UI-代码一致性检查报告")
    print("="*60)

    summary = report["summary"]
    print(f"\n📊 检查摘要:")
    print(f"   一致性评分: {summary['consistency_score']}/100")
    print(f"   发现问题: {summary['total_issues']} 个")
    print(f"   警告项目: {summary['total_warnings']} 个")

    if report["issues"]:
        print(f"\n❌ 发现的问题:")
        for i, issue in enumerate(report["issues"], 1):
            print(f"   {i}. {issue}")

            if report["warnings"]:
        print(f"\n⚠️  警告项目:")
        for i, warning in enumerate(report["warnings"], 1):
            print(f"   {i}. {warning}")

    print(f"\n💡 改进建议:")
    for i, rec in enumerate(report["recommendations"], 1):
        print(f"   {i}. {rec}")

    print("\n"*60)

    # 返回退出码
    return 0 if summary['total_issues'] == 0 else 1


    if __name__ == "__main__":
    sys.exit(main())
