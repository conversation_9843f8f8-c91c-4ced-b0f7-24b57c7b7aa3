{"timestamp": "2025-08-25T14:34:06.144785", "metrics": {"cpu_percent": 34.9, "cpu_count": 10, "memory_percent": 61.4, "memory_available_gb": 6.18048095703125, "disk_percent": 67.40652941964547, "disk_free_gb": 52.82782745361328, "database_count": 8, "total_db_size_mb": 1.24609375, "total_files": 61967, "total_size_mb": 560.4293460845947, "file_types": 58, "python_files": 87, "total_code_lines": 35950, "total_functions": 1151, "complex_functions": 29, "process_memory_mb": 21.75, "process_memory_percent": 0.13275146484375, "directory_sizes": {"core": 1.3557968139648438, ".qoder": 0.03575706481933594, "frontend": 544.0565299987793, ".pytest_cache": 0.022377967834472656, "tests": 1.155329704284668, ".claude": 0.027031898498535156, "backend": 0.02236461639404297, "__pycache__": 0.0022735595703125, "docs": 0.12032699584960938, "logs": 0.08143329620361328, "scripts": 0.4445371627807617, "api": 0.020715713500976562, ".kiro": 0.0, ".git": 5.953168869018555, ".vscode": 2.86102294921875e-06, "data": 1.3044328689575195}, "dependencies_count": 20}, "recommendations": [{"category": "disk_usage", "priority": "low", "title": "目录frontend占用空间较大", "description": "目录frontend占用544.1MB空间", "suggestions": ["检查是否有不必要的文件可以删除", "考虑压缩或归档旧文件", "将大文件移到外部存储", "定期清理临时文件"]}, {"category": "dependencies", "priority": "medium", "title": "依赖版本未固定", "description": "只有0/20个依赖固定了版本", "suggestions": ["使用==固定依赖包版本以确保一致性", "定期测试和更新依赖包", "使用requirements-dev.txt管理开发依赖", "考虑使用pipenv或poetry管理依赖"]}, {"category": "performance", "priority": "medium", "title": "性能优化建议", "description": "系统功能较多，建议进行性能优化", "suggestions": ["使用性能分析工具识别瓶颈", "实现智能缓存策略", "优化数据库查询", "使用异步处理提高并发性能"]}, {"category": "monitoring", "priority": "low", "title": "系统监控建议", "description": "建议添加更完善的系统监控", "suggestions": ["实现应用性能监控(APM)", "添加业务指标监控", "设置自动告警机制", "定期生成性能报告"]}, {"category": "security", "priority": "medium", "title": "安全加固建议", "description": "建议加强系统安全性", "suggestions": ["定期更新依赖包以修复安全漏洞", "实现输入验证和输出编码", "添加访问控制和身份验证", "定期进行安全审计"]}], "health_score": 85.0, "summary": {"total_recommendations": 5, "high_priority": 0, "medium_priority": 3, "low_priority": 2}}