#!/usr/bin/env python3
"""
批量语法修复工具
快速修复大量Python文件中的常见语法问题
"""

import re
from pathlib import Path
from typing import List, Dict
import ast

class MassSyntaxFixer:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.fixes_applied = 0
        self.files_processed = 0
        
    def fix_all_syntax_issues(self):
        """批量修复所有语法问题"""
        print("🔧 批量语法修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        
        for file_path in python_files:
            try:
                if self.fix_file_comprehensive(file_path):
                    self.fixes_applied += 1
                    print(f"✅ 修复: {file_path.relative_to(self.project_root)}")
                self.files_processed += 1
            except Exception as e:
                print(f"❌ 失败: {file_path.relative_to(self.project_root)} - {e}")
        
        print(f"\n🎉 批量修复完成!")
        print(f"📁 处理文件: {self.files_processed}")
        print(f"🔧 成功修复: {self.fixes_applied}")
        
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob("*.py"))
        
        # 添加根目录的Python文件
        python_files.extend(self.project_root.glob("*.py"))
        
        return python_files
    
    def fix_file_comprehensive(self, file_path: Path) -> bool:
        """综合修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用多轮修复
            content = self.apply_comprehensive_fixes(content)
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            return False
    
    def apply_comprehensive_fixes(self, content: str) -> str:
        """应用综合修复"""
        # 第一轮：基本缩进修复
        content = self.fix_basic_indentation(content)
        
        # 第二轮：语句块修复
        content = self.fix_statement_blocks(content)
        
        # 第三轮：特殊语法修复
        content = self.fix_special_syntax(content)
        
        return content
    
    def fix_basic_indentation(self, content: str) -> str:
        """修复基本缩进问题"""
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 修复函数定义缩进
            if re.match(r'^        def ', line):
                line = re.sub(r'^        def ', '    def ', line)
            
            # 修复类定义缩进
            elif re.match(r'^        class ', line):
                line = re.sub(r'^        class ', 'class ', line)
            
            # 修复except语句缩进
            elif re.match(r'^            except ', line):
                line = re.sub(r'^            except ', '    except ', line)
            
            # 修复else语句缩进
            elif re.match(r'^            else:', line):
                line = re.sub(r'^            else:', '    else:', line)
            
            # 修复elif语句缩进
            elif re.match(r'^            elif ', line):
                line = re.sub(r'^            elif ', '    elif ', line)
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def fix_statement_blocks(self, content: str) -> str:
        """修复语句块问题"""
        lines = content.split('\n')
        fixed_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 检查需要缩进块的语句
            if self.needs_indented_block(line):
                fixed_lines.append(line)
                
                # 检查下一行
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    expected_indent = self.get_expected_indent(line)
                    
                    # 如果下一行缩进不正确，添加pass
                    if (next_line.strip() and 
                        not next_line.startswith(' ' * expected_indent) and
                        not next_line.strip().startswith('#')):
                        fixed_lines.append(' ' * expected_indent + 'pass')
                
                i += 1
            else:
                fixed_lines.append(line)
                i += 1
        
        return '\n'.join(fixed_lines)
    
    def fix_special_syntax(self, content: str) -> str:
        """修复特殊语法问题"""
        # 修复孤立的except语句
        content = re.sub(
            r'\n(\s*)except ([^:]+):\n(?!\s*\1\s)',
            r'\n\1try:\n\1    pass\n\1except \2:\n\1    pass\n',
            content
        )
        
        # 修复孤立的else语句
        content = re.sub(
            r'\n(\s*)else:\n(?!\s*\1\s)',
            r'\n\1if True:\n\1    pass\n\1else:\n\1    pass\n',
            content
        )
        
        return content
    
    def needs_indented_block(self, line: str) -> bool:
        """检查是否需要缩进块"""
        stripped = line.strip()
        return bool(re.match(
            r'^(def|class|if|for|while|try|with|except|finally|else|elif)\s+.*:$',
            stripped
        ))
    
    def get_expected_indent(self, line: str) -> int:
        """获取期望的缩进级别"""
        current_indent = len(line) - len(line.lstrip())
        return current_indent + 4

def main():
    """主函数"""
    fixer = MassSyntaxFixer()
    fixer.fix_all_syntax_issues()

if __name__ == "__main__":
    main()