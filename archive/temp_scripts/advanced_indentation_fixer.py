#!/usr/bin/env python3
"""
高级缩进修复工具
自动检测和修复Python文件中的缩进问题
"""

import ast
import re
from pathlib import Path
from typing import List, Dict, Tuple
import sys

class AdvancedIndentationFixer:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.fixes_applied = 0
        self.files_processed = 0
        
    def fix_all_indentation_issues(self):
        """修复所有缩进问题"""
        print("🔧 高级缩进修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        
        for file_path in python_files:
            try:
                self.fix_file_indentation(file_path)
                self.files_processed += 1
            except Exception as e:
                print(f"❌ 修复文件失败 {file_path}: {e}")
        
        print(f"\n✅ 缩进修复完成!")
        print(f"📁 处理文件: {self.files_processed}")
        print(f"🔧 应用修复: {self.fixes_applied}")
        
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob("*.py"))
        
        # 添加根目录的Python文件
        python_files.extend(self.project_root.glob("*.py"))
        
        return python_files
    
    def fix_file_indentation(self, file_path: Path):
        """修复单个文件的缩进问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 检查是否有语法错误
            try:
                ast.parse(original_content)
                return  # 如果没有语法错误，跳过
            except SyntaxError as e:
                if "IndentationError" not in str(e) and "expected an indented block" not in str(e):
                    return  # 不是缩进错误，跳过
            
            print(f"🔧 修复缩进: {file_path.relative_to(self.project_root)}")
            
            # 修复缩进问题
            fixed_content = self.fix_indentation_content(original_content)
            
            # 验证修复后的代码
            try:
                ast.parse(fixed_content)
                
                # 保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                self.fixes_applied += 1
                print(f"   ✅ 修复成功")
                
            except SyntaxError as e:
                print(f"   ❌ 修复后仍有语法错误: {e}")
                
        except Exception as e:
            print(f"   ❌ 处理文件失败: {e}")
    
    def fix_indentation_content(self, content: str) -> str:
        """修复内容的缩进问题"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 检查函数定义的缩进问题
            if re.match(r'^        def ', line):
                # 8个空格的函数定义，应该是4个空格
                fixed_line = re.sub(r'^        def ', '    def ', line)
                fixed_lines.append(fixed_line)
                continue
            
            # 检查类方法定义的缩进问题
            if re.match(r'^            def ', line):
                # 12个空格的函数定义，应该是8个空格
                fixed_line = re.sub(r'^            def ', '        def ', line)
                fixed_lines.append(fixed_line)
                continue
            
            # 检查其他常见的缩进问题
            if re.match(r'^        """', line) and i > 0:
                # 检查前一行是否是函数定义
                prev_line = lines[i-1] if i > 0 else ""
                if re.match(r'^    def ', prev_line):
                    # 文档字符串应该是8个空格
                    fixed_line = re.sub(r'^        """', '        """', line)
                    fixed_lines.append(fixed_line)
                    continue
            
            # 修复其他缩进模式
            fixed_line = self.fix_line_indentation(line, i, lines)
            fixed_lines.append(fixed_line)
        
        return '\n'.join(fixed_lines)
    
    def fix_line_indentation(self, line: str, line_num: int, all_lines: List[str]) -> str:
        """修复单行的缩进问题"""
        # 如果是空行，保持原样
        if not line.strip():
            return line
        
        # 检查常见的缩进错误模式
        patterns = [
            # 8空格的def应该是4空格
            (r'^        def ', '    def '),
            # 8空格的class应该是0空格或4空格
            (r'^        class ', 'class '),
            # 12空格的内容应该是8空格
            (r'^            ([^d])', r'        \1'),
        ]
        
        for pattern, replacement in patterns:
            if re.match(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line

def main():
    """主函数"""
    fixer = AdvancedIndentationFixer()
    fixer.fix_all_indentation_issues()

if __name__ == "__main__":
    main()