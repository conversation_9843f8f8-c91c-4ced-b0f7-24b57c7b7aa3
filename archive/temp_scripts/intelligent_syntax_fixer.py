#!/usr/bin/env python3
"""
智能语法修复工具
自动检测和修复Python文件中的各种语法问题
"""

import ast
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import sys

class IntelligentSyntaxFixer:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.fixes_applied = 0
        self.files_processed = 0
        
    def fix_all_syntax_issues(self):
        """修复所有语法问题"""
        print("🔧 智能语法修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        
        for file_path in python_files:
            try:
                if self.fix_file_syntax(file_path):
                    self.fixes_applied += 1
                self.files_processed += 1
            except Exception as e:
                print(f"❌ 修复文件失败 {file_path}: {e}")
        
        print(f"\n✅ 语法修复完成!")
        print(f"📁 处理文件: {self.files_processed}")
        print(f"🔧 应用修复: {self.fixes_applied}")
        
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob("*.py"))
        
        # 添加根目录的Python文件
        python_files.extend(self.project_root.glob("*.py"))
        
        return python_files
    
    def fix_file_syntax(self, file_path: Path) -> bool:
        """修复单个文件的语法问题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 检查是否有语法错误
            try:
                ast.parse(original_content)
                return False  # 如果没有语法错误，跳过
            except SyntaxError as e:
                pass  # 有语法错误，继续修复
            
            print(f"🔧 修复语法: {file_path.relative_to(self.project_root)}")
            
            # 修复语法问题
            fixed_content = self.fix_syntax_content(original_content)
            
            # 验证修复后的代码
            try:
                ast.parse(fixed_content)
                
                # 保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print(f"   ✅ 修复成功")
                return True
                
            except SyntaxError as e:
                print(f"   ❌ 修复后仍有语法错误: {e}")
                return False
                
        except Exception as e:
            print(f"   ❌ 处理文件失败: {e}")
            return False
    
    def fix_syntax_content(self, content: str) -> str:
        """修复内容的语法问题"""
        lines = content.split('\n')
        fixed_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 修复函数定义后缺少缩进的问题
            if self.is_function_definition(line):
                fixed_line, skip_lines = self.fix_function_definition(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复类定义后缺少缩进的问题
            if self.is_class_definition(line):
                fixed_line, skip_lines = self.fix_class_definition(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复try语句后缺少缩进的问题
            if self.is_try_statement(line):
                fixed_line, skip_lines = self.fix_try_statement(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复if语句后缺少缩进的问题
            if self.is_if_statement(line):
                fixed_line, skip_lines = self.fix_if_statement(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复for/while语句后缺少缩进的问题
            if self.is_loop_statement(line):
                fixed_line, skip_lines = self.fix_loop_statement(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复with语句后缺少缩进的问题
            if self.is_with_statement(line):
                fixed_line, skip_lines = self.fix_with_statement(lines, i)
                fixed_lines.extend(fixed_line)
                i += skip_lines
                continue
            
            # 修复普通的缩进问题
            fixed_line = self.fix_line_indentation(line)
            fixed_lines.append(fixed_line)
            i += 1
        
        return '\n'.join(fixed_lines)
    
    def is_function_definition(self, line: str) -> bool:
        """检查是否是函数定义"""
        return bool(re.match(r'^\s*def\s+\w+\s*\(.*\)\s*:', line.strip()))
    
    def is_class_definition(self, line: str) -> bool:
        """检查是否是类定义"""
        return bool(re.match(r'^\s*class\s+\w+.*:', line.strip()))
    
    def is_try_statement(self, line: str) -> bool:
        """检查是否是try语句"""
        return bool(re.match(r'^\s*try\s*:', line.strip()))
    
    def is_if_statement(self, line: str) -> bool:
        """检查是否是if语句"""
        return bool(re.match(r'^\s*if\s+.*:', line.strip()))
    
    def is_loop_statement(self, line: str) -> bool:
        """检查是否是循环语句"""
        return bool(re.match(r'^\s*(for|while)\s+.*:', line.strip()))
    
    def is_with_statement(self, line: str) -> bool:
        """检查是否是with语句"""
        return bool(re.match(r'^\s*with\s+.*:', line.strip()))
    
    def fix_function_definition(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复函数定义后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            next_line = lines[i]
            
            # 如果下一行是文档字符串
            if '"""' in next_line or "'''" in next_line:
                if self.get_line_indent(next_line) != expected_indent:
                    fixed_line = ' ' * expected_indent + next_line.strip()
                    result.append(fixed_line)
                else:
                    result.append(next_line)
                i += 1
            
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def fix_class_definition(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复类定义后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def fix_try_statement(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复try语句后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def fix_if_statement(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复if语句后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def fix_loop_statement(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复循环语句后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def fix_with_statement(self, lines: List[str], start_idx: int) -> Tuple[List[str], int]:
        """修复with语句后的缩进问题"""
        result = [lines[start_idx]]
        current_indent = self.get_line_indent(lines[start_idx])
        expected_indent = current_indent + 4
        
        i = start_idx + 1
        
        # 检查下一行是否需要缩进
        if i < len(lines):
            # 如果下一行是空行或注释，跳过
            while i < len(lines) and (not lines[i].strip() or lines[i].strip().startswith('#')):
                result.append(lines[i])
                i += 1
            
            # 如果下一行没有正确缩进，添加pass语句
            if i < len(lines):
                next_line = lines[i]
                if self.get_line_indent(next_line) < expected_indent and next_line.strip():
                    result.append(' ' * expected_indent + 'pass')
        
        return result, i - start_idx - 1
    
    def get_line_indent(self, line: str) -> int:
        """获取行的缩进级别"""
        return len(line) - len(line.lstrip())
    
    def fix_line_indentation(self, line: str) -> str:
        """修复单行的缩进问题"""
        # 如果是空行，保持原样
        if not line.strip():
            return line
        
        # 修复常见的缩进错误模式
        patterns = [
            # 8空格的def应该是4空格
            (r'^        def ', '    def '),
            # 8空格的class应该是0空格
            (r'^        class ', 'class '),
        ]
        
        for pattern, replacement in patterns:
            if re.match(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line

def main():
    """主函数"""
    fixer = IntelligentSyntaxFixer()
    fixer.fix_all_syntax_issues()

if __name__ == "__main__":
    main()