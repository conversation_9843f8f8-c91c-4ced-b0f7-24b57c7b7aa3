#!/usr/bin/env python3
"""
简单语法修复工具
快速修复常见的Python语法问题
"""

import re
from pathlib import Path
from typing import List

class SimpleSyntaxFixer:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.fixes_applied = 0
        
    def fix_all_files(self):
        """修复所有文件"""
        print("🔧 简单语法修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        
        for file_path in python_files:
            try:
                if self.fix_file(file_path):
                    self.fixes_applied += 1
                    print(f"✅ 修复: {file_path.relative_to(self.project_root)}")
            except Exception as e:
                print(f"❌ 失败: {file_path.relative_to(self.project_root)} - {e}")
        
        print(f"\n🎉 修复完成! 共修复 {self.fixes_applied} 个文件")
        
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob("*.py"))
        
        # 添加根目录的Python文件
        python_files.extend(self.project_root.glob("*.py"))
        
        return python_files
    
    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用修复规则
            content = self.apply_fixes(content)
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"处理文件错误: {e}")
            return False
    
    def apply_fixes(self, content: str) -> str:
        """应用修复规则"""
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 修复函数定义的缩进
            if re.match(r'^        def ', line):
                fixed_line = re.sub(r'^        def ', '    def ', line)
                fixed_lines.append(fixed_line)
                continue
            
            # 修复类定义的缩进
            if re.match(r'^        class ', line):
                fixed_line = re.sub(r'^        class ', 'class ', line)
                fixed_lines.append(fixed_line)
                continue
            
            # 修复文档字符串的缩进
            if re.match(r'^        """', line) and i > 0:
                prev_line = lines[i-1] if i > 0 else ""
                if re.match(r'^    def ', prev_line):
                    fixed_line = re.sub(r'^        """', '        """', line)
                    fixed_lines.append(fixed_line)
                    continue
            
            # 添加缺失的pass语句
            if i > 0:
                prev_line = lines[i-1]
                # 检查前一行是否是需要缩进的语句
                if (re.match(r'^\s*(def|class|if|for|while|try|with|except|finally|else|elif)\s+.*:', prev_line.strip()) and
                    line.strip() and not line.startswith(' ') and not line.strip().startswith('#')):
                    # 添加pass语句
                    indent = len(prev_line) - len(prev_line.lstrip()) + 4
                    fixed_lines.append(' ' * indent + 'pass')
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

def main():
    """主函数"""
    fixer = SimpleSyntaxFixer()
    fixer.fix_all_files()

if __name__ == "__main__":
    main()