#!/usr/bin/env python3
"""
语法修复工具
自动修复Python语法错误
"""
import sys
import ast
import re
from pathlib import Path
from typing import List, Dict, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class SyntaxFixer:
    """语法修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.fixes_applied = 0
        self.files_processed = 0
        
    def fix_all_syntax_errors(self):
        """修复所有语法错误"""
        print("🔧 语法修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        print(f"检查 {len(python_files)} 个Python文件")
        
        # 修复每个文件
        for file_path in python_files:
            self.fix_file_syntax(file_path)
        
        print(f"\n✅ 语法修复完成!")
        print(f"处理文件: {self.files_processed}")
        print(f"应用修复: {self.fixes_applied}")
        
        return self.fixes_applied
    
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob('*.py'))
        
        # 排除某些文件
        exclude_patterns = ['__pycache__', '.git', 'venv', 'env']
        
        filtered_files = []
        for file_path in python_files:
            if not any(pattern in str(file_path) for pattern in exclude_patterns):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def fix_file_syntax(self, file_path: Path):
        """修复单个文件的语法错误"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 首先尝试解析，看是否有语法错误
            try:
                ast.parse(original_content)
                # 没有语法错误，跳过
                self.files_processed += 1
                return
                except SyntaxError as e:
                # 有语法错误，尝试修复
                fixed_content = self.fix_syntax_error(original_content, e)
                
                if fixed_content != original_content:
                    # 验证修复后的代码
                    try:
                        ast.parse(fixed_content)
                        # 修复成功，写回文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(fixed_content)
                        
                        self.fixes_applied += 1
                        print(f"   ✅ {file_path.relative_to(self.project_root)} - 修复语法错误")
                        
                        except SyntaxError:
                        print(f"   ⚠️ {file_path.relative_to(self.project_root)} - 修复失败")
                        else:
                    print(f"   ❓ {file_path.relative_to(self.project_root)} - 无法自动修复")
            
            self.files_processed += 1
            
            except Exception as e:
            print(f"   ❌ 处理文件失败 {file_path.relative_to(self.project_root)}: {e}")
    
            def fix_syntax_error(self, content: str, error: SyntaxError) -> str:
        """修复特定的语法错误"""
        lines = content.split('\n')
        
        if error.lineno is None:
            return content
        
        error_line_idx = error.lineno - 1
        
        if error_line_idx >= len(lines):
            return content
        
        error_line = lines[error_line_idx]
        
        # 常见的语法错误修复
        fixed_line = self.apply_common_fixes(error_line, error)
        
        if fixed_line != error_line:
            lines[error_line_idx] = fixed_line
            return '\n'.join(lines)
        
        return content
    
    def apply_common_fixes(self, line: str, error: SyntaxError) -> str:
        """应用常见的语法修复"""
        original_line = line
        
        # 1. 修复缩进问题
        if "unindent does not match any outer indentation level" in str(error):
            # 尝试修复缩进
            stripped = line.lstrip()
            if stripped:
                # 使用4个空格的标准缩进
                indent_level = (len(line) - len(stripped)) // 4
                line = '    ' * indent_level + stripped
        
        # 2. 修复意外的缩进
        elif "unexpected indent" in str(error):
            line = line.lstrip()
        
        # 3. 修复缺少冒号
        elif "invalid syntax" in str(error) and any(keyword in line for keyword in ['if ', 'for ', 'while ', 'def ', 'class ', 'try:', 'except', 'else', 'elif']):
            if not line.rstrip().endswith(':'):
                line = line.rstrip() + ':'
        
        # 4. 修复缺少缩进块
        elif "expected an indented block" in str(error):
            # 这个需要在下一行添加pass语句
            pass  # 这个在调用者处理
        
        # 5. 修复括号不匹配
        elif "closing parenthesis" in str(error) or "unmatched" in str(error):
            line = self.fix_bracket_mismatch(line)
        
        # 6. 修复字符串问题
        elif "EOL while scanning string literal" in str(error):
            line = self.fix_string_literal(line)
        
        return line
    
    def fix_bracket_mismatch(self, line: str) -> str:
        """修复括号不匹配"""
        # 简单的括号平衡检查
        open_parens = line.count('(')
        close_parens = line.count(')')
        open_brackets = line.count('[')
        close_brackets = line.count(']')
        open_braces = line.count('{')
        close_braces = line.count('}')
        
        # 如果缺少闭合括号，添加它们
        if open_parens > close_parens:
            line += ')' * (open_parens - close_parens)
            elif close_parens > open_parens:
            line = '(' * (close_parens - open_parens) + line
        
            if open_brackets > close_brackets:
            line += ']' * (open_brackets - close_brackets)
            elif close_brackets > open_brackets:
            line = '[' * (close_brackets - open_brackets) + line
        
            if open_braces > close_braces:
            line += '}' * (open_braces - close_braces)
            elif close_braces > open_braces:
            line = '{' * (close_braces - open_braces) + line
        
        return line
    
    def fix_string_literal(self, line: str) -> str:
        """修复字符串字面量问题"""
        # 检查未闭合的字符串
        single_quotes = line.count("'")
        double_quotes = line.count('"')
        
        # 如果单引号数量是奇数，添加一个单引号
        if single_quotes % 2 == 1:
            line += "'"
        
        # 如果双引号数量是奇数，添加一个双引号
        if double_quotes % 2 == 1:
            line += '"'
        
        return line

class IndentationFixer:
    """缩进修复器"""
    
    def __init__(self):
        self.project_root = project_root
        
    def fix_indentation_errors(self):
        """修复缩进错误"""
        print("\n🔧 修复缩进错误")
        print("=" * 30)
        
        python_files = self.get_python_files()
        fixes_applied = 0
        
        for file_path in python_files:
            if self.fix_file_indentation(file_path):
                fixes_applied += 1
        
        print(f"✅ 修复了 {fixes_applied} 个文件的缩进问题")
        return fixes_applied
    
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob('*.py'))
        
        exclude_patterns = ['__pycache__', '.git', 'venv', 'env']
        
        filtered_files = []
        for file_path in python_files:
            if not any(pattern in str(file_path) for pattern in exclude_patterns):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def fix_file_indentation(self, file_path: Path) -> bool:
        """修复单个文件的缩进"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixed_lines = []
            indent_stack = [0]  # 缩进级别栈
            fixed = False
            
            for i, line in enumerate(lines):
                if not line.strip():  # 空行
                    fixed_lines.append(line)
                    continue
                
                # 计算当前行的缩进
                stripped = line.lstrip()
                current_indent = len(line) - len(stripped)
                
                # 检查是否需要缩进的语句
                if stripped.rstrip().endswith(':'):
                    # 这行应该增加缩进级别
                    expected_indent = indent_stack[-1]
                    if current_indent != expected_indent:
                        line = ' ' * expected_indent + stripped
                        fixed = True
                    
                    # 下一行应该缩进
                    indent_stack.append(expected_indent + 4)
                
                    elif current_indent < indent_stack[-1]:
                    # 缩进减少，弹出栈
                    while len(indent_stack) > 1 and current_indent < indent_stack[-1]:
                        indent_stack.pop()
                    
                    expected_indent = indent_stack[-1]
                    if current_indent != expected_indent:
                        line = ' ' * expected_indent + stripped
                        fixed = True
                
                        elif current_indent > indent_stack[-1]:
                    # 缩进增加，但没有冒号，可能是错误
                    expected_indent = indent_stack[-1]
                    if current_indent != expected_indent and current_indent != expected_indent + 4:
                        line = ' ' * expected_indent + stripped
                        fixed = True
                
                fixed_lines.append(line)
            
                if fixed:
                    with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(fixed_lines)
                
                print(f"   ✅ {file_path.relative_to(self.project_root)} - 修复缩进")
                return True
            
            return False
            
            except Exception as e:
            print(f"   ❌ 处理文件失败 {file_path}: {e}")
            return False

            def main():
    """主函数"""
    print("🔧 语法和缩进修复工具")
    print("=" * 50)
    
    try:
        # 1. 修复语法错误
        syntax_fixer = SyntaxFixer()
        syntax_fixes = syntax_fixer.fix_all_syntax_errors()
        
        # 2. 修复缩进错误
        indent_fixer = IndentationFixer()
        indent_fixes = indent_fixer.fix_indentation_errors()
        
        print("\n" + "=" * 50)
        print("📊 修复总结")
        print("=" * 50)
        print(f"语法修复: {syntax_fixes}")
        print(f"缩进修复: {indent_fixes}")
        print(f"总修复数: {syntax_fixes + indent_fixes}")
        
        if syntax_fixes + indent_fixes > 0:
            print("\n✅ 语法修复完成！")
            return True
            else:
            print("\n✨ 语法已经正确，无需修复！")
            return True
            
            except Exception as e:
        print(f"\n💥 语法修复异常: {e}")
        return False

        if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)