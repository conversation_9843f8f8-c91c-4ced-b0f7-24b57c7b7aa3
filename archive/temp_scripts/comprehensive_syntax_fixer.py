#!/usr/bin/env python3
"""
综合语法修复工具
修复Python文件中的各种语法和缩进问题
"""

import re
from pathlib import Path
from typing import List, Tuple

class ComprehensiveSyntaxFixer:
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.fixes_applied = 0
        
    def fix_all_files(self):
        """修复所有文件"""
        print("🔧 综合语法修复工具")
        print("=" * 50)
        
        # 获取所有Python文件
        python_files = self.get_python_files()
        
        for file_path in python_files:
            try:
                if self.fix_file(file_path):
                    self.fixes_applied += 1
                    print(f"✅ 修复: {file_path.relative_to(self.project_root)}")
            except Exception as e:
                print(f"❌ 失败: {file_path.relative_to(self.project_root)} - {e}")
        
        print(f"\n🎉 修复完成! 共修复 {self.fixes_applied} 个文件")
        
    def get_python_files(self) -> List[Path]:
        """获取所有Python文件"""
        python_files = []
        
        # 要检查的目录
        check_dirs = ['core', 'backend', 'scripts', 'tests']
        
        for dir_name in check_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                python_files.extend(dir_path.rglob("*.py"))
        
        # 添加根目录的Python文件
        python_files.extend(self.project_root.glob("*.py"))
        
        return python_files
    
    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用修复规则
            content = self.comprehensive_fix(content)
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"处理文件错误: {e}")
            return False
    
    def comprehensive_fix(self, content: str) -> str:
        """综合修复内容"""
        lines = content.split('\\n')
        fixed_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # 修复try-except块的问题
            if self.is_try_block_issue(lines, i):
                fixed_block, skip = self.fix_try_block(lines, i)
                fixed_lines.extend(fixed_block)
                i += skip
                continue
            
            # 修复函数定义问题
            if self.is_function_issue(lines, i):
                fixed_block, skip = self.fix_function_block(lines, i)
                fixed_lines.extend(fixed_block)
                i += skip
                continue
            
            # 修复类定义问题
            if self.is_class_issue(lines, i):
                fixed_block, skip = self.fix_class_block(lines, i)
                fixed_lines.extend(fixed_block)
                i += skip
                continue
            
            # 修复普通缩进问题
            fixed_line = self.fix_line_indentation(line)
            fixed_lines.append(fixed_line)
            i += 1
        
        return '\\n'.join(fixed_lines)
    
    def is_try_block_issue(self, lines: List[str], index: int) -> bool:
        """检查是否是try块问题"""
        if index >= len(lines):
            return False
        
        line = lines[index].strip()
        
        # 检查孤立的except语句
        if line.startswith('except ') and index > 0:
            prev_lines = [lines[j].strip() for j in range(max(0, index-5), index)]
            if not any('try:' in prev_line for prev_line in prev_lines):
                return True
        
        return False
    
    def is_function_issue(self, lines: List[str], index: int) -> bool:
        """检查是否是函数定义问题"""
        if index >= len(lines):
            return False
        
        line = lines[index]
        return bool(re.match(r'^        def ', line))
    
    def is_class_issue(self, lines: List[str], index: int) -> bool:
        """检查是否是类定义问题"""
        if index >= len(lines):
            return False
        
        line = lines[index]
        return bool(re.match(r'^        class ', line))
    
    def fix_try_block(self, lines: List[str], index: int) -> Tuple[List[str], int]:
        """修复try块"""
        result = []
        current_line = lines[index]
        
        # 如果是孤立的except，添加try块
        if current_line.strip().startswith('except '):
            indent = len(current_line) - len(current_line.lstrip())
            result.append(' ' * indent + 'try:')
            result.append(' ' * (indent + 4) + 'pass')
            result.append(current_line)
            return result, 1
        
        result.append(current_line)
        return result, 1
    
    def fix_function_block(self, lines: List[str], index: int) -> Tuple[List[str], int]:
        """修复函数定义块"""
        result = []
        current_line = lines[index]
        
        # 修复函数定义的缩进
        if re.match(r'^        def ', current_line):
            fixed_line = re.sub(r'^        def ', '    def ', current_line)
            result.append(fixed_line)
            
            # 检查下一行是否需要添加pass
            if index + 1 < len(lines):
                next_line = lines[index + 1]
                if not next_line.strip() or not next_line.startswith('        '):
                    result.append('        pass')
            
            return result, 1
        
        result.append(current_line)
        return result, 1
    
    def fix_class_block(self, lines: List[str], index: int) -> Tuple[List[str], int]:
        """修复类定义块"""
        result = []
        current_line = lines[index]
        
        # 修复类定义的缩进
        if re.match(r'^        class ', current_line):
            fixed_line = re.sub(r'^        class ', 'class ', current_line)
            result.append(fixed_line)
            
            # 检查下一行是否需要添加pass
            if index + 1 < len(lines):
                next_line = lines[index + 1]
                if not next_line.strip() or not next_line.startswith('    '):
                    result.append('    pass')
            
            return result, 1
        
        result.append(current_line)
        return result, 1
    
    def fix_line_indentation(self, line: str) -> str:
        """修复单行缩进"""
        if not line.strip():
            return line
        
        # 修复常见的缩进问题
        fixes = [
            # 修复函数定义缩进
            (r'^        def ', '    def '),
            # 修复类定义缩进
            (r'^        class ', 'class '),
            # 修复except语句缩进
            (r'^                except ', '            except '),
            # 修复其他常见缩进问题
            (r'^            ([a-zA-Z_])', r'        \\1'),
        ]
        
        for pattern, replacement in fixes:
            if re.match(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line

def main():
    """主函数"""
    fixer = ComprehensiveSyntaxFixer()
    fixer.fix_all_files()

if __name__ == "__main__":
    main()