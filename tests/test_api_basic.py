"""
API基础测试
"""
import unittest
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from fastapi.testclient import TestClient
    from backend.main import app
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False


@unittest.skipUnless(FASTAPI_AVAILABLE, "FastAPI not available")
class TestAPI(unittest.TestCase):
    """API测试"""

    def setUp(self):
        """设置测试环境"""
        self.client = TestClient(app)

    def test_root_endpoint(self):
        """测试根端点"""
        response = self.client.get("/")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("message", data)
        self.assertIn("version", data)
        self.assertIn("status", data)

    def test_health_endpoint(self):
        """测试健康检查端点"""
        response = self.client.get("/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("status", data)

    def test_api_v1_health(self):
        """测试API v1健康检查"""
        response = self.client.get("/api/v1/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("status", data)

    def test_system_info(self):
        """测试系统信息端点"""
        response = self.client.get("/api/v1/system/info")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("status", data)
        self.assertIn("version", data)

    def test_strategies_list(self):
        """测试策略列表端点"""
        response = self.client.get("/api/v1/strategies")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsInstance(data, list)

    def test_market_data(self):
        """测试市场数据端点"""
        response = self.client.get("/api/v1/data/AAPL")
        # 可能返回200或其他状态码，取决于数据源
        self.assertIn(response.status_code, [200, 404, 500])

    def test_backtest_endpoint(self):
        """测试回测端点"""
        backtest_data = {
            "strategy": "moving_average",
            "symbol": "AAPL",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "initial_capital": 100000
        }
        response = self.client.post("/api/v1/backtest", json=backtest_data)
        # 可能返回200或其他状态码，取决于策略和数据
        self.assertIn(response.status_code, [200, 400, 404, 500])

    def test_docs_endpoint(self):
        """测试API文档端点"""
        response = self.client.get("/docs")
        self.assertEqual(response.status_code, 200)

    def test_openapi_schema(self):
        """测试OpenAPI schema"""
        response = self.client.get("/openapi.json")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("openapi", data)
        self.assertIn("info", data)


class TestAPIWithoutFastAPI(unittest.TestCase):
    """当FastAPI不可用时的测试"""

    @unittest.skipIf(FASTAPI_AVAILABLE, "FastAPI is available")
    def test_fastapi_not_available(self):
        """测试FastAPI不可用的情况"""
        self.assertFalse(FASTAPI_AVAILABLE)
        print("FastAPI not available, skipping API tests")


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
