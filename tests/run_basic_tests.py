#!/usr/bin/env python3
"""
基础测试运行器
"""
import unittest
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_tests():
    """运行所有基础测试"""
    print("🧪 运行基础测试套件")
    print("=" * 50)

    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # 添加测试模块
    test_modules = [
        'test_core_basic',
        'test_api_basic'
    ]

    for module_name in test_modules:
        try:
            module = __import__(module_name)
            suite.addTests(loader.loadTestsFromModule(module))
            print(f"✅ 加载测试模块: {module_name}")
        except ImportError as e:
            print(f"⚠️  跳过测试模块 {module_name}: {e}")
        except Exception as e:
            print(f"❌ 加载测试模块失败 {module_name}: {e}")

    # 运行测试
    print("\n" + "=" * 50)
    print("🚀 开始运行测试...")
    print("=" * 50)

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果摘要
    print("\n" + "=" * 50)
    print("📊 测试结果摘要")
    print("=" * 50)
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped)}")

    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")

    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")

    if result.skipped:
        print("\n⏭️  跳过的测试:")
        for test, reason in result.skipped:
            print(f"  - {test}: {reason}")

    # 总体结果
    if result.wasSuccessful():
        print("\n🎉 所有测试通过!")
        return True
    else:
        print(f"\n❌ 测试失败 ({len(result.failures + result.errors)} 个问题)")
        return False


def main():
    """主函数"""
    try:
        success = run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试运行器出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
